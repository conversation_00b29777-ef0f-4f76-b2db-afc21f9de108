package com.ously.gamble.cpopups.persistence.model;

import com.ously.gamble.cpopups.persistence.dto.PopupsFullDto;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@SuppressWarnings("unused")


@NamedNativeQuery(name = "PopupsArchived.getAllArchivedUserPopups",
        query = """
                select               pu.user_id as userId,
                                     pu.id as id,
                                     pu.sender as sender,
                                     pu.created_at as createdAt,
                                     pu.content as content,
                                     pu.result as result,
                                     pu.archived_at as archivedAt
                                from popups_archive pu
                                     where pu.user_id = ?1
                """,
        resultSetMapping = "Mapping.PopupsFullDto")

@SqlResultSetMapping(name = "Mapping.PopupsFullDto",
        classes = @ConstructorResult(targetClass = PopupsFullDto.class,
                columns = {
                        @ColumnResult(name = "userId",
                                type = long.class),
                        @ColumnResult(name = "id",
                                type = int.class),
                        @ColumnResult(name = "sender",
                                type = String.class),
                        @ColumnResult(name = "createdAt",
                                type = Instant.class),
                        @ColumnResult(name = "content",
                                type = String.class),
                        @ColumnResult(name = "result",
                                type = int.class),
                        @ColumnResult(name = "archivedAt",
                                type = Instant.class)

                }
        )
)

@Entity
@Table(name = "popups_archive")
@EntityListeners(AuditingEntityListener.class)
@IdClass(PopupsId.class)
public class PopupsArchived implements Persistable<PopupsId> {


    @Id
    @Column(name = "user_id", nullable = false, updatable = false)
    private long userId;

    @Id
    @Column(name = "id", nullable = false, updatable = false)
    private int puId;

    @Column(name = "sender", nullable = false, updatable = false)
    private String sender;

    @Column(name = "archived_at", nullable = false, updatable = false)
    private Instant archivedAt;

    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "content", nullable = false, updatable = false)
    private String content;

    @Column(name = "result", nullable = false)
    private int result;


    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    @Override
    public PopupsId getId() {
        return new PopupsId(userId, puId);
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getPuId() {
        return puId;
    }

    public void setPuId(int puId) {
        this.puId = puId;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Instant getArchivedAt() {
        return archivedAt;
    }

    public void setArchivedAt(Instant archivedAt) {
        this.archivedAt = archivedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public boolean isWasLoaded() {
        return wasLoaded;
    }

    public void setWasLoaded(boolean wasLoaded) {
        this.wasLoaded = wasLoaded;
    }
}
