package com.ously.gamble.cpopups.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.clouddb.CloudDbDeleteRequest;
import com.ously.gamble.api.clouddb.CloudDbEntryRequest;
import com.ously.gamble.api.consumable.CustomConsumableRequest;
import com.ously.gamble.api.consumable.UserConsumableAdminService;
import com.ously.gamble.api.cpopups.CPopupService;
import com.ously.gamble.api.cpopups.CUserPopup;
import com.ously.gamble.api.events.FcmPushEvent;
import com.ously.gamble.api.videoads.CrmVideoAdRequest;
import com.ously.gamble.api.videoads.VideoAdResponse;
import com.ously.gamble.api.videoads.VideoAdsService;
import com.ously.gamble.api.webhook.WebhookAddPopupsRequest;
import com.ously.gamble.conditions.ConditionalOnNotBridge;
import com.ously.gamble.conditions.ConditionalOnNotBridgeRouter;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.cpopups.config.CPopupsConfiguration;
import com.ously.gamble.cpopups.persistence.model.Popups;
import com.ously.gamble.cpopups.persistence.model.PopupsId;
import com.ously.gamble.cpopups.persistence.repository.PopupsArchiveRepository;
import com.ously.gamble.cpopups.persistence.repository.PopupsRepository;
import com.ously.gamble.exception.ApiError;
import com.ously.gamble.payload.TxPrice;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
@ConditionalOnProperty(prefix = "cpopups", name = "enabled", havingValue = "true")
@ConditionalOnNotBridge
@ConditionalOnNotBridgeRouter
@ConditionalOnNotMonitor
public class CPopupsServiceImpl implements CPopupService {

    private static final String STR_BUTTON_KEY = "buttons";
    private static final String STR_ACTION_KEY = "action";
    private static final String STR_ACTION_REWARD_KEY = "action_reward";
    private static final String STR_REWARD_KEY = "reward";

    private static final Logger log = LoggerFactory.getLogger(CPopupsServiceImpl.class);
    private final PopupsRepository popupRepository;
    private final CPopupsConfiguration configuration;
    private final PopupsArchiveRepository paARepo;

    private final UserConsumableAdminService ucaS;

    private final ObjectMapper om;
    private final ApplicationEventPublisher eventPublisher;

    private final VideoAdsService videoAdsService;

    @Autowired
    public CPopupsServiceImpl(PopupsRepository puRepo,
                              PopupsArchiveRepository paARepo,
                              CPopupsConfiguration cpConf,
                              ObjectMapper om,
                              ApplicationEventPublisher eventPublisher,
                              UserConsumableAdminService ucaSrv,
                              @Autowired(required = false) VideoAdsService videoAdsService) {
        this.popupRepository = puRepo;
        this.paARepo = paARepo;
        this.configuration = cpConf;
        this.om = om;
        this.eventPublisher = eventPublisher;
        this.ucaS = ucaSrv;
        this.videoAdsService = videoAdsService;
    }


    @Override
    public void expirePopups() {
        popupRepository.archiveExpiredPopups();
    }

    @Override
    public void purgeArchivedPopups() {
        if (paARepo != null) {
            Instant deleteBeforeTimestamp = Instant.now().minusSeconds(configuration.getArchiveTTL().toSeconds());
            paARepo.purgeOutdatedPopups(deleteBeforeTimestamp);
        }
    }


    @Override
    @Transactional
    public Optional<ApiError> createPopup(long userId, String sender, Duration ttl, String content) {

        if (!checkContent(content)) {
            return Optional.of(new ApiError(1701, "Content is not a valid json"));
        }

        int nextIdForUser = popupRepository.findNextIdForUser(userId);
        Instant createdAt = Instant.now();
        Instant expiresAt = createdAt.plusMillis(ttl.toMillis());

        Popups nPu = new Popups();
        nPu.setUserId(userId);
        nPu.setPuId(nextIdForUser);
        nPu.setSender(sender);
        nPu.setCreatedAt(createdAt);
        nPu.setExpiresAt(expiresAt);
        nPu.setResult(0);
        nPu.setContent(content);

        popupRepository.save(nPu);
        return Optional.empty();

    }

    boolean checkContent(String content) {
        return checkContentS(content, om);
    }

    static boolean checkContentS(String content, ObjectMapper om) {
        try {
            List<String> rewStrings = getRewardsFromJsonTree(om.readTree(content));
            for (var pdef : rewStrings) {
                TxPrice.parsePriceDef(pdef);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    private static List<String> getRewardsFromJsonTree(JsonNode jsonNode) {
        List<String> rews = new ArrayList<>(1);
        if (jsonNode.has(STR_BUTTON_KEY)) {
            JsonNode jsonNode1 = jsonNode.get(STR_BUTTON_KEY);
            if (jsonNode1.isArray()) {
                jsonNode1.forEach(a -> {
                    if (a.has(STR_ACTION_KEY) && a.has(STR_ACTION_REWARD_KEY)) {
                        JsonNode jsonNode2 = a.get(STR_ACTION_KEY);
                        if (STR_REWARD_KEY.equalsIgnoreCase(jsonNode2.textValue())) {
                            JsonNode rewString = a.get(STR_ACTION_REWARD_KEY);
                            rews.add(rewString.textValue());
                        }
                    }
                });
            }
        }
        return rews;
    }

    static boolean checkContent(Map<String, Object> content) {
        Object bts = content.get(STR_BUTTON_KEY);
        if (!(bts instanceof List)) {
            return true;
        }
        @SuppressWarnings("unchecked") List<Map<String, Object>> btList = (List<Map<String, Object>>) bts;
        for (var bt : btList) {
            if (bt.containsKey(STR_ACTION_KEY) && bt.containsKey(STR_ACTION_REWARD_KEY)) {
                Object obj = bt.get(STR_ACTION_KEY);
                if (obj == null || !STR_REWARD_KEY.equalsIgnoreCase(obj.toString())) {
                    continue;
                }
                Object obj2 = bt.get(STR_ACTION_REWARD_KEY);
                if (obj2 == null) {
                    continue;
                }
                try {
                    TxPrice.parsePriceDef(obj2.toString());
                } catch (Exception e) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public List<CUserPopup> getPopups(long userId) {
        return popupRepository.getAllCUserPopups(userId);
    }

    @Override
    @Transactional
    public List<CUserPopup> markPopupResult(long userId, int puId, int result) {
        markAndArchive(userId, puId, result);
        return popupRepository.getAllCUserPopups(userId);
    }

    @Override
    public void createPopupFromWebhook(WebhookAddPopupsRequest whpur) {
        if (!checkContent(whpur.getData())) {
            log.warn("Webhook request for cpopup is not valid:{}", whpur);
            return;
        }

        long userId = Long.parseLong(whpur.getUserId());

        Optional<String> firebaseUIDFromUserId = popupRepository.getFirebaseUIDFromUserId(userId);
        if (firebaseUIDFromUserId.isEmpty()) {
            log.warn("Cannot find local_id for user {}", userId);
            return;
        }

        // Process expiration time
        Instant createdAt = Instant.now();
        Instant expiresAt = createdAt.plus(1, ChronoUnit.HOURS);
        if (StringUtils.isNotEmpty(whpur.getTtl())) {
            Duration parsedDuration = Duration.parse(whpur.getTtl());
            expiresAt = createdAt.plusSeconds(parsedDuration.toSeconds());
        }

        int nextIdForUser = popupRepository.findNextIdForUser(userId);
        Map<String, Object> dataMap = new HashMap<>(whpur.getData());

        // Process special offers first (may modify whpur.getData())
        checkForCustomOffers(whpur);

        // Process video ads (added feature)
        checkForVideoAds(whpur, userId, dataMap);

        // Add expiration time to data sent to client
        dataMap.put("ttl", expiresAt);

        // Convert to JSON for database storage
        String data;
        try {
            data = om.writeValueAsString(dataMap);
        } catch (JsonProcessingException e) {
            log.warn("Cannot convert data map to json:{}", dataMap, e);
            return;
        }

        // Save popup to database
        Popups nPu = new Popups();
        nPu.setUserId(userId);
        nPu.setSender(whpur.getSender());
        nPu.setPuId(nextIdForUser);
        nPu.setCreatedAt(createdAt);
        nPu.setExpiresAt(expiresAt);
        nPu.setResult(0);
        nPu.setContent(data);
        popupRepository.save(nPu);

        // Send to Firestore
        String uid = firebaseUIDFromUserId.get();
        String collectionName = "popups";
        String id = Integer.toString(nextIdForUser);
        eventPublisher.publishEvent(new CloudDbEntryRequest("users/{ENV}/" + uid + "/cols/" + collectionName + "/" + id, dataMap));

        // Process push notification if present
        if (whpur.getData().containsKey("push")) {
            Object push = whpur.getData().get("push");
            if (push instanceof Map oMap) {
                Map<String, String> parameters = new HashMap<>();
                Object title = oMap.get("title");
                Object body = oMap.get("body");
                if (title != null && body != null) {
                    parameters.put("title", title.toString());
                    parameters.put("body", body.toString());
                    parameters.put("popupid", Integer.toString(nextIdForUser));
                    eventPublisher.publishEvent(new FcmPushEvent(userId, "popup", parameters));
                }
            }
        }
    }

    private void checkForVideoAds(WebhookAddPopupsRequest whpur, long userId, Map<String, Object> dataMap) {

        if (videoAdsService == null) {
            return;
        }

        String type = whpur.getData().getOrDefault("type", "").toString();
        if (!"video_ad".equals(type)) {
            return;
        }

        Object coinAmountObj = whpur.getData().get("coin_amount");
        Double coinAmount = null;
        if (coinAmountObj instanceof Number) {
            coinAmount = ((Number) coinAmountObj).doubleValue();
        } else if (coinAmountObj instanceof String) {
            try {
                coinAmount = Double.parseDouble((String) coinAmountObj);
            } catch (NumberFormatException ignored) {
            }
        }

        if (coinAmount == null || coinAmount <= 0) {
            log.warn("Video ad webhook missing or invalid coin_amount: {}", coinAmountObj);
            return;
        }

        CrmVideoAdRequest crmRequest = new CrmVideoAdRequest(
                userId,
                "crm_video_ad",
                coinAmount,
                "crm_popup"
        );

        VideoAdResponse response = videoAdsService.prepare(crmRequest);

        if (response.uuid() != null) {
            dataMap.put("video_ad_uuid", response.uuid().toString());
            dataMap.put("video_ad_coin_amount", coinAmount);
            log.info("Prepared video ad {} for user {} with coin amount {}", response.uuid(), userId, coinAmount);
        } else {
            log.warn("Video ad preparation limit reached or failed for user {}", userId);
        }
    }

    private void checkForCustomOffers(WebhookAddPopupsRequest whpur) {
        String type = whpur.getData().getOrDefault("type", "unknown").toString();
        if (!"special_offer".equals(type)) {
            return;
        }

        String cId = whpur.getData().getOrDefault("consumable_id", "-1").toString();
        String cMlt = whpur.getData().getOrDefault("multiplier", "100").toString();

        String campaign = whpur.getData().getOrDefault("campaign_id", "unspecified").toString();

        var ccReq = new CustomConsumableRequest(Long.parseLong(whpur.getUserId()), Long.parseLong(cId), whpur.getTtl(),
                Integer.parseInt(cMlt), 100,
                whpur.getSender(), campaign, "", "", "", false);

        var uc = ucaS.addUserConsumable(ccReq);
        if (uc != null) {
            whpur.getData().put("custom_consumable_id", uc.getCustomId());
        }
    }

    // Removed createJsonFromMap method as it's no longer needed

    private void markAndArchive(long userId, int puId, int result) {

        Optional<Popups> byId = popupRepository.findById(new PopupsId(userId, puId));
        if (byId.isEmpty()) {
            return;
        }

        Popups pu = byId.get();
        try {
            checkContentForRewards(pu, result);
        } catch (JsonProcessingException e) {
            log.error("Error handling marking popup", e);
            return;
        }

        popupRepository.markResult(userId, puId, result);
        popupRepository.deleteById(new PopupsId(userId, puId));

        Optional<String> firebaseUIDFromUserId = popupRepository.getFirebaseUIDFromUserId(userId);
        if (firebaseUIDFromUserId.isEmpty()) {
            log.warn("Cannot find local_id for user {}", userId);
            return;
        }
        String uid = firebaseUIDFromUserId.get();
        String collectionName = "popups";
        String id = Integer.toString(puId);

        eventPublisher.publishEvent(new CloudDbDeleteRequest("users/{ENV}/" + uid + "/cols/" + collectionName + "/" + id));
    }

    private void checkContentForRewards(Popups pu, int result) throws JsonProcessingException {
        JsonNode jsonNode = om.readTree(pu.getContent());
        if (jsonNode.has(STR_BUTTON_KEY)) {
            JsonNode jsonNode1 = jsonNode.get(STR_BUTTON_KEY);
            if (jsonNode1.isArray()) {
                jsonNode1.forEach(a -> {
                    if (a.has(STR_ACTION_KEY) && a.has(STR_ACTION_REWARD_KEY)) {
                        JsonNode jsonNode2 = a.get(STR_ACTION_KEY);
                        if (STR_REWARD_KEY.equalsIgnoreCase(jsonNode2.textValue())) {
                            JsonNode rewString = a.get(STR_ACTION_REWARD_KEY);

                            AwardedBonus ab = new AwardedBonus();
                            ab.setPriceDef(rewString.textValue());
                            ab.setUid(pu.getUserId());
                            ab.setType(AchievementType.POPUP);
                            ab.setDescription("");
                            ab.setQualifier(UUID.randomUUID().toString());
                            if (a.has("book_directly") && a.get("book_directly").isBoolean() && a.get("book_directly").asBoolean()) {
                                ab.setClaimImediately(true);
                            }
                            eventPublisher.publishEvent(ab);

                        }
                    }
                });
            }
        }
    }
}