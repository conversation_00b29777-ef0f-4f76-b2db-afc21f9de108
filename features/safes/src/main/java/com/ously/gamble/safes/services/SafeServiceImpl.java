package com.ously.gamble.safes.services;

import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.achievements.TokenType;
import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.features.TokenService;
import com.ously.gamble.api.safes.AddSafeTokens;
import com.ously.gamble.conditions.ConditionalOnBackendOrOffloader;
import com.ously.gamble.payload.TxPrice;
import com.ously.gamble.safes.api.ActiveSafeInfo;
import com.ously.gamble.safes.api.PlayerSafeInfo;
import com.ously.gamble.safes.api.SafeService;
import com.ously.gamble.safes.api.SafeType;
import com.ously.gamble.safes.config.SafeConfiguration;
import com.ously.gamble.safes.persistence.model.ActiveSafe;
import com.ously.gamble.safes.persistence.model.ActiveSafeId;
import com.ously.gamble.safes.persistence.model.BacklogSafe;
import com.ously.gamble.safes.persistence.repositories.ActiveSafeRepository;
import com.ously.gamble.safes.persistence.repositories.BacklogSafeRepository;
import com.ously.gamble.safes.util.SafeConfigurationHelper;
import com.ously.gamble.safes.util.SafeTypeConfig;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Service
@ConditionalOnBackendOrOffloader
@ConditionalOnProperty(prefix = "safes", name = "enabled", havingValue = "true")
public class SafeServiceImpl implements SafeService {
    private final Logger log = LoggerFactory.getLogger(SafeServiceImpl.class);
    private final ActiveSafeRepository activeSaveRepo;
    private final SafeConfiguration safeConfiguration;
    private final ApplicationEventPublisher eventPublisher;
    private final TokenService tokenService;
    private final BacklogSafeRepository backlogSaveRepo;
    private final CachedMap<Long, PlayerSafeInfo> userSafesCachedMap;

    public SafeServiceImpl(ActiveSafeRepository asRepository,
                           BacklogSafeRepository bsRepository,
                           SafeConfiguration safeConfiguration,
                           TokenService tokenService,
                           CachedMap<Long, PlayerSafeInfo> userSafesCachedMap,
                           ApplicationEventPublisher eventPublisher) {
        this.activeSaveRepo = asRepository;
        this.safeConfiguration = safeConfiguration;
        this.eventPublisher = eventPublisher;
        this.tokenService = tokenService;
        this.backlogSaveRepo = bsRepository;
        this.userSafesCachedMap = userSafesCachedMap;
    }

    @Override
    public PlayerSafeInfo getPlayersSafeInfo(long userId) {
        log.debug("Getting safe info for user: {}", userId);
        try {
            return userSafesCachedMap.getOrComputeIfAbsent(userId, this::getPlayersSafeInfoInternal);
        } catch (Exception e) {
            log.error("Error retrieving player safe info for userId: {}", userId, e);
            // Return empty info in case of error to avoid breaking the client
            return new PlayerSafeInfo(Collections.emptyList(), 0);
        }
    }


    /**
     * Internal method to retrieve a player's safe information
     *
     * @param userId The ID of the user to get safe info for
     * @return The player's safe information
     */
    private PlayerSafeInfo getPlayersSafeInfoInternal(long userId) {
        try {
            // Get backlog count
            int backlogCount = backlogSaveRepo.countByUserId(userId);
            log.debug("User {} has {} safes in backlog", userId, backlogCount);

            // Get active safes
            List<ActiveSafe> activeSafes = activeSaveRepo.findAllByUserId(userId);
            log.debug("User {} has {} active safes", userId, activeSafes.size());

            if (activeSafes.isEmpty()) {
                return new PlayerSafeInfo(Collections.emptyList(), backlogCount);
            }

            // Convert to ActiveSafeInfo objects
            List<ActiveSafeInfo> safeInfoList = activeSafes.stream()
                .map(safe -> new ActiveSafeInfo(safe.getSafeId(), safe.getOpenTime(), safe.getType()))
                .toList();

            return new PlayerSafeInfo(safeInfoList, backlogCount);
        } catch (Exception e) {
            log.error("Error retrieving safe info for user {}", userId, e);
            return new PlayerSafeInfo(Collections.emptyList(), 0);
        }
    }


    /**
     * Claims a safe for a user, either if it's ready to be opened or by using a token
     *
     * @param userId The ID of the user claiming the safe
     * @param safeId The ID of the safe to claim
     * @param useToken Whether to use a token to open the safe early
     * @return Optional containing the rewards if successful, empty otherwise
     */
    @Transactional
    @Override
    public Optional<String> claimSafe(long userId, String safeId, boolean useToken) {
        log.debug("Attempting to claim safe {} for user {} (useToken: {})", safeId, userId, useToken);

        try {
            // Check if the safe exists
            Optional<ActiveSafe> safeOpt = activeSaveRepo.findById(new ActiveSafeId(userId, safeId));
            if (safeOpt.isEmpty()) {
                log.warn("Safe {} not found for user {}", safeId, userId);
                return Optional.empty();
            }

            ActiveSafe activeSafe = safeOpt.get();
            boolean canClaim = false;

            // Check if the safe is ready to be opened
            if (activeSafe.getOpenTime().isBefore(Instant.now())) {
                log.debug("Safe {} is ready to be opened (opens at: {})", safeId, activeSafe.getOpenTime());
                canClaim = true;
            } else if (useToken) {
                // Try to use a token to open the safe early
                log.debug("Attempting to use token to open safe {} early", safeId);
                canClaim = tokenService.deductTokens(userId, TokenType.TRESOR_LOCKPICK, 1);
                if (!canClaim) {
                    log.debug("Failed to deduct token for user {}", userId);
                }
            }

            if (canClaim) {
                // Process the safe claim
                String rewards = activeSafe.getRewards();
                log.info("User {} successfully claimed safe {} with rewards: {}", userId, safeId, rewards);

                // Create and publish the awarded bonus event
                AwardedBonus awardedBonus = new AwardedBonus();
                awardedBonus.setClaimImediately(true);
                awardedBonus.setPriceDef(rewards);
                awardedBonus.setQualifier("SAFE:" + safeId);
                awardedBonus.setUid(userId);
                awardedBonus.setType(AchievementType.SAFE_OPENED);
                awardedBonus.setTitle("SAFE HAS BEEN OPENED");
                awardedBonus.setVariables("type=" + activeSafe.getType());
                eventPublisher.publishEvent(awardedBonus);

                // Remove the claimed safe
                activeSaveRepo.delete(activeSafe);
                activeSaveRepo.flush();

                // Move a backlog safe to active if available
                backlogSaveRepo.findLatestByUserId(userId).ifPresent(backlog -> {
                    log.debug("Moving backlog safe to active for user {}", userId);
                    moveBacklogToActive(backlog);
                });

                // Invalidate cache
                userSafesCachedMap.removeFast(userId);

                return Optional.of(rewards);
            } else {
                log.debug("Cannot claim safe {} for user {}: not ready and no token used", safeId, userId);
            }
        } catch (Exception e) {
            log.error("Error claiming safe {} for user {}", safeId, userId, e);
        }

        return Optional.empty();
    }

    /**
     * Moves a backlog safe to active status and removes it from the backlog
     *
     * @param backlog The backlog safe to move to active
     */
    private void moveBacklogToActive(BacklogSafe backlog) {
        long userId = backlog.getUserId();
        TokenType tokenType = backlog.getType().getTokenType();
        String safeId = backlog.getSafeId();

        log.debug("Moving backlog safe {} of type {} to active for user {}", safeId, backlog.getType(), userId);

        try {
            // Add as active safe
            addSafe(userId, tokenType);

            // Remove from backlog
            backlogSaveRepo.delete(backlog);

            // Invalidate cache
            userSafesCachedMap.removeFast(userId);

            log.debug("Successfully moved backlog safe {} to active for user {}", safeId, userId);
        } catch (Exception e) {
            log.error("Error moving backlog safe {} to active for user {}", safeId, userId, e);
        }
    }


    /**
     * Adds a new safe for a user, either as an active safe or in the backlog
     *
     * @param userId The ID of the user to add the safe for
     * @param type The token type that determines the safe type
     * @return Updated PlayerSafeInfo after adding the safe
     */
    @Transactional
    @Override
    public PlayerSafeInfo addSafe(long userId, TokenType type) {
        log.debug("Adding safe of type {} for user {}", type, userId);

        try {
            // Convert token type to safe type
            SafeType safeType = SafeType.fromTokenType(type);
            if (safeType == null) {
                log.error("Invalid token type for safe: {}", type);
                return getPlayersSafeInfo(userId);
            }

            String safeId = UUID.randomUUID().toString();

            // Check if we have open slots for active safes
            int activeCount = activeSaveRepo.countByUserId(userId);
            int activeLimit = safeConfiguration.getActiveSize();

            if (activeCount < activeLimit) {
                // Create active safe
                log.debug("Creating active safe for user {} ({}/{})", userId, activeCount + 1, activeLimit);

                try {
                    // Get player level for reward calculation
                    int playerLevel = activeSaveRepo.getPlayerLevel(userId);
                    SafeTypeConfig safeTypeConfig = SafeConfigurationHelper.getSafeTypeConfig(safeType);

                    if (safeTypeConfig == null) {
                        log.error("No configuration found for safe type: {}", safeType);
                        return getPlayersSafeInfo(userId);
                    }

                    // Calculate opening time and rewards
                    Instant opensAt = Instant.now().plus(Duration.parse(safeTypeConfig.duration()));
                    String rewards = calculateSafeRewards(safeTypeConfig, playerLevel);

                    // Create and save the active safe
                    ActiveSafe activeSafe = new ActiveSafe();
                    activeSafe.setSafeId(safeId);
                    activeSafe.setOpenTime(opensAt);
                    activeSafe.setUserId(userId);
                    activeSafe.setRewards(rewards);
                    activeSafe.setType(safeType);
                    activeSaveRepo.saveAndFlush(activeSafe);

                    log.info("Created active safe {} of type {} for user {}, opens at {}",
                             safeId, safeType, userId, opensAt);
                } catch (Exception e) {
                    log.error("Error creating active safe for user {}", userId, e);
                }
            } else {
                // Check if we can add to backlog
                int backlogCount = backlogSaveRepo.countByUserId(userId);
                int backlogLimit = safeConfiguration.getBacklogSize();

                if (backlogCount < backlogLimit) {
                    // Create backlog safe
                    log.debug("Creating backlog safe for user {} ({}/{})", userId, backlogCount + 1, backlogLimit);

                    try {
                        BacklogSafe backlogSafe = new BacklogSafe();
                        backlogSafe.setSafeId(safeId);
                        backlogSafe.setUserId(userId);
                        backlogSafe.setCreatedAt(Instant.now());
                        backlogSafe.setType(safeType);
                        backlogSaveRepo.saveAndFlush(backlogSafe);

                        log.info("Created backlog safe {} of type {} for user {}", safeId, safeType, userId);
                    } catch (Exception e) {
                        log.error("Error creating backlog safe for user {}", userId, e);
                    }
                } else {
                    log.warn("Cannot add safe for user {}: active slots full ({}/{}) and backlog full ({}/{})",
                             userId, activeCount, activeLimit, backlogCount, backlogLimit);
                }
            }

            // Invalidate cache
            userSafesCachedMap.removeFast(userId);
        } catch (Exception e) {
            log.error("Unexpected error adding safe for user {}", userId, e);
        }

        // Return updated safe info
        return getPlayersSafeInfo(userId);
    }

    /**
     * Calculate rewards for a safe based on player level and safe type configuration
     *
     * @param safeTypeConfig The configuration for the safe type
     * @param playerLevel The player's current level
     * @return A string representation of the rewards
     */
    private String calculateSafeRewards(SafeTypeConfig safeTypeConfig, int playerLevel) {
        // Find the appropriate level segment for the player's level
        for (var levelSegment : safeTypeConfig.slcList()) {
            if (playerLevel >= levelSegment.levelFrom() && playerLevel <= levelSegment.levelTo()) {
                // Use ThreadLocalRandom for better performance and thread safety
                int baseAmount = levelSegment.goldFrom();
                int maxAmount = levelSegment.goldTo();
                int range = maxAmount - baseAmount + 1;
                int safeAmount = ThreadLocalRandom.current().nextInt(range) + baseAmount;

                log.debug("Calculated safe reward for player level {}: {} (range: {}-{})",
                          playerLevel, safeAmount, baseAmount, maxAmount);

                return "S#" + safeAmount;
            }
        }

        // If no matching level segment is found, use the highest one
        if (!safeTypeConfig.slcList().isEmpty()) {
            var highestSegment = safeTypeConfig.slcList().getLast();
            int baseAmount = highestSegment.goldFrom();
            int maxAmount = highestSegment.goldTo();
            int range = maxAmount - baseAmount + 1;
            int safeAmount = ThreadLocalRandom.current().nextInt(range) + baseAmount;

            log.warn("No exact level segment found for player level {}. Using highest segment: {}-{}",
                     playerLevel, highestSegment.levelFrom(), highestSegment.levelTo());

            return "S#" + safeAmount;
        }

        log.error("No level segments found in safe configuration");
        return "";
    }


    /**
     * Event listener for handling safe tokens
     *
     * @param event The AddSafeTokens event containing user ID and tokens
     */
    @EventListener
    public void handleSafeTokens(AddSafeTokens event) {
        long userId = event.userId();
        List<TxPrice> safeTokens = event.safeTokens();

        log.debug("Handling safe tokens event for user {}: {} tokens", userId, safeTokens.size());

        if (safeTokens.isEmpty()) {
            log.debug("No safe tokens to process for user {}", userId);
            return;
        }

        try {
            // Process each token and add corresponding safes
            List<TokenType> validTokens = safeTokens.stream()
                .map(this::getTokens)
                .filter(Objects::nonNull)
                .toList();

            log.debug("Processing {} valid tokens for user {}", validTokens.size(), userId);

            for (TokenType tokenType : validTokens) {
                try {
                    addSafe(userId, tokenType);
                } catch (Exception e) {
                    log.error("Error adding safe of type {} for user {}", tokenType, userId, e);
                }
            }

            // Ensure cache is invalidated
            userSafesCachedMap.removeFast(userId);

            log.info("Processed {} safe tokens for user {}", validTokens.size(), userId);
        } catch (Exception e) {
            log.error("Error handling safe tokens for user {}", userId, e);
        }
    }

    /**
     * Converts a transaction price to the corresponding token type for safes
     *
     * @param txPrice The transaction price to convert
     * @return The corresponding token type, or null if not applicable
     */
    private TokenType getTokens(TxPrice txPrice) {
        if (txPrice == null) {
            log.warn("Received null TxPrice");
            return null;
        }

        TokenType result = switch (txPrice.getType()) {
            case TSR -> randomSafeType();
            case TSW -> TokenType.TRESOR_WOOD;
            case TSS -> TokenType.TRESOR_SILVER;
            case TSG -> TokenType.TRESOR_GOLD;
            case TSE -> TokenType.TRESOR_EPIC;
            case TSL -> TokenType.TRESOR_LEGENDARY;
            case TSU -> TokenType.TRESOR_ULTIMATE;
            default -> null;
        };

        if (result == null) {
            log.debug("No token type mapping for TxPrice type: {}", txPrice.getType());
        }

        return result;
    }

    /**
     * Gets a random safe token type based on the configured distribution
     *
     * @return A randomly selected token type
     */
    private TokenType randomSafeType() {
        SafeType randomSafeType = SafeConfigurationHelper.getTypeForRandomSafe();
        log.debug("Selected random safe type: {}", randomSafeType);

        return switch (randomSafeType) {
            case WOOD -> TokenType.TRESOR_WOOD;
            case SILVER -> TokenType.TRESOR_SILVER;
            case GOLD -> TokenType.TRESOR_GOLD;
            case EPIC -> TokenType.TRESOR_EPIC;
            case LEGENDARY -> TokenType.TRESOR_LEGENDARY;
            case ULTIMATE -> TokenType.TRESOR_ULTIMATE;
        };
    }
}
