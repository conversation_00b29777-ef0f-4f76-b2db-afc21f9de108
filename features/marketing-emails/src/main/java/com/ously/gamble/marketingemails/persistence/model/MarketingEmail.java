package com.ously.gamble.marketingemails.persistence.model;

import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "marketing_emails")
@IdClass(MarketingEmailId.class)
public class MarketingEmail implements Persistable<MarketingEmailId> {

    @Id
    @Column(name = "user_id")
    private long userId;

    @Id
    @Column(name = "num")
    private int num;

    @Column(name = "email")
    private String email;


    @Column(name = "verified_at")
    private Instant verifiedAt;

    @Column(name = "expires_at")
    private Instant expiresAt;

    public MarketingEmail() {
    }

    @Override
    public MarketingEmailId getId() {
        return new MarketingEmailId(userId, num);
    }

    @Override
    public boolean isNew() {
        return false;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Instant getVerifiedAt() {
        return verifiedAt;
    }

    public void setVerifiedAt(Instant verifiedAt) {
        this.verifiedAt = verifiedAt;
    }

    public Instant getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(Instant expiresAt) {
        this.expiresAt = expiresAt;
    }

    private String generateVerificationCode() {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 6);
    }
}
