package com.ously.gamble.marketingemails.persistence.repository;

import com.ously.gamble.marketingemails.persistence.model.MarketingEmail;
import com.ously.gamble.marketingemails.persistence.model.MarketingEmailId;
import jakarta.transaction.Transactional;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@ConditionalOnProperty(prefix = "marketingemails", name = "enabled", havingValue = "true")
public interface MarketingEmailRepository extends JpaRepository<MarketingEmail, MarketingEmailId> {
    @Query(nativeQuery = true, value =
            """
                            select coalesce(max(num),0)+1 from marketing_emails where user_id= :userId
                    """)
    int getNextFreeNumForUser(@Param("userId") long userId);

    @Query("select me from MarketingEmail me where me.userId = :userId and me.email = :email and me.verifiedAt is null and me.expiresAt > CURRENT_TIMESTAMP")
    Optional<MarketingEmail> findByUserIdAndEmail(long userId, String email);

    @Query("select me from MarketingEmail me where me.email = :email and me.verifiedAt is not null and me.num = (select max(me2.num) from MarketingEmail me2 where me2.email = :email and me2.verifiedAt is not null)")
    Optional<MarketingEmail> findActiveVerifiedEmailByEmail(@Param("email") String email);

    @Modifying
    @Transactional
    @Query("delete from MarketingEmail me where me.expiresAt < CURRENT_TIMESTAMP and me.verifiedAt is null")
    void deleteExpiredVerificationRequests();

    @Modifying
    @Transactional
    @Query("delete from MarketingEmail me where me.userId = :userId and me.verifiedAt is null")
    void deleteOpenVerificationRequests(long userId);
}
