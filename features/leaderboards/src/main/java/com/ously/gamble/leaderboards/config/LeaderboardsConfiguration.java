package com.ously.gamble.leaderboards.config;

import com.ously.gamble.api.leaderboards.LeaderboardType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "leaderboards")
public class LeaderboardsConfiguration {

    boolean enabled = true;
    int topEntries = 5;

    boolean schedulerenabled = true;

    int ttlseconds = 10 * 60;

    int cachesize = 5000;

    int concurrency = 25;

    int minuseractivedays = 90;

    boolean interner = false;

    boolean reloadOnSessionStatus = true;


    LeaderboardType[] enabledTypes = new LeaderboardType[]{LeaderboardType.MAXMULT_DAYS30,
            LeaderboardType.MAXMULT_ALLTIME, LeaderboardType.MAXWIN_ALLTIME, LeaderboardType.MAXWIN_DAYS30};

    public boolean isReloadOnSessionStatus() {
        return reloadOnSessionStatus;
    }

    public void setReloadOnSessionStatus(boolean reloadOnSessionStatus) {
        this.reloadOnSessionStatus = reloadOnSessionStatus;
    }

    public boolean isInterner() {
        return interner;
    }

    public void setInterner(boolean interner) {
        this.interner = interner;
    }

    public int getTopEntries() {
        return topEntries;
    }

    public void setTopEntries(int topEntries) {
        this.topEntries = topEntries;
    }

    public LeaderboardType[] getEnabledTypes() {
        return enabledTypes;
    }

    public void setEnabledTypes(LeaderboardType[] enabledTypes) {
        this.enabledTypes = enabledTypes;
    }

    public int getTtlseconds() {
        return ttlseconds;
    }

    public void setTtlseconds(int ttlseconds) {
        this.ttlseconds = ttlseconds;
    }

    public int getCachesize() {
        return cachesize;
    }

    public void setCachesize(int cachesize) {
        this.cachesize = cachesize;
    }

    public int getConcurrency() {
        return concurrency;
    }

    public void setConcurrency(int concurrency) {
        this.concurrency = concurrency;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getMinuseractivedays() {
        return minuseractivedays;
    }

    public void setMinuseractivedays(int minuseractivedays) {
        this.minuseractivedays = minuseractivedays;
    }

    public boolean isSchedulerenabled() {
        return schedulerenabled;
    }

    public void setSchedulerenabled(boolean schedulerenabled) {
        this.schedulerenabled = schedulerenabled;
    }
}

