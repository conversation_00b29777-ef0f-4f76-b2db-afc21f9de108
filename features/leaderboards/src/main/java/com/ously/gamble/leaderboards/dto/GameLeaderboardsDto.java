package com.ously.gamble.leaderboards.dto;

import com.ously.gamble.api.leaderboards.*;
import com.ously.gamble.api.session.SessionStatistics;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


public class GameLeaderboardsDto {


    private static final BigDecimal BD100 = BigDecimal.valueOf(100L);
    private final int gameId;
    private final Map<LeaderboardType, IndexedGameLeaderboard> boards;

    public GameLeaderboardsDto(int gameId, Map<LeaderboardType, List<GameLeaderboardEntry>> entries) {
        this.gameId = gameId;
        this.boards = new EnumMap<>(LeaderboardType.class);
        for (Map.Entry<LeaderboardType, List<GameLeaderboardEntry>> entry : entries.entrySet()) {
            boards.put(entry.getKey(), new IndexedGameLeaderboard(entry.getValue()));
        }

    }

    public GameLeaderboards getLeaderboardsForUser(String playerId, int topCount, SessionStatistics sessionStatistics) {
        Map<LeaderboardType, GameLeaderboard> glbMap = createGlbMap(playerId, topCount, sessionStatistics);
        return new GameLeaderboards(gameId, glbMap);
    }

    private Map<LeaderboardType, GameLeaderboard> createGlbMap(String playerId, int topCount, SessionStatistics statistics) {
        Map<LeaderboardType, GameLeaderboard> result = new EnumMap<>(LeaderboardType.class);
        if (statistics == null) {
        boards.forEach((key, value) -> result.put(key, new GameLeaderboard(value.getUserPosition(playerId), value.getUserValue(playerId),
                value.topEntries(topCount), value.getCount())));
        } else {
            // TODO: inc. the session statistics into the leaderboards
            boards.forEach((key, value) -> result.put(key, reorderLeaderboards(playerId, key, value, statistics, topCount))
            );

        }
        return result;
    }


    /**
     * @param playerId   the extId of the player
     * @param type       type of the board
     * @param board      the board entries
     * @param statistics the SessionStatistics used to reorder
     * @param topCount   the number of top entries returned
     * @return A GameLeaderboard with the "new" topN entries and the updated rank of the player acc. to latest sessionStatistics
     */
    public static GameLeaderboard reorderLeaderboards(String playerId, LeaderboardType type, IndexedGameLeaderboard board, SessionStatistics statistics, int topCount) {
        // this is the value to update for the playerId
        long nValue = type.valueType() == GameLeaderboardValueType.MAXMULT ? (statistics.getMaxMultiplier().multiply(BD100).longValue()) : (statistics.getMaxWin().multiply(BD100).longValue());

        int userPosition = board.getUserPosition(playerId);
        long userValue = board.getUserValue(playerId);

        // new value is less than stats value, return unchanged rankings
        if (userPosition > 0) {
            if (nValue <= userValue) {
                return new GameLeaderboard(userPosition, userValue, board.topEntries(topCount), board.getCount());
            } else {
                int newPos = board.getPositionForValue(nValue);
                if (newPos > topCount) {
                    // Players rank is not on the top N, so we can return the std. one
                    return new GameLeaderboard(newPos, nValue, board.topEntries(topCount), board.getCount());
                } else {
                    // Players new (and pot. old) rank is on topN
                    List<GameLeaderboardEntry> gameLeaderboardEntries = new ArrayList<>(board.topEntries(topCount));
                    Optional<GameLeaderboardEntry> playerEntry = board.entries.stream().filter(a -> a.playerId().equals(playerId)).findFirst();
                    List<GameLeaderboardEntry> collect = gameLeaderboardEntries.stream().filter(a -> !a.playerId().equals(playerId)).collect(Collectors.toList());

                    var oldEntry = playerEntry.orElseGet(() -> new GameLeaderboardEntry(playerId, newPos, nValue, 0, "YOU"));

                    collect.add(newPos - 1, new GameLeaderboardEntry(playerId, newPos, nValue, oldEntry.level(), oldEntry.username()));
                    if (collect.size() > topCount) {
                        collect.removeLast();
                    }
                    return new GameLeaderboard(newPos, userValue, renumberPositions(collect), board.getCount());
                }

            }
        }
        // User is not on the leaderboard already, new entry

        if (userPosition == -1L) {
            int newPos = board.getPositionForValue(nValue);
            if (newPos > topCount) {
                return new GameLeaderboard(newPos, nValue, board.topEntries(topCount), board.getCount() + 1);
            } else {
                var nTopList = board.topEntries(topCount).stream().map(a -> {
                    if (a.value() < nValue) {
                        return new GameLeaderboardEntry(a.playerId(), a.position() + 1, a.value(), a.level(), a.username());
                    } else {
                        return a;
                    }
                }).collect(Collectors.toList());

                // String playerId, int position, long value, int level, String username
                GameLeaderboardEntry entry = new GameLeaderboardEntry(playerId, newPos, nValue, 0, "YOU");
                nTopList.add(newPos - 1, entry);
                nTopList.removeLast();
                return new GameLeaderboard(newPos, nValue, renumberPositions(nTopList), board.getCount() + 1);
            }


        }

        return null;
    }

    private static List<GameLeaderboardEntry> renumberPositions(List<GameLeaderboardEntry> collect) {
        final AtomicInteger position = new AtomicInteger(1);
        return collect.stream().map(a -> new GameLeaderboardEntry(a.playerId(), position.getAndIncrement(), a.value(), a.level(), a.username())).toList();
    }


}
