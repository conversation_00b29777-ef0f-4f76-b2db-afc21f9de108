package com.ously.gamble.missions.persistence.model;

import java.io.Serializable;
import java.util.Objects;

public class UserProgressId implements Serializable {

    private Long userId;
    private Long missionId;

    public UserProgressId() {
    }

    public UserProgressId(Long uId, Long mId) {
        this.userId = uId;
        this.missionId = mId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        var that = (UserProgressId) o;
        return userId.equals(that.userId) && missionId.equals(that.missionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, missionId);
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMissionId() {
        return missionId;
    }

    public void setMissionId(Long missionId) {
        this.missionId = missionId;
    }
}
