package com.ously.gamble.missions.service.evaluators;

import com.ously.gamble.api.missions.*;
import com.ously.gamble.api.session.MonitoredTransaction;
import com.ously.gamble.persistence.model.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@SuppressWarnings("ALL")
public abstract class AbstractMission {
    final Logger log = LoggerFactory.getLogger(this.getClass());
    final Set<MissionGoal> goals;
    final MissionType type;
    final MissionService mMgmt;

    protected AbstractMission(MissionService mMg, MissionType type,
                              MissionGoal... goals) {
        this.type = type;
        this.mMgmt = mMg;
        this.goals = Arrays.stream(goals).collect(Collectors.toUnmodifiableSet());
    }

    public Set<MissionGoal> getMissionGoals() {
        return goals;
    }

    public UserMissionUpdate checkTxForMission(final MonitoredTransaction txholder) {
        final var dm = txholder.getDm();
        if (dm.getType() != type) {
            return null;
        }
        if (!goals.contains(dm.getGoal())) {
            log.debug("Goal {} of txHolder:{} not found", dm.getGoal(), txholder);
            return null;
        }

        return switch (dm.getGoal()) {
            case SPIN_COUNT -> checkSpinCount(dm, txholder);
            case WIN_SUM -> checkWinSum(dm, txholder);
            case SPIN_WIN_COUNT -> checkWinSpinCount(dm, txholder);
            case BET_SUM -> checkBetSum(dm, txholder);
        };
    }

    public UserMissionUpdate checkSpinCount(DailyMission dm, MonitoredTransaction txholder) {
        // only bets & wins with a bet >0 is a pot. new spin
        if (txholder.getBet() > 0.000001d && (txholder.getType() == TransactionType.BET || txholder.getType() == TransactionType.DIRECTWIN)) {
            var userMUpdate = mMgmt.updateMissionProgressDiff(txholder.getUserId(), dm.getId(), dm.getGoalValue(), 1L, dm.getValidTillES(), txholder.getEpochSecondsUTC());
            if (userMUpdate != null) {
                var val = userMUpdate.getCurrentValue();
                if (val >= dm.getGoalValue()) {
                    log.debug("Mission {}/SPIN_COUNT FINISHED for user {}", userMUpdate.getMissionId(), txholder.getUserId());
                    userMUpdate.setFinished(true);
                    return userMUpdate;
                }
                if (val > dm.getGoalValue()) {
                    return null;
                }
                log.debug("PROGRESS: {}/{} for mission {}, user {} ", userMUpdate.getCurrentValue(), dm.getGoalValue(), dm.getId(), txholder.getUserId());
                return userMUpdate;
            }
        }
        return null;
    }

    private UserMissionUpdate checkWinSpinCount(DailyMission dm, MonitoredTransaction txholder) {
        if (txholder.getType() == TransactionType.WIN || txholder.getType() == TransactionType.DIRECTWIN) {
            if (txholder.getWin() <= 0) {
                return null;
            }
            var userMUpdate = mMgmt.updateMissionProgressDiff(txholder.getUserId(), dm.getId(), dm.getGoalValue(), 1L, dm.getValidTillES(), txholder.getEpochSecondsUTC());
            if (userMUpdate != null) {
                var val = userMUpdate.getCurrentValue();
                if (val >= dm.getGoalValue()) {
                    log.debug("Mission {}/SPIN_WIN_COUNT FINISHED for user {}", userMUpdate.getMissionId(), txholder.getUserId());
                    userMUpdate.setFinished(true);
                    return userMUpdate;
                }
                if (val > dm.getGoalValue()) {
                    return null;
                }
                log.debug("PROGRESS: {}/{} for mission {}, user {} ", userMUpdate.getCurrentValue(), dm.getGoalValue(), dm.getId(), txholder.getUserId());
                return userMUpdate;
            }
        }
        return null;
    }

    private UserMissionUpdate checkWinSum(DailyMission dm, MonitoredTransaction txholder) {
        if (txholder.getType() == TransactionType.WIN || txholder.getType() == TransactionType.DIRECTWIN) {
            var winAdd = (long) (txholder.getWin() * 100);
            if (winAdd == 0) {
                return null;
            }
            var userMUpdate = mMgmt.updateMissionProgressDiff(txholder.getUserId(), dm.getId(), dm.getGoalValue(), winAdd, dm.getValidTillES(), txholder.getEpochSecondsUTC());
            if (userMUpdate != null) {
                var val = userMUpdate.getCurrentValue();
                if (val >= dm.getGoalValue()) {
                    log.debug("Mission {}/WIN_SUM FINISHED for user {}", userMUpdate.getMissionId(), txholder.getUserId());
                    userMUpdate.setFinished(true);
                    return userMUpdate;
                }
                if (val > dm.getGoalValue()) {
                    return null;
                }
                log.debug("PROGRESS: {}/{} for mission {}, user {} ", userMUpdate.getCurrentValue(), dm.getGoalValue(), dm.getId(), txholder.getUserId());
                return userMUpdate;
            }
        }
        return null;
    }

    private UserMissionUpdate checkBetSum(DailyMission dm, MonitoredTransaction txholder) {
        if (txholder.getType() == TransactionType.BET || txholder.getType() == TransactionType.DIRECTWIN) {
            var betAdd = (long) (txholder.getBet() * 100);
            if (betAdd == 0) {
                return null;
            }
            log.debug("CHecking upd for {} bet of {}", betAdd, txholder);
            var userMUpdate = mMgmt.updateMissionProgressDiff(txholder.getUserId(), dm.getId(), dm.getGoalValue(), betAdd, dm.getValidTillES(), txholder.getEpochSecondsUTC());
            if (userMUpdate != null) {
                var val = userMUpdate.getCurrentValue();
                if (val >= dm.getGoalValue()) {
                    log.debug("Mission {}/BET_SUM FINISHED for user {}", userMUpdate.getMissionId(), txholder.getUserId());
                    userMUpdate.setFinished(true);
                    return userMUpdate;
                }
                if (val > dm.getGoalValue()) {
                    return null;
                }
                log.debug("PROGRESS: {}/{} for mission {}, user {} ", userMUpdate.getCurrentValue(), dm.getGoalValue(), dm.getId(), txholder.getUserId());
                return userMUpdate;
            }
        }
        return null;
    }

}
