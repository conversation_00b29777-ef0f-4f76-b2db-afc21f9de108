package com.ously.gamble.rtp;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.stereotype.Service;

@Service
public class RTPFeature extends AbstractPlatformFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("RTP feature", "RTP calculations");
    }
}
