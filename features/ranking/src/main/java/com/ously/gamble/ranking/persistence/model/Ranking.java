package com.ously.gamble.ranking.persistence.model;

import com.ously.gamble.api.rankings.*;
import com.ously.gamble.ranking.persistence.converters.RankingResultsConverter;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;

import java.time.Instant;
import java.time.LocalDate;


@SqlResultSetMapping(name = "Ranking.RankingDto",
        classes = @ConstructorResult(targetClass = RankingDto.class,
                columns = {
                        @ColumnResult(name = "rDate", type =
                                LocalDate.class),
                        @ColumnResult(name = "rankId", type =
                                String.class),
                        @ColumnResult(name = "rankingStatus",
                                type =
                                        String.class),

                        @ColumnResult(name = "startAt", type =
                                Instant.class),
                        @ColumnResult(name = "endAt", type =
                                Instant.class),
                        @ColumnResult(name = "rankingLength",
                                type =
                                        String.class),
                        @ColumnResult(name = "rankingType", type =
                                String.class),
                        @ColumnResult(name = "type", type = String.class),
                        @ColumnResult(name = "background", type = String.class),
                        @ColumnResult(name = "selector", type =
                                String.class),
                        @ColumnResult(name = "title", type =
                                String.class),
                        @ColumnResult(name = "entryCost", type = int.class),
                        @ColumnResult(name = "pricePool", type = int.class),
                        @ColumnResult(name =
                                "results",
                                type =
                                        String.class)}))

@SqlResultSetMapping(name = "Ranking.RankingId",
        classes = @ConstructorResult(targetClass = RankingId.class,
                columns = {
                        @ColumnResult(name = "rDate", type =
                                LocalDate.class),
                        @ColumnResult(name = "rankId", type =
                                String.class)}))

@NamedNativeQuery(name = "Ranking.getActiveRankings", query = """
         select rk.r_date as rDate, rk.rank_id as rankId, rk.rank_status as rankingStatus, rk.start_at as startAt, rk.end_at as endAt,
         rk.rank_length as rankingLength, rk.rank_type as rankingType, rk.type as type, rk.background as background,rk.selector as selector,
         rk.title as title,rk.entry_cost as entryCost, rk.price_pool as pricePool
         ,rk.results as results
          from rankings rk where rk.r_date >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) and rk.end_at >= CURRENT_TIMESTAMP
        """, resultSetMapping = "Ranking.RankingDto")

@NamedNativeQuery(name = "Ranking.getClosableRankings", query = """
         select rk.r_date as rDate, rk.rank_id as rankId
          from rankings rk where  rk.end_at < DATE_SUB(CURRENT_TIMESTAMP,INTERVAL 10 SECOND) and rk.rank_status = 'ACTIVE'
        """, resultSetMapping = "Ranking.RankingId")


@NamedNativeQuery(name = "Ranking.getClosedRankingIds", query = """
         select rk.r_date as rDate, rk.rank_id as rankId
          from rankings rk where  rk.end_at > DATE_SUB(CURRENT_TIMESTAMP,INTERVAL ?1 HOUR) and rk.rank_status = 'CLOSED'
          and JSON_EXTRACT(results, '$.sumScores') >0 order by rk.r_date desc,rk.rank_id desc limit ?2,?3
        """, resultSetMapping = "Ranking.RankingId")


@Entity
//@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = "rankings")
@IdClass(RankingId.class)
public class Ranking implements Persistable<RankingId> {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Column(name = "r_date")
    @Id
    LocalDate rDate;

    @Column(name = "rank_id")
    @Id
    String rankId;

    @Column(name = "rank_status")
    @Enumerated(EnumType.STRING)
    RankingStatus rankingStatus;

    @Column(name = "start_at")
    Instant startAt;

    @Column(name = "end_at")
    Instant endAt;

    @Column(name = "rank_length")
    String rankingLength;

    @Column(name = "rank_type")
    @Enumerated(EnumType.STRING)
    RankingType rankingType;

    @Column(name = "selector")
    String selector;

    @Column(name = "type", length = 30, nullable = false)
    @Enumerated(EnumType.STRING)
    TournamentType type = TournamentType.DEFAULT;

    @Column(name = "background", length = 30)
    String background = null;

    @Column(name = "title", length = 200)
    String title = "";

    @Column(name = "entry_cost")
    int entryCost = 0;

    @Column(name = "price_pool")
    int pricePool = 0;


    @Convert(converter = RankingResultsConverter.class)
    @Column(name = "results")
    //@Type(type = "json")
    RankingResults results;

    @Override
    public RankingId getId() {
        return new RankingId(rDate, rankId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public boolean isWasLoaded() {
        return wasLoaded;
    }

    public void setWasLoaded(boolean wasLoaded) {
        this.wasLoaded = wasLoaded;
    }

    public LocalDate getrDate() {
        return rDate;
    }

    public void setrDate(LocalDate rDate) {
        this.rDate = rDate;
    }

    public String getRankId() {
        return rankId;
    }

    public void setRankId(String rankId) {
        this.rankId = rankId;
    }

    public TournamentType getType() {
        return type;
    }

    public void setType(TournamentType type) {
        this.type = type;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public RankingStatus getRankingStatus() {
        return rankingStatus;
    }

    public void setRankingStatus(RankingStatus rankingStatus) {
        this.rankingStatus = rankingStatus;
    }

    public Instant getStartAt() {
        return startAt;
    }

    public void setStartAt(Instant startAt) {
        this.startAt = startAt;
    }

    public Instant getEndAt() {
        return endAt;
    }

    public void setEndAt(Instant endAt) {
        this.endAt = endAt;
    }

    public String getRankingLength() {
        return rankingLength;
    }

    public void setRankingLength(String rankingLength) {
        this.rankingLength = rankingLength;
    }

    public RankingType getRankingType() {
        return rankingType;
    }

    public void setRankingType(RankingType rankingType) {
        this.rankingType = rankingType;
    }

    public String getSelector() {
        return selector;
    }

    public void setSelector(String selector) {
        this.selector = selector;
    }

    public RankingResults getResults() {
        return results;
    }

    public void setResults(RankingResults results) {
        this.results = results;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getEntryCost() {
        return entryCost;
    }

    public void setEntryCost(int entryCost) {
        this.entryCost = entryCost;
    }

    public int getPricePool() {
        return pricePool;
    }

    public void setPricePool(int pricePool) {
        this.pricePool = pricePool;
    }


    public void updateModel(RankingDto dto) {
       
        this.selector = dto.selector();
        this.setTitle(dto.title());
        this.setEntryCost(dto.entryCost());
        this.background = dto.background();
        this.endAt = dto.endAt();
        this.startAt = dto.startAt();
        this.pricePool = dto.pricePool();
        this.rankingLength = dto.rankingLength();
        this.rankingStatus = dto.status();
        this.type = dto.type();
        this.rankingLength = dto.rankingLength();
        this.results = null;
    }
}
