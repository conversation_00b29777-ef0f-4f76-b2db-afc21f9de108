package com.ously.gamble.ranking.monitoring;

import com.ously.gamble.api.missions.RankUpdate;
import com.ously.gamble.api.monitoring.MonitoredItemsBatchEvent;
import com.ously.gamble.api.rankings.RankingScriptService;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.TriggerContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import static com.ously.gamble.api.missions.RankUpdateType.SET_IF_GREATER;

@Component
@ConditionalOnMonitor
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")

public class RankingMonitorProgressUpdater implements Runnable, Trigger {
    final Logger log = LoggerFactory.getLogger(RankingMonitorProgressUpdater.class);

    private static final int MAX_DRAINSIZE = 250;

    final List<RankUpdate> updates = new ArrayList<>(MAX_DRAINSIZE);

    private final RankingScriptService rankScripts;
    private final RankingMonitorResultUpdater rankUpdater;

    long drainageWaitTime = 1000;

    public final BlockingQueue<RankUpdate> queue = new ArrayBlockingQueue<>(25000);


    private final ScheduledFuture<?> scheduledProgressUpdater;


    public RankingMonitorProgressUpdater(ThreadPoolTaskScheduler tpTS,
                                         RankingScriptService rscService,
                                         RankingMonitorResultUpdater resUpdater
    ) {
        this.rankUpdater = resUpdater;
        this.rankScripts = rscService;
        scheduledProgressUpdater = tpTS.schedule(this, this);

        log.info("Started Scheduling of RedisScoredSetUpdater");
    }


    @PreDestroy
    public void shutdown() {
        if (scheduledProgressUpdater != null) {
            if (!scheduledProgressUpdater.isCancelled()) {
                scheduledProgressUpdater.cancel(false);
            }
        }
    }

    @Override
    public void run() {

        if (queue.isEmpty()) {
            drainageWaitTime = 1000L;
        } else {
            var updateCount = 0L;
            synchronized (updates) {
                updateCount = executeUpdates(MAX_DRAINSIZE);
            }
            if (updateCount > (MAX_DRAINSIZE / 2)) {
                drainageWaitTime = Math.max(drainageWaitTime - 50, 0);
            } else {
                drainageWaitTime = Math.min(drainageWaitTime + 50, 1000);
            }
        }
    }


    @EventListener
    public void handleRankingUpdateBatch(MonitoredItemsBatchEvent<RankUpdate> updates) {
        log.debug("Got a batch for ranking updates {}: ", updates.getItems().size());
        for (RankUpdate item : updates.getItems()) {
            try {
                queue.put(item);
            } catch (InterruptedException ignore) {
            }
        }
    }

    public void addUpdates(Collection<RankUpdate> updates) {
        updates.forEach(this::addUpdate);
    }

    public void addUpdate(RankUpdate ru) {
        try {
            queue.put(ru);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private long executeUpdates(int drainSize) {
        // now drain, split updates by setname (& implicitely GT flag)
        queue.drainTo(updates, drainSize);
        var setMap = updates.stream().collect(Collectors.groupingBy(RankUpdate::setName));
        updates.clear();
        Set<String> setNames = setMap.keySet();
        long handledUpdates = setMap.entrySet().stream().map(a -> updateSet(a.getKey(), a.getValue())).mapToLong(a -> a).sum();
        // now update the aggregated numbers and store in cache
        // For each key in setNames execute the aggregation and store value in cachedMap/firestore
        updateAggregatedScores(setNames);
        return handledUpdates;
    }

    private void updateAggregatedScores(Set<String> setNames) {
        rankUpdater.addRanksToUpdate(setNames);
    }


    private int updateSet(String key, List<RankUpdate> updates) {
        if (updates.isEmpty()) {
            return 0;
        }

        log.debug("Got {} ranking updates to apply", updates.size());
        try {
            var gtFlag = updates.getFirst().type() == SET_IF_GREATER;
            if (gtFlag) {
                List<Object> params = new ArrayList<>(updates.size());
                for (var a : updates) {
                    params.add(a.score());
                    params.add(a.userId());
                }

                try {
                    rankScripts.maxScores(key, params.toArray());
                } catch (Exception e) {
                    log.warn("RedisScoreUpdate Problem:cmd='maxScript',params={}, problem={}", params, e.getMessage());
                }


            } else {
                // here we send a multi (multiple commands at once)
                // ZINCR setname score1 userid1
                // ZINCR setname score2 userid2
                // ...
                // EXEC
                List<Object> params = new ArrayList<>(updates.size());
                for (var a : updates) {
                    params.add(a.score());
                    params.add(a.userId());
                }
                rankScripts.sumScores(key, params.toArray());
            }
        } catch (Exception e) {
            log.warn("Exception on ranking scoreUpdater", e);
            return 0;
        }
        return updates.size();
    }


    @Override
    public Instant nextExecution(TriggerContext triggerContext) {
        if (triggerContext.lastCompletion() == null) {
            return Instant.now().plus(10, ChronoUnit.SECONDS);
        }
        var lastFinishedEpochMilli = triggerContext.lastCompletion().toEpochMilli();
        return Instant.ofEpochMilli(lastFinishedEpochMilli + drainageWaitTime);
    }
}
