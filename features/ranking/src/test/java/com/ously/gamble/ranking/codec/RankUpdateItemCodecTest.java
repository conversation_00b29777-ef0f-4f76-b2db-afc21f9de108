package com.ously.gamble.ranking.codec;

import com.ously.gamble.api.missions.RankUpdate;
import io.netty.buffer.ByteBuf;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

import static com.ously.gamble.api.missions.RankUpdateType.SET_IF_GREATER;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

@SuppressWarnings("unused")
class RankUpdateItemCodecTest {
    final static int ROUNDS = 10000;

    @Test
    void testEncodeDecode() throws IOException {
        RankUpdateItemCodec codec = RankUpdateItemCodec.INSTANCE;
        long sumBuf = 0L;
        long sumCap = 0L;
        long start = System.currentTimeMillis();
        for (int i = 0; i < ROUNDS; i++) {
            RankUpdate tx = createTx(i, i % 200);
            ByteBuf encode = codec.getValueEncoder().encode(tx);
            sumBuf += encode.readableBytes();
            sumCap += encode.capacity();
            RankUpdate[] decode = (RankUpdate[]) codec.getValueDecoder().decode(encode, null);
            boolean equals = tx.equals(decode[0]);
            if (!equals) {
                assertEquals(tx, decode[0]);
            }
        }
        long start2 = System.currentTimeMillis();

        System.out.println("BufSize: " + sumBuf + " avg:" + (sumBuf / ROUNDS) + " Capacity: " + sumCap);
        System.out.println("Custom: " + (start2 - start));

    }


    @Test
    void testEncodeDecodeArr() throws IOException {
        RankUpdateItemCodec codec = RankUpdateItemCodec.INSTANCE;
        long sumBuf = 0L;
        long sumCap = 0L;
        RankUpdate[] arr = new RankUpdate[ROUNDS];
        int u = 0;
        for (int i = 0; i < ROUNDS; i++) {
            RankUpdate tx = createTx(i, u);
            arr[i] = tx;
            u = (u + 1) % 200;
        }

        // WARMUP
        for (int i = 0; i < 100; i++) {
            // MilestoneUpdateItem.optimize(arr);
            ByteBuf encodeO = codec.getValueEncoder().encode(arr);
            RankUpdate[] decode = (RankUpdate[]) codec.getValueDecoder().decode(encodeO, null);
            encodeO.release();
        }

        long opt1 = System.nanoTime();
        long opt2 = System.nanoTime();
        ByteBuf encodeO = codec.getValueEncoder().encode(arr);
        long opt3 = System.nanoTime();
        assertEquals(10000, arr.length);


        long start = System.nanoTime();
        ByteBuf encode = codec.getValueEncoder().encode(arr);
        sumBuf += encode.readableBytes();
        sumCap += encode.capacity();

        long start2 = System.nanoTime();
        RankUpdate[] decode = (RankUpdate[]) codec.getValueDecoder().decode(encode, null);
        long end = System.nanoTime();

        assertEquals(arr.length, decode.length);

        // Comp nArr / arr.opt
//        var nArr2 = MilestoneUpdateItem.optimize(decode);
        assertArrayEquals(arr, decode);

        System.out.println("BufSize: " + sumBuf + " avg:" + (sumBuf / ROUNDS) + " Capacity: " + sumCap);
        System.out.println("Opt (all): " + (opt2 - opt1));
        System.out.println("Encode (all): " + (start2 - start));
        System.out.println("Decode (all): " + (end - start2));
        System.out.println("Encode (opt): " + (opt3 - opt2));

    }

    private RankUpdate createTx(int i, int u) {
        return new RankUpdate("rank1", 999L + (i % 20), 100 + (i % 10), SET_IF_GREATER, Instant.now().toEpochMilli());
    }

}