package com.ously.gamble.social.service.game;

import com.ously.gamble.configprops.SlotOptionsConfig;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class GameOptionsServiceImplTest {

    @Test
    void testLevelUnlockCost() {

        GameOptionsServiceImpl gosp = new GameOptionsServiceImpl(
                new SlotOptionsConfig(),
                null,
                null, null,
                null, null,
                null,
                null);


        int cost = gosp.getsCost(-50, 50L);
        assertEquals(10, cost);

    }

}