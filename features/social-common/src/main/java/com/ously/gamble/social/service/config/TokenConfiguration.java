package com.ously.gamble.social.service.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "social.token")
public class TokenConfiguration {


    /**
     * if true, only one coin booster can be activated so stacking is disabled (default=false)
     */
    boolean onlyOneCoinBooster = false;


    public boolean isOnlyOneCoinBooster() {
        return onlyOneCoinBooster;
    }

    public void setOnlyOneCoinBooster(boolean onlyOneCoinBooster) {
        this.onlyOneCoinBooster = onlyOneCoinBooster;
    }
}
