package com.ously.gamble.api.rankings;

import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface RankingService {
    List<String> getJoinedTournaments(long userId);

    List<String> joinTournament(long userId, String rankId);

    List<RankResults> getActiveRanks();

    Map<String, RankResults> getRanksForUser(long userId);

    Map<String, List<RankingResult>> getTopRanksMulti();

    RankingResults closeRanking(LocalDate localDate, String rankId);


    @Transactional
    void rewardRanking(LocalDate localDate, String rankId);

    TournamentPositions getCurrentPositions(String rankId);

    RankingAggregation getAggScoresOfRanking(String rankId);

    List<RankingRewardDefinitionItem> getRankingRewardDefinitions();

    List<RankResults> getLatestClosedRankings(int hoursBack, int offset, int length);

    @Transactional
    // every 2 minutes
    void reloadRankings();
}
