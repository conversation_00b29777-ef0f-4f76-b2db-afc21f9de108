package com.ously.gamble.milestones.monitoring;

import com.ously.gamble.api.monitoring.MonitoredItemsBatchEvent;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import com.ously.gamble.milestones.api.MilestoneProgressCheckerService;
import com.ously.gamble.milestones.api.MilestoneUpdateItem;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.TriggerContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ScheduledFuture;

@Component
@ConditionalOnMonitor
@ConditionalOnProperty(prefix = "monitor.milestones", name = "enabled", havingValue = "true")

// This is a candidate for monitoring container
//
public class MilestonesMonitorProgressUpdater implements Runnable, Trigger {
    final Logger log = LoggerFactory.getLogger(MilestonesMonitorProgressUpdater.class);

    private static final int MAX_DRAINSIZE = 250;

    final List<MilestoneUpdateItem> updates = new ArrayList<>(MAX_DRAINSIZE);
    private final MilestoneProgressCheckerService progressChecker;

    long drainageWaitTime = 1000;

    private final BlockingQueue<MilestoneUpdateItem> userMilestoneUpdateQueue = new ArrayBlockingQueue<>(25000);

    private final JdbcTemplate jdbcTemplate;

    private TransactionTemplate txTemplate;

    private ScheduledFuture<?> scheduledProgressUpdater;


    public MilestonesMonitorProgressUpdater(JdbcTemplate jdbcTemplate,
                                            PlatformTransactionManager transactionManager,
                                            ThreadPoolTaskScheduler tpTS,
                                            MilestoneProgressCheckerService prgChecker
    ) {
        this.progressChecker = prgChecker;
        this.jdbcTemplate = jdbcTemplate;
        if (transactionManager != null) {
            txTemplate = new TransactionTemplate(transactionManager);
            txTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            txTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        }
        if (tpTS != null) {
            scheduledProgressUpdater = tpTS.schedule(this, this);
            log.info("Started Scheduling of Milestone update executions");
        }
    }

    @PreDestroy
    public void shutdown() {
        if (scheduledProgressUpdater != null) {
            if (!scheduledProgressUpdater.isCancelled()) {
                scheduledProgressUpdater.cancel(false);
            }
        }
    }

    @Override
    public void run() {

        var num = userMilestoneUpdateQueue.drainTo(updates, MAX_DRAINSIZE);
        if (num > 0) {
            txTemplate.execute(result -> {
                performUpdates(updates);
                return 0;
            });
            updates.clear();
        }
        recalcWaitTime(num);

    }

    @EventListener
    public void handleMilestoneUpdateBatch(MonitoredItemsBatchEvent<MilestoneUpdateItem> updates) {
        log.debug("Got a batch for user milestone updates {}: ", updates.getItems().size());
        for(MilestoneUpdateItem item:updates.getItems()) {
            try {
                userMilestoneUpdateQueue.put(item);
            } catch (InterruptedException ignore) {
            }
        }
    }

    private void recalcWaitTime(int numDrained) {
        if (numDrained == MAX_DRAINSIZE) {
            drainageWaitTime /= 2;
        } else if (numDrained > (MAX_DRAINSIZE / 2)) {
            drainageWaitTime -= 100;
        } else if (numDrained == 0) {
            drainageWaitTime += 100;
        } else {
            drainageWaitTime -= 50;
        }
        drainageWaitTime = Math.max(0, Math.min(drainageWaitTime, 1000));
    }

    protected void performUpdates(List<MilestoneUpdateItem> updates) {
        if (updates.isEmpty()) {
            return;
        }
       var optUpdates = optimizeUpdates(updates);

        var sql = "INSERT INTO milestone_progress (user_id,milestone_id, stage, stages,score,target_score,updated_at) VALUES (?,?,?,?,?,?,CURRENT_TIMESTAMP) ON DUPLICATE KEY UPDATE score=score + values(score), updated_at=CURRENT_TIMESTAMP";

        int updatedCount = 0;
        try {
            int[] ints = jdbcTemplate.batchUpdate(sql, new MilestoneUpdatePrepStmtBatchSetter(optUpdates));
        } catch (Exception e) {
            log.warn("Exception during insert of progress: {}", sql, e);
        }

        log.debug("updated {} / {} entries -> updCnt {}", updates.size(), optUpdates.length, updatedCount);

        // Checks
        processChecks();

    }

    private void processChecks() {
        progressChecker.processChecks();
    }


    /**
     * Try to minimize number by regrouping by userId/milestone_id
     *
     * @param updates the original list
     */
    protected static MilestoneUpdateItem[] optimizeUpdates(List<MilestoneUpdateItem> updates) {
        return MilestoneUpdateItem.optimize(updates);
    }

    @Override
    public Instant nextExecution(TriggerContext triggerContext) {
        if (triggerContext.lastCompletion() == null) {
            return Instant.now().plus(2, ChronoUnit.SECONDS);
        }
        var lastFinishedEpochMilli = triggerContext.lastCompletion().toEpochMilli();
        return Instant.ofEpochMilli(lastFinishedEpochMilli + drainageWaitTime);
    }
}
