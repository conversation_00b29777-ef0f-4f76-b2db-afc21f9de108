package com.ously.gamble.milestones.api;

import com.ously.gamble.api.monitoring.MonitoredAction;
import com.ously.gamble.api.monitoring.MonitoredGameTx;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public record MilestoneUserItems(long userId, List<MilestoneItem> items) {

    public List<MilestoneUpdateItem> evalTx(MonitoredGameTx tx) {
        return items.stream().map(a -> a.evalTx(tx)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<MilestoneUpdateItem> evalAction(MonitoredAction tx) {
        return items.stream().map(a -> a.evalAction(tx)).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
