package com.ously.gamble.collectibles.validation;

import com.ously.gamble.conditions.ConditionalOnBackend;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.OffsetDateTime;

@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EndDateAfterStartDateValidator.class)
public @interface EndDateAfterStartDate {
    String message() default "End date must be after start date";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

@Component
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
class EndDateAfterStartDateValidator implements ConstraintValidator<EndDateAfterStartDate, Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext context) {
        if (o == null) {
            return true;
        }

        try {
            OffsetDateTime start = (OffsetDateTime) BeanUtils.getPropertyDescriptor(o.getClass(), "startDate")
                    .getReadMethod().invoke(o);

            if (start == null) {
                start = OffsetDateTime.now();
            }

            OffsetDateTime end = (OffsetDateTime) BeanUtils.getPropertyDescriptor(o.getClass(), "endDate")
                    .getReadMethod().invoke(o);

            if (end == null) return true;

            if (!end.isAfter(start)) {
                context.disableDefaultConstraintViolation();

                context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
                        .addPropertyNode("endDate")
                        .addConstraintViolation();

                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
