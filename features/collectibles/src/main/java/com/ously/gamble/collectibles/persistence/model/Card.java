package com.ously.gamble.collectibles.persistence.model;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import java.util.Objects;
import java.util.List;

@Entity
@Table(name = "cards")
public class Card {
    public enum CardStatus {
        ENABLED, EXPIRED, DISABLED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "collection_id", nullable = false)
    private CardCollection cardCollection;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CardStatus status = CardStatus.DISABLED;

    @Column(name = "image_url", nullable = false)
    private String imageUrl;

    @Column(name = "rarity_level", nullable = false)
    private Byte rarityLevel;

    @Column(name = "sort_order")
    private Byte sortOrder = 99;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "card", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Reward> rewards = new HashSet<>();

    @OneToMany(mappedBy = "card", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<CardPiece> cardPieces = new HashSet<>();

    // Constructors
    public Card() {}

    public Card(String name, String imageUrl, CardCollection cardCollection) {
        this.name = name;
        this.imageUrl = imageUrl;
        this.cardCollection = cardCollection;
        this.sortOrder = 99;
    }

    // Utility methods
    public boolean isActive() {
        return status == CardStatus.ENABLED &&
               cardCollection != null && cardCollection.isActive();
    }

    public boolean isExpired() {
        return status == CardStatus.EXPIRED ||
               (cardCollection != null && cardCollection.isExpired());
    }

    public boolean isCommon() {
        return rarityLevel != null && rarityLevel == 1;
    }

    public boolean isUncommon() {
        return rarityLevel != null && rarityLevel == 2;
    }

    public boolean isRare() {
        return rarityLevel != null && rarityLevel == 3;
    }

    // Card pieces management
    public void addCardPiece(CardPiece cardPiece) {
        cardPieces.add(cardPiece);
        cardPiece.setCard(this);
        updateRarityFromPieces();
    }

    public void removeCardPiece(CardPiece cardPiece) {
        cardPieces.remove(cardPiece);
        cardPiece.setCard(null);
        updateRarityFromPieces();
    }

    public void setCardPieces(Set<CardPiece> cardPieces) {
        this.cardPieces.clear();
        if (cardPieces != null) {
            cardPieces.forEach(this::addCardPiece);
        }
    }

    public void updateCardPieces(List<CardPiece> newPieces) {
        // Clear existing pieces
        this.cardPieces.clear();

        // Add new pieces
        if (newPieces != null) {
            for (CardPiece piece : newPieces) {
                piece.setCard(this);
                this.cardPieces.add(piece);
            }
        }

        updateRarityFromPieces();
    }

    private void updateRarityFromPieces() {
        if (cardPieces.isEmpty()) {
            this.rarityLevel = 1; // Default to common if no pieces
            return;
        }

        double averageRarity = cardPieces.stream()
                .mapToInt(CardPiece::getRarityLevel)
                .average()
                .orElse(1.0);

        this.rarityLevel = (byte) Math.round(averageRarity);
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public CardCollection getCardCollection() {
        return cardCollection;
    }

    public void setCardCollection(CardCollection cardCollection) {
        this.cardCollection = cardCollection;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CardStatus getStatus() {
        return status;
    }

    public void setStatus(CardStatus status) {
        this.status = status;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Byte getRarityLevel() {
        return rarityLevel;
    }

    public void setRarityLevel(Byte rarityLevel) {
        this.rarityLevel = rarityLevel;
    }

    public Byte getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Byte sortOrder) {
        this.sortOrder = sortOrder;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public Set<Reward> getRewards() {
        return rewards;
    }

    public void setRewards(Set<Reward> rewards) {
        this.rewards = rewards;
    }

    public Set<CardPiece> getCardPieces() {
        return cardPieces;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Card card = (Card) o;
        return Objects.equals(id, card.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Card{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", rarityLevel=" + rarityLevel +
                ", status=" + status +
                '}';
    }
}
