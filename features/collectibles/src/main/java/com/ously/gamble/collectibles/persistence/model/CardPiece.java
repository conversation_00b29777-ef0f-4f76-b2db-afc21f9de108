package com.ously.gamble.collectibles.persistence.model;

import jakarta.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "card_pieces")
public class CardPiece {

    @EmbeddedId
    private CardPieceId id;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("cardId")
    @JoinColumn(name = "card_id", nullable = false)
    private Card card;

    @Column(name = "rarity_level", nullable = false)
    private Integer rarityLevel;

    // Constructors
    public CardPiece() {}

    public CardPiece(Integer pieceNumber, Integer rarityLevel) {
        this.id = new CardPieceId(null, pieceNumber);
        this.rarityLevel = rarityLevel;
    }

    public CardPiece(Card card, Integer pieceNumber, Integer rarityLevel) {
        this.id = new CardPieceId(card.getId(), pieceNumber);
        this.card = card;
        this.rarityLevel = rarityLevel;
    }

    // Utility methods
    public boolean isCommon() {
        return rarityLevel != null && rarityLevel == 1;
    }

    public boolean isUncommon() {
        return rarityLevel != null && rarityLevel == 2;
    }

    public boolean isRare() {
        return rarityLevel != null && rarityLevel == 3;
    }

    // Getters and Setters
    public CardPieceId getId() {
        return id;
    }

    public void setId(CardPieceId id) {
        this.id = id;
    }

    public Card getCard() {
        return card;
    }

    public void setCard(Card card) {
        this.card = card;
        if (card != null && this.id != null) {
            this.id.setCardId(card.getId());
        }
    }

    public Integer getPieceNumber() {
        return id != null ? id.getPieceNumber() : null;
    }

    public void setPieceNumber(Integer pieceNumber) {
        if (this.id == null) {
            this.id = new CardPieceId();
        }
        this.id.setPieceNumber(pieceNumber);
    }

    public Integer getRarityLevel() {
        return rarityLevel;
    }

    public void setRarityLevel(Integer rarityLevel) {
        this.rarityLevel = rarityLevel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardPiece cardPiece = (CardPiece) o;
        return Objects.equals(id, cardPiece.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "CardPiece{" +
                "id=" + id +
                ", rarityLevel=" + rarityLevel +
                '}';
    }

    // Embedded ID class
    @Embeddable
    public static class CardPieceId {
        @Column(name = "card_id")
        private Integer cardId;

        @Column(name = "piece_number")
        private Integer pieceNumber;

        public CardPieceId() {}

        public CardPieceId(Integer cardId, Integer pieceNumber) {
            this.cardId = cardId;
            this.pieceNumber = pieceNumber;
        }

        public Integer getCardId() {
            return cardId;
        }

        public void setCardId(Integer cardId) {
            this.cardId = cardId;
        }

        public Integer getPieceNumber() {
            return pieceNumber;
        }

        public void setPieceNumber(Integer pieceNumber) {
            this.pieceNumber = pieceNumber;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CardPieceId that = (CardPieceId) o;
            return Objects.equals(cardId, that.cardId) && Objects.equals(pieceNumber, that.pieceNumber);
        }

        @Override
        public int hashCode() {
            return Objects.hash(cardId, pieceNumber);
        }

        @Override
        public String toString() {
            return "CardPieceId{" +
                    "cardId=" + cardId +
                    ", pieceNumber=" + pieceNumber +
                    '}';
        }
    }
}
