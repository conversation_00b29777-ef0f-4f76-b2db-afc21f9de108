package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.service.CardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin/collections/{collectionId}/cards")
@Tag(name = "Admin Cards", description = "Admin operations for cards within collections")
public class CardController {

    private static final Logger log = LoggerFactory.getLogger(CardController.class);

    private final CardService cardService;
    private final CardCollectionMapper mapper;

    public CardController(CardService cardService, CardCollectionMapper mapper) {
        this.cardService = cardService;
        this.mapper = mapper;
    }

    @Operation(description = "Get all cards in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCards(@PathVariable Integer collectionId) {
        return cardService.findByCollectionId(collectionId)
                .stream()
                .map(mapper::toCardResponse)
                .toList();
    }

    @Operation(description = "Get card by id", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> getCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        return cardService.findByIdAndCollectionIdWithPieces(cardId, collectionId)
                .map(card -> ResponseEntity.ok(mapper.toCardResponse(card)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create card in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardResponse createCard(
            @PathVariable Integer collectionId,
            @Valid @RequestBody CardCollectionDto.CreateCardRequest request) throws CollectiblesLogicException {
        Card card = cardService.createCard(collectionId, request);
        return mapper.toCardResponse(card);
    }

    @Operation(description = "Update card", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> updateCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @Valid @RequestBody CardCollectionDto.UpdateCardRequest request) throws CollectiblesLogicException {

        Optional<Card> card = cardService.updateCard(collectionId, cardId, request);
        if (card.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(mapper.toCardResponse(card.get()));
    }

    @Operation(description = "Delete card", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        if (cardService.deleteCard(collectionId, cardId)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
