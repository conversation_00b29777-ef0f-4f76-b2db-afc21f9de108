package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.service.CollectionService;
import com.ously.gamble.collectibles.service.CardService;
import com.ously.gamble.collectibles.persistence.model.Card;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin/collections")
@Tag(name = "Admin Collections", description = "Admin operations for collections, cards and rewards")
public class AdminController {

    private static final Logger log = LoggerFactory.getLogger(AdminController.class);

    private final CollectionService collectionService;
    private final CardService cardService;
    private final CardCollectionMapper mapper;

    public AdminController(CollectionService collectionService, CardService cardService, CardCollectionMapper mapper) {
        this.collectionService = collectionService;
        this.cardService = cardService;
        this.mapper = mapper;
    }

    // === COLLECTION OPERATIONS ===

    @Operation(description = "Get all collections", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public Page<CardCollectionDto.CardCollectionSummaryResponse> getCollections(
            @PageableDefault(size = 20) Pageable pageable,
            @Parameter(description = "Filter by collection status")
            @RequestParam(required = false) CardCollection.CollectionStatus status,
            @Parameter(description = "Filter collections starting from this date")
            @RequestParam(required = false) OffsetDateTime startDateFrom,
            @Parameter(description = "Filter collections starting before this date")
            @RequestParam(required = false) OffsetDateTime startDateTo,
            @Parameter(description = "Filter collections ending from this date")
            @RequestParam(required = false) OffsetDateTime endDateFrom,
            @Parameter(description = "Filter collections ending before this date")
            @RequestParam(required = false) OffsetDateTime endDateTo) {

        // If no filters provided, use the simple method
        if (status == null && startDateFrom == null && startDateTo == null &&
            endDateFrom == null && endDateTo == null) {
            return collectionService.findAll(pageable)
                    .map(mapper::toCollectionResponse);
        }

        //  Otherwise use filtered method
        return collectionService.findAllFiltered(
                status, startDateFrom, startDateTo, endDateFrom, endDateTo, pageable)
                .map(mapper::toCollectionResponse);
    }

    @Operation(description = "Get one collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionSummaryResponse> getCollection(@PathVariable Integer id) {
        return collectionService.findById(id)
                .map(collection -> ResponseEntity.ok(mapper.toCollectionResponse(collection)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionSummaryResponse createCollection(
            @Valid @RequestBody CardCollectionDto.CreateCardCollectionRequest request) throws CollectiblesLogicException {
        CardCollection collection = collectionService.create(request);
        return mapper.toCollectionResponse(collection);
    }

    @Operation(description = "Update collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionSummaryResponse> updateCollection(
            @PathVariable Integer id,
            @Valid @RequestBody CardCollectionDto.UpdateCardCollectionRequest request) throws CollectiblesLogicException {
        Optional<CardCollection> collection = collectionService.update(id, request);

        if (collection.isEmpty()) return ResponseEntity.notFound().build();
        return ResponseEntity.ok(mapper.toCollectionResponse(collection.get()));
    }

    @Operation(description = "Delete collection", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
        if (collectionService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // === CARD OPERATIONS ===

    @Operation(description = "Get all cards in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{collectionId}/cards")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCards(@PathVariable Integer collectionId) {
        return cardService.findByCollectionId(collectionId)
                .stream()
                .map(mapper::toCardResponse)
                .toList();
    }

    @Operation(description = "Get card by id", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> getCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        return cardService.findByIdAndCollectionIdWithPieces(cardId, collectionId)
                .map(card -> ResponseEntity.ok(mapper.toCardResponse(card)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create card in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{collectionId}/cards")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardResponse createCard(
            @PathVariable Integer collectionId,
            @Valid @RequestBody CardCollectionDto.CreateCardRequest request) throws CollectiblesLogicException {
        Card card = cardService.createCard(collectionId, request);
        return mapper.toCardResponse(card);
    }

    @Operation(description = "Update card", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> updateCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @Valid @RequestBody CardCollectionDto.UpdateCardRequest request) throws CollectiblesLogicException {

        // Manually set the ID from path parameter
        CardCollectionDto.UpdateCardRequest requestWithId = new CardCollectionDto.UpdateCardRequest(
                cardId,
                request.name(),
                request.imageUrl(),
                request.cardPieces(),
                request.status(),
                request.sortOrder()
        );

        Optional<Card> card = cardService.updateCard(collectionId, cardId, requestWithId);
        if (card.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(mapper.toCardResponse(card.get()));
    }

    @Operation(description = "Delete card", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        if (cardService.deleteCard(collectionId, cardId)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

}
