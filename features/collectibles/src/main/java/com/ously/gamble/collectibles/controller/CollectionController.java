package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.service.CollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin/collections")
@Tag(name = "Admin Collections", description = "Admin operations for collections")
public class CollectionController {

    private static final Logger log = LoggerFactory.getLogger(CollectionController.class);

    private final CollectionService collectionService;
    private final CardCollectionMapper mapper;

    public CollectionController(CollectionService collectionService, CardCollectionMapper mapper) {
        this.collectionService = collectionService;
        this.mapper = mapper;
    }

    @Operation(description = "Get all collections", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public Page<CardCollectionDto.CardCollectionSummaryResponse> getCollections(
            @PageableDefault(size = 20) Pageable pageable,
            @Parameter(description = "Filter by collection status")
            @RequestParam(required = false) CardCollection.CollectionStatus status,
            @Parameter(description = "Filter collections starting from this date")
            @RequestParam(required = false) OffsetDateTime startDateFrom,
            @Parameter(description = "Filter collections starting before this date")
            @RequestParam(required = false) OffsetDateTime startDateTo,
            @Parameter(description = "Filter collections ending from this date")
            @RequestParam(required = false) OffsetDateTime endDateFrom,
            @Parameter(description = "Filter collections ending before this date")
            @RequestParam(required = false) OffsetDateTime endDateTo) {

        // If no filters provided, use the simple method
        if (status == null && startDateFrom == null && startDateTo == null &&
            endDateFrom == null && endDateTo == null) {
            return collectionService.findAll(pageable)
                    .map(mapper::toCollectionResponse);
        }

        //  Otherwise use filtered method
        return collectionService.findAllFiltered(
                status, startDateFrom, startDateTo, endDateFrom, endDateTo, pageable)
                .map(mapper::toCollectionResponse);
    }

    @Operation(description = "Get one collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionSummaryResponse> getCollection(@PathVariable Integer id) {
        return collectionService.findById(id)
                .map(collection -> ResponseEntity.ok(mapper.toCollectionResponse(collection)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionSummaryResponse createCollection(
            @Valid @RequestBody CardCollectionDto.CreateCardCollectionRequest request) throws CollectiblesLogicException {
        CardCollection collection = collectionService.create(request);
        return mapper.toCollectionResponse(collection);
    }

    @Operation(description = "Update collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionSummaryResponse> updateCollection(
            @PathVariable Integer id,
            @Valid @RequestBody CardCollectionDto.UpdateCardCollectionRequest request) throws CollectiblesLogicException {
        Optional<CardCollection> collection = collectionService.update(id, request);

        if (collection.isEmpty()) return ResponseEntity.notFound().build();
        return ResponseEntity.ok(mapper.toCollectionResponse(collection.get()));
    }

    @Operation(description = "Delete collection", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
        if (collectionService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
