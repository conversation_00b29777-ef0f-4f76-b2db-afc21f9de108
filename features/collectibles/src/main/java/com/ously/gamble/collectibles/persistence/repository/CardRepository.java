package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.Card;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CardRepository extends JpaRepository<Card, Integer> {
    // Basic finders
    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.cardPieces WHERE c.cardCollection.id = :collectionId")
    List<Card> findByCardCollectionId(@Param("collectionId") Integer cardCollectionId);

    Page<Card> findByCardCollectionId(Integer cardCollectionId, Pageable pageable);
    Optional<Card> findByIdAndCardCollectionId(Integer id, Integer cardCollectionId);

    List<Card> findByStatus(Card.CardStatus status);
    Page<Card> findByStatus(Card.CardStatus status, Pageable pageable);

    List<Card> findByRarityLevel(Byte rarityLevel);
    Page<Card> findByRarityLevel(Byte rarityLevel, Pageable pageable);

    List<Card> findByCardCollectionIdAndStatus(Integer cardCollectionId, Card.CardStatus status);
    List<Card> findByCardCollectionIdAndRarityLevel(Integer cardCollectionId, Byte rarityLevel);

    // Unique name validation
    boolean existsByNameAndCardCollectionId(String name, Integer cardCollectionId);
    boolean existsByNameAndCardCollectionIdAndIdNot(String name, Integer cardCollectionId, Integer id);

    // Fetch with card pieces
    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.cardPieces WHERE c.id = :id")
    Optional<Card> findByIdWithCardPieces(@Param("id") Integer id);

    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.cardPieces WHERE c.id = :id AND c.cardCollection.id = :collectionId")
    Optional<Card> findByIdAndCardCollectionIdWithCardPieces(@Param("id") Integer id, @Param("collectionId") Integer collectionId);
    
    // Active cards queries
    @Query("SELECT c FROM Card c WHERE c.status = 'ENABLED' " +
           "AND c.cardCollection.status = 'ENABLED' " +
           "AND c.cardCollection.startDate <= :now " +
           "AND (c.cardCollection.endDate IS NULL OR c.cardCollection.endDate > :now)")
    List<Card> findActiveCards(@Param("now") LocalDateTime now);

    @Query("SELECT c FROM Card c WHERE c.status = 'ENABLED' " +
           "AND c.cardCollection.status = 'ENABLED' " +
           "AND c.cardCollection.startDate <= :now " +
           "AND (c.cardCollection.endDate IS NULL OR c.cardCollection.endDate > :now)")
    Page<Card> findActiveCards(@Param("now") LocalDateTime now, Pageable pageable);
    @Query("SELECT c FROM Card c WHERE c.cardCollection.id = :collectionId " +
           "AND c.status = 'ENABLED'")
    List<Card> findActiveCardsByCollection(@Param("collectionId") Integer collectionId);

    @Query("SELECT c FROM Card c WHERE c.cardCollection.id = :collectionId " +
           "AND c.status = 'ENABLED'")
    Page<Card> findActiveCardsByCollection(@Param("collectionId") Integer collectionId, Pageable pageable);

    @Query("SELECT c FROM Card c WHERE c.status = 'EXPIRED'")
    List<Card> findExpiredCards();

    List<Card> findByNameContainingIgnoreCase(String name);
    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.cardPieces WHERE c.cardCollection.id = :collectionId ORDER BY c.sortOrder ASC, c.createdAt ASC")
    List<Card> findByCardCollectionIdOrderBySortOrderAscCreatedAtAsc(@Param("collectionId") Integer cardCollectionId);
    // Rarity queries
    @Query("SELECT c FROM Card c WHERE c.rarityLevel >= :minRarity")
    List<Card> findByRarityLevelGreaterThanEqual(@Param("minRarity") Byte minRarity);

    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 3")
    List<Card> findRareCards();

    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 1")
    List<Card> findCommonCards();

    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 2")
    List<Card> findUncommonCards();

    // Count queries
    @Query("SELECT COUNT(c) FROM Card c WHERE c.cardCollection.id = :collectionId")
    long countByCollectionId(@Param("collectionId") Integer collectionId);

    @Query("SELECT COUNT(c) FROM Card c WHERE c.cardCollection.id = :collectionId AND c.status = :status")
    long countByCollectionIdAndStatus(@Param("collectionId") Integer collectionId,
                                    @Param("status") Card.CardStatus status);

    @Query("SELECT COUNT(c) FROM Card c WHERE c.rarityLevel = :rarity")
    long countByRarityLevel(@Param("rarity") Byte rarity);

    // Fetch with relations
    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.rewards WHERE c.id = :id")
    Optional<Card> findByIdWithRewards(@Param("id") Integer id);

    @Query("SELECT c FROM Card c " +
           "LEFT JOIN FETCH c.cardCollection cc " +
           "LEFT JOIN FETCH c.rewards r " +
           "LEFT JOIN FETCH c.cardPieces cp " +
           "WHERE c.id = :id")
    Optional<Card> findByIdWithCollectionAndRewardsAndPieces(@Param("id") Integer id);


}
