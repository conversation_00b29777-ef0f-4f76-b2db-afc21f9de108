//package com.ously.gamble.collectibles.configuration;
//
//import com.ously.gamble.api.cache.CachedMap;
//import com.ously.gamble.api.cache.CachedMapFactory;
//import com.ously.gamble.api.cache.CodecType;
//import com.ously.gamble.collectibles.api.CollectibleDto;
//import com.ously.gamble.collectibles.api.CollectibleSetDto;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class CollectiblesSetup {
//
//    @Bean
//    @ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
//    CachedMap<String, CollectibleDto[]> collectiblesCache(
//            CachedMapFactory<String, CollectibleDto[]> fact) {
//        return fact.createLocalCachedMap("dmissions", CodecType.FST, String.class, CollectibleDto[].class, 60 * 30);
//    }
//
//    @Bean
//    @ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
//    CachedMap<String, CollectibleSetDto[]> collectibleSetsCache(
//            CachedMapFactory<String, CollectibleSetDto[]> fact) {
//        return fact.createLocalCachedMap("dmissions", CodecType.FST, String.class, CollectibleSetDto[].class, 60 * 30);
//    }
//}
