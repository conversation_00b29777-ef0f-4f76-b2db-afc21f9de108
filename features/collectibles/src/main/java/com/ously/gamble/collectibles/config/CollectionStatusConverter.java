package com.ously.gamble.collectibles.config;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class CollectionStatusConverter implements Converter<String, CardCollection.CollectionStatus> {
    
    @Override
    public CardCollection.CollectionStatus convert(String source) {
        return CardCollection.CollectionStatus.valueOf(source.toUpperCase());
    }
}
