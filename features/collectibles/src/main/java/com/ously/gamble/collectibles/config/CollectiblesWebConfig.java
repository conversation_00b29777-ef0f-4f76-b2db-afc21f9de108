package com.ously.gamble.collectibles.config;

import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
public class CollectiblesWebConfig implements WebMvcConfigurer {

    private final CollectionStatusConverter collectionStatusConverter;

    public CollectiblesWebConfig(CollectionStatusConverter collectionStatusConverter) {
        this.collectionStatusConverter = collectionStatusConverter;
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(collectionStatusConverter);
    }
}
