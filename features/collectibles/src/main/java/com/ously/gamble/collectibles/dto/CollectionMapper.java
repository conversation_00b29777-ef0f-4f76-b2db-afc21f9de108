package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
public class CollectionMapper {

    public CollectionDto.CardCollectionSummaryResponse toCollectionResponse(CardCollection collection) {
        return new CollectionDto.CardCollectionSummaryResponse(
                collection.getId(),
                collection.getName(),
                collection.getStartDate(),
                collection.getEndDate(),
                collection.getStatus(),
                collection.getSortOrder()
        );
    }

    public CollectionDto.RewardResponse toRewardResponse(Reward reward) {
        String targetType = reward.isForCollection() ? "COLLECTION" : "CARD";
        Integer targetId = reward.getTargetId();
        String targetName = reward.getTargetName();

        return new CollectionDto.RewardResponse(
                reward.getId(),
                reward.getRewardType(),
                reward.getMilestonePercentage(),
                reward.getRewardData(),
                targetType,
                targetId,
                targetName
        );
    }
}
