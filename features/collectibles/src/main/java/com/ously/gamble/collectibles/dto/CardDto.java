package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.Card;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import java.util.List;

public class CardDto {

    //---------------------
    // Request DTOs
    //---------------------

    public record CreateCardRequest(
            @NotBlank(message = "Card name cannot be empty")
            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @NotBlank(message = "Image URL is required")
            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @NotNull(message = "Card pieces are required")
            @Size(min = 4, max = 4, message = "Card must have exactly 4 pieces")
            List<@Valid CreateCardPieceRequest> cardPieces,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {}

    public record UpdateCardRequest(
            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @Size(min = 4, max = 4, message = "Card must have exactly 4 pieces")
            List<@Valid UpdateCardPieceRequest> cardPieces,

            Card.CardStatus status,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {}

    public record CreateCardPieceRequest(
            @NotNull(message = "Piece number is required")
            @Min(value = 1, message = "Piece number must be between 1 and 4")
            @Max(value = 4, message = "Piece number must be between 1 and 4")
            Integer pieceNumber,

            @NotNull(message = "Rarity level is required")
            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Integer rarityLevel
    ) {}

    public record UpdateCardPieceRequest(
            @NotNull(message = "Piece number is required")
            @Min(value = 1, message = "Piece number must be between 1 and 4")
            @Max(value = 4, message = "Piece number must be between 1 and 4")
            Integer pieceNumber,

            @NotNull(message = "Rarity level is required")
            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Integer rarityLevel
    ) {}

    //---------------------
    // Response DTOs
    //---------------------

    public record CardResponse(
            Integer id,
            String name,
            String imageUrl,
            Byte rarityLevel,
            Card.CardStatus status,
            Byte sortOrder,
            List<CardPieceResponse> cardPieces
    ) {}

    public record CardPieceResponse(
            Integer pieceNumber,
            Integer rarityLevel
    ) {}
}
