# Jackpots Module Testing Guide

This guide provides a comprehensive approach to testing the Jackpots module, including test strategies, test cases, and best practices for ensuring high-quality code.

## Table of Contents
1. [Testing Strategy](#testing-strategy)
2. [Unit Testing](#unit-testing)
3. [Integration Testing](#integration-testing)
4. [Performance Testing](#performance-testing)
5. [Test Coverage Goals](#test-coverage-goals)
6. [Test Data Management](#test-data-management)
7. [Mocking Strategies](#mocking-strategies)
8. [Test Execution](#test-execution)
9. [Continuous Integration](#continuous-integration)
10. [Test Case Catalog](#test-case-catalog)

## Testing Strategy

The testing strategy for the Jackpots module follows a pyramid approach:

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Performance Tests**: Test system behavior under load
4. **End-to-End Tests**: Test complete workflows

### Testing Principles

- **Isolation**: Tests should be independent and not affect each other
- **Determinism**: Tests should produce the same results on each run
- **Readability**: Tests should be easy to understand and maintain
- **Coverage**: Tests should cover both happy paths and edge cases
- **Performance**: Tests should execute quickly to enable fast feedback

## Unit Testing

Unit tests focus on testing individual components in isolation, using mocks for dependencies.

### Key Components to Test

#### JackpotService

Test the core service methods:

- `createNewJackpot()`
- `getActiveJackpotForGame()`
- `disableJackpot()`

Example test for `createNewJackpot()`:

```java
@ExtendWith(MockitoExtension.class)
class JackpotServiceImplTest {
    @Mock
    private ActiveJackpotRepository activeJackpotRepo;
    
    @Mock
    private HistoricJackpotRepository historicJackpotRepo;
    
    @Mock
    private ScoredSet<Integer> jackpotSet;
    
    @Mock
    private RTopic njpTopic;
    
    @Mock
    private JackpotConfigService jackpotConfigService;
    
    @InjectMocks
    private JackpotServiceImpl jackpotService;
    
    @Test
    void testCreateNewJackpot_NoExistingJackpot() {
        // Arrange
        int gameId = 999;
        long userId = 0;
        when(activeJackpotRepo.findByGameId(gameId)).thenReturn(Optional.empty());
        when(jackpotConfigService.getJackpotConfig(anyInt(), anyLong(), anyInt(), anyInt()))
            .thenReturn(new JackpotConfig(1, 10, 1000, 5));
        
        // Act
        jackpotService.createNewJackpot(gameId, userId);
        
        // Assert
        verify(activeJackpotRepo).save(any(ActiveJackpot.class));
        verify(jackpotSet).addScore(anyLong(), eq(gameId));
        verify(njpTopic).publish(gameId);
    }
    
    @Test
    void testCreateNewJackpot_ExistingJackpotWithWinner() {
        // Arrange
        int gameId = 999;
        long userId = 123;
        ActiveJackpot existingJackpot = new ActiveJackpot();
        existingJackpot.setGameId(gameId);
        existingJackpot.setPot(10000);
        
        when(activeJackpotRepo.findByGameId(gameId)).thenReturn(Optional.of(existingJackpot));
        when(jackpotSet.getScore(gameId)).thenReturn(15000.0);
        when(jackpotConfigService.getJackpotConfig(anyInt(), anyLong(), anyInt(), anyInt()))
            .thenReturn(new JackpotConfig(1, 10, 1000, 5));
        
        // Act
        jackpotService.createNewJackpot(gameId, userId);
        
        // Assert
        verify(historicJackpotRepo).save(any(HistoricJackpot.class));
        verify(activeJackpotRepo).save(any(ActiveJackpot.class));
        verify(jackpotSet).addScore(anyLong(), eq(gameId));
        verify(njpTopic).publish(gameId);
    }
}
```

#### JackpotMonitoringEvaluator

Test the monitoring and evaluation logic:

- `handleTx()`
- `monitorTransaction()`
- `reloadActiveJackpots()`

Example test for `monitorTransaction()`:

```java
@ExtendWith(MockitoExtension.class)
class JackpotMonitoringEvaluatorImplTest {
    @Mock
    private ActiveJackpotRepository jackpotRepo;
    
    @Mock
    private MonitoringSender<JackpotUpdate> msupdSender;
    
    @Mock
    private JackpotConfiguration jackpotConfig;
    
    @InjectMocks
    private JackpotMonitoringEvaluatorImpl evaluator;
    
    @BeforeEach
    void setUp() {
        Map<Integer, GameJackpot> gameJackpots = new HashMap<>();
        gameJackpots.put(999, new GameJackpot(999, Instant.now(), 10, 1000, 5000, 1000));
        ReflectionTestUtils.setField(evaluator, "gameJackpots", gameJackpots);
        
        when(jackpotConfig.getDynamicPotPercentage()).thenReturn(0.0025);
    }
    
    @Test
    void testMonitorTransaction_QualifyingTransaction() {
        // Arrange
        List<JackpotUpdate> updates = new ArrayList<>();
        MonitoredGameTx tx = new MonitoredGameTx(
            123L, 999, 1, GamePlatform.DESKTOP,
            TransactionType.DIRECTWIN, 5000, 500,
            0, (short) 0, false, 95.4f
        );
        
        // Act
        evaluator.monitorTransaction(tx, updates);
        
        // Assert
        assertEquals(1, updates.size());
        JackpotUpdate update = updates.get(0);
        assertEquals(999, update.gameId());
        assertEquals(123L, update.userId());
        assertEquals(5000 * 0.0025, update.wager(), 0.001);
        assertEquals(1000, update.probability());
    }
    
    @Test
    void testMonitorTransaction_BelowMinimumBet() {
        // Arrange
        List<JackpotUpdate> updates = new ArrayList<>();
        MonitoredGameTx tx = new MonitoredGameTx(
            123L, 999, 1, GamePlatform.DESKTOP,
            TransactionType.DIRECTWIN, 500, 500,
            0, (short) 0, false, 95.4f
        );
        
        // Act
        evaluator.monitorTransaction(tx, updates);
        
        // Assert
        assertEquals(0, updates.size());
    }
}
```

#### JackpotUtils

Test utility methods for jackpot parameter calculation:

- `getJackpotParameters()`
- `aggregate()`

Example test for `getJackpotParameters()`:

```java
class JackpotUtilsTest {
    @Test
    void testGetJackpotParameters_WithHistoricalData() {
        // Arrange
        List<HistoricJackpotData> histData = List.of(
            new HistoricJackpotData(1, 10000, 1000, 20000, 65),
            new HistoricJackpotData(1, 10000, 1000, 20000, 55),
            new HistoricJackpotData(2, 5000, 1000, 20000, 65),
            new HistoricJackpotData(2, 5000, 1000, 20000, 55)
        );
        
        // Act
        JackpotParameters params = JackpotUtils.getJackpotParameters(
            0.5, 0.0025, 0.0025, histData);
        
        // Assert
        assertEquals(9836, params.probability());
        assertTrue(params.initialAmount() > 0);
    }
    
    @Test
    void testGetJackpotParameters_EmptyHistory() {
        // Arrange
        List<HistoricJackpotData> histData = new ArrayList<>();
        
        // Act
        JackpotParameters params = JackpotUtils.getJackpotParameters(
            0.5, 0.0025, 0.0025, histData);
        
        // Assert
        assertEquals(10000, params.probability());
        assertEquals(250, params.initialAmount());
    }
}
```

### Testing Edge Cases

Be sure to test edge cases such as:

- Zero or negative bet amounts
- Very high or low probabilities
- Missing or invalid game IDs
- Concurrent jackpot hits

## Integration Testing

Integration tests verify that components work together correctly.

### Key Integration Tests

#### Jackpot Creation and Retrieval

Test the complete workflow of creating and retrieving jackpots:

```java
@SpringBootTest
@Transactional
class JackpotIntegrationTest {
    @Autowired
    private JackpotService jackpotService;
    
    @Autowired
    private JackpotStatusService jackpotStatusService;
    
    @Autowired
    private ActiveJackpotRepository activeJackpotRepo;
    
    @Test
    void testCreateAndRetrieveJackpot() {
        // Arrange
        int gameId = 999;
        
        // Act
        jackpotService.createNewJackpot(gameId, 0);
        jackpotStatusService.updateJackpotStatus();
        GameJackpot jackpot = jackpotStatusService.getActiveJackpotForGame(gameId);
        
        // Assert
        assertNotNull(jackpot);
        assertEquals(gameId, jackpot.gameId());
    }
}
```

#### Jackpot Monitoring and Evaluation

Test the complete monitoring and evaluation workflow:

```java
@SpringBootTest
@Transactional
class JackpotMonitoringIntegrationTest {
    @Autowired
    private JackpotService jackpotService;
    
    @Autowired
    private JackpotMonitoringEvaluatorImpl jackpotMonitoringEvaluator;
    
    @Autowired
    private ScoredSet<Integer> jackpotSet;
    
    @Test
    void testJackpotMonitoring() throws InterruptedException {
        // Arrange
        int gameId = 999;
        jackpotService.createNewJackpot(gameId, 0);
        double initialScore = jackpotSet.getScore(gameId);
        
        List<MonitoredGameTx> transactions = new ArrayList<>();
        transactions.add(new MonitoredGameTx(
            123L, gameId, 1, GamePlatform.DESKTOP,
            TransactionType.DIRECTWIN, 10000, 500,
            0, (short) 0, false, 95.4f
        ));
        
        // Act
        MonitoredItemsBatchEvent<MonitoredGameTx> event = 
            new MonitoredItemsBatchEvent<>(transactions);
        jackpotMonitoringEvaluator.handleTx(event);
        
        // Allow time for async processing
        Thread.sleep(1000);
        
        // Assert
        double newScore = jackpotSet.getScore(gameId);
        assertTrue(newScore > initialScore);
    }
}
```

#### Jackpot Hit Workflow

Test the complete workflow when a jackpot is hit:

```java
@SpringBootTest
@Transactional
class JackpotHitIntegrationTest {
    @Autowired
    private JackpotService jackpotService;
    
    @Autowired
    private HistoricJackpotRepository historicJackpotRepo;
    
    @Autowired
    private ScoredSet<Integer> jackpotSet;
    
    @Test
    void testJackpotHit() {
        // Arrange
        int gameId = 999;
        long userId = 123;
        jackpotService.createNewJackpot(gameId, 0);
        
        // Simulate pot accumulation
        jackpotSet.addScore(10000, gameId);
        
        // Act - simulate a jackpot hit
        jackpotService.createNewJackpot(gameId, userId);
        
        // Assert
        List<HistoricJackpotData> history = 
            historicJackpotRepo.getLatestHistoricJackpots(gameId, 1);
        assertEquals(1, history.size());
        assertEquals(userId, history.get(0).getUserId());
    }
}
```

## Performance Testing

Performance tests verify that the system can handle expected load.

### Load Testing

Test the system under expected load:

```java
@SpringBootTest
class JackpotLoadTest {
    @Autowired
    private JackpotMonitoringEvaluatorImpl jackpotMonitoringEvaluator;
    
    @Test
    void testHighVolumeTransactionProcessing() {
        // Arrange
        int gameId = 999;
        int transactionCount = 10000;
        
        List<MonitoredGameTx> transactions = new ArrayList<>(transactionCount);
        for (int i = 0; i < transactionCount; i++) {
            transactions.add(new MonitoredGameTx(
                123L, gameId, 1, GamePlatform.DESKTOP,
                TransactionType.DIRECTWIN, 10000, 500,
                0, (short) 0, false, 95.4f
            ));
        }
        
        // Act
        long startTime = System.currentTimeMillis();
        MonitoredItemsBatchEvent<MonitoredGameTx> event = 
            new MonitoredItemsBatchEvent<>(transactions);
        jackpotMonitoringEvaluator.handleTx(event);
        long endTime = System.currentTimeMillis();
        
        // Assert
        long duration = endTime - startTime;
        System.out.println("Processed " + transactionCount + 
            " transactions in " + duration + "ms");
        
        // Ensure processing time is within acceptable limits
        assertTrue(duration < 5000);
    }
}
```

### Stress Testing

Test the system under extreme load:

```java
@SpringBootTest
class JackpotStressTest {
    @Autowired
    private JackpotMonitoringEvaluatorImpl jackpotMonitoringEvaluator;
    
    @Test
    void testConcurrentTransactionProcessing() throws Exception {
        // Arrange
        int gameCount = 100;
        int transactionsPerGame = 1000;
        int threadCount = 10;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // Act
        long startTime = System.currentTimeMillis();
        
        for (int t = 0; t < threadCount; t++) {
            final int threadId = t;
            executor.submit(() -> {
                try {
                    for (int g = 0; g < gameCount / threadCount; g++) {
                        int gameId = threadId * (gameCount / threadCount) + g;
                        
                        List<MonitoredGameTx> transactions = new ArrayList<>(transactionsPerGame);
                        for (int i = 0; i < transactionsPerGame; i++) {
                            transactions.add(new MonitoredGameTx(
                                123L, gameId, 1, GamePlatform.DESKTOP,
                                TransactionType.DIRECTWIN, 10000, 500,
                                0, (short) 0, false, 95.4f
                            ));
                        }
                        
                        MonitoredItemsBatchEvent<MonitoredGameTx> event = 
                            new MonitoredItemsBatchEvent<>(transactions);
                        jackpotMonitoringEvaluator.handleTx(event);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(2, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();
        
        // Assert
        long duration = endTime - startTime;
        System.out.println("Processed " + (gameCount * transactionsPerGame) + 
            " transactions across " + gameCount + " games in " + duration + "ms");
        
        // Ensure processing time is within acceptable limits
        assertTrue(duration < 120000);
    }
}
```

## Test Coverage Goals

The jackpot module should aim for the following test coverage:

- **Line Coverage**: At least 80%
- **Branch Coverage**: At least 75%
- **Method Coverage**: At least 90%

### Critical Areas for Coverage

Focus on achieving high coverage in these critical areas:

1. Jackpot creation and parameter calculation
2. Transaction monitoring and evaluation
3. Jackpot hit determination
4. Error handling and recovery

## Test Data Management

### Test Data Setup

Create utility methods for setting up test data:

```java
public class JackpotTestDataUtil {
    public static ActiveJackpot createTestActiveJackpot(int gameId) {
        ActiveJackpot jackpot = new ActiveJackpot();
        jackpot.setGameId(gameId);
        jackpot.setCreatedAt(Instant.now());
        jackpot.setMinBet(10);
        jackpot.setProbability(1000);
        jackpot.setInitialPot(1000);
        jackpot.setPot(1000);
        return jackpot;
    }
    
    public static List<HistoricJackpotData> createTestHistoricData(int gameId, int count) {
        List<HistoricJackpotData> data = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            data.add(new HistoricJackpotData(
                i + 1, 10000, 1000, 20000, 60 + i
            ));
        }
        return data;
    }
    
    public static MonitoredGameTx createTestTransaction(long userId, int gameId, int bet) {
        return new MonitoredGameTx(
            userId, gameId, 1, GamePlatform.DESKTOP,
            TransactionType.DIRECTWIN, bet, 500,
            0, (short) 0, false, 95.4f
        );
    }
}
```

### Database Setup for Tests

Use `@Sql` annotations to set up database state for tests:

```java
@SpringBootTest
@Transactional
@Sql(scripts = "/sql/jackpot-test-data.sql")
class JackpotDatabaseTest {
    // Test methods
}
```

Example SQL script (`jackpot-test-data.sql`):

```sql
-- Clear existing data
DELETE FROM jackpots_historic;
DELETE FROM jackpots_active;

-- Insert test data
INSERT INTO jackpots_active (game_id, created_at, minbet, probability, repetition, start_pot, pot)
VALUES (999, NOW(), 10, 1000, 0, 1000, 1000);

INSERT INTO jackpots_historic (game_id, created_at, won_at, probability, minbet, start_pot, pot, user_id)
VALUES (999, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), 1000, 10, 1000, 5000, 123);
```

## Mocking Strategies

### Mocking Redis

Use embedded Redis for testing:

```java
@TestConfiguration
public class TestRedisConfiguration {
    private RedisServer redisServer;
    
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        redisServer = new RedisServer(6379);
        redisServer.start();
        
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory();
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }
    
    @PreDestroy
    public void stopRedis() {
        if (redisServer != null) {
            redisServer.stop();
        }
    }
}
```

### Mocking Random Number Generation

Create a testable version of components that use random number generation:

```java
public class TestableJackpotMonitorProgressUpdater extends JackpotMonitorProgressUpdater {
    private final Random controlledRandom;
    
    public TestableJackpotMonitorProgressUpdater(
            ScoredSet<Integer> jackpotSet,
            JackpotService jackpotService,
            Random controlledRandom) {
        super(jackpotSet, jackpotService);
        this.controlledRandom = controlledRandom;
    }
    
    @Override
    protected Random getRandom() {
        return controlledRandom;
    }
}
```

## Test Execution

### Running Tests Locally

Run tests using Maven:

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=JackpotServiceImplTest

# Run tests with specific tag
mvn test -Dgroups=UnitTest
```

### Test Categorization

Use JUnit tags to categorize tests:

```java
@Tag("UnitTest")
class JackpotServiceImplTest {
    // Test methods
}

@Tag("IntegrationTest")
class JackpotIntegrationTest {
    // Test methods
}

@Tag("PerformanceTest")
class JackpotLoadTest {
    // Test methods
}
```

## Continuous Integration

### CI Pipeline Configuration

Configure CI to run tests on each commit:

```yaml
# Example GitHub Actions workflow
name: Jackpot Tests

on:
  push:
    paths:
      - 'features/jackpots/**'
      - 'api/src/main/java/com/ously/gamble/api/jackpots/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'
      - name: Run tests
        run: mvn test -Dtest=com.ously.gamble.jackpots.*Test
      - name: Generate coverage report
        run: mvn jacoco:report
      - name: Upload coverage report
        uses: actions/upload-artifact@v2
        with:
          name: coverage-report
          path: target/site/jacoco
```

### Test Reports

Configure JaCoCo for test coverage reporting:

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.7</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

## Test Case Catalog

### Core Functionality Tests

1. **Jackpot Creation**
   - Create jackpot for new game
   - Create jackpot after previous jackpot hit
   - Create jackpot with historical data
   - Create jackpot without historical data

2. **Jackpot Retrieval**
   - Get active jackpot for game
   - Get all active jackpots
   - Get jackpot for non-existent game

3. **Jackpot Monitoring**
   - Process qualifying transaction
   - Process transaction below minimum bet
   - Process transaction for game without jackpot
   - Process batch of transactions

4. **Jackpot Evaluation**
   - Evaluate jackpot hit (controlled random)
   - Evaluate multiple updates for same game
   - Evaluate updates for multiple games

5. **Jackpot Parameter Calculation**
   - Calculate parameters with historical data
   - Calculate parameters without historical data
   - Calculate parameters with varying probability targets

### Edge Case Tests

1. **Boundary Conditions**
   - Very high probability values
   - Very low probability values
   - Maximum bet amounts
   - Minimum bet amounts
   - Zero bet amounts
   - Negative bet amounts

2. **Error Handling**
   - Database connection failure
   - Redis connection failure
   - Invalid game ID
   - Invalid user ID
   - Duplicate jackpot creation

3. **Concurrency**
   - Concurrent jackpot creation
   - Concurrent transaction processing
   - Concurrent jackpot hits

### Performance Tests

1. **Load Tests**
   - High volume transaction processing
   - Multiple games with transactions
   - Long-running test with continuous transactions

2. **Stress Tests**
   - Maximum transaction rate
   - Maximum number of active jackpots
   - Redis performance under load

3. **Endurance Tests**
   - Continuous operation over extended period
   - Memory usage over time
   - Database growth over time
