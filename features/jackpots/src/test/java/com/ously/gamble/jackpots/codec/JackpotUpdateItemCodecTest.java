package com.ously.gamble.jackpots.codec;

import com.ously.gamble.api.jackpots.JackpotUpdate;
import io.netty.buffer.ByteBuf;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

@SuppressWarnings("unused")
class JackpotUpdateItemCodecTest {
    final static int ROUNDS = 10000;


    @Test
    void testEncodeDecode() throws IOException {
        JackpotUpdateItemCodec codec = JackpotUpdateItemCodec.INSTANCE;
        long sumBuf = 0L;
        long sumCap = 0L;
        long start = System.currentTimeMillis();
        for (int i = 0; i < ROUNDS; i++) {
            JackpotUpdate tx = createTx(i, i % 200);
            ByteBuf encode = codec.getValueEncoder().encode(tx);
            sumBuf += encode.readableBytes();
            sumCap += encode.capacity();
            JackpotUpdate[] decode = (JackpotUpdate[]) codec.getValueDecoder().decode(encode, null);
            boolean equals = tx.equals(decode[0]);
            if (!equals) {
                assertEquals(tx, decode[0]);
            }
        }
        long start2 = System.currentTimeMillis();

        System.out.println("BufSize: " + sumBuf + " avg:" + (sumBuf / ROUNDS) + " Capacity: " + sumCap);
        System.out.println("Custom: " + (start2 - start));

    }


    @Test
    void testEncodeDecodeArr() throws IOException {
        JackpotUpdateItemCodec codec = JackpotUpdateItemCodec.INSTANCE;
        long sumBuf = 0L;
        long sumCap = 0L;
        JackpotUpdate[] arr = new JackpotUpdate[ROUNDS];
        int u = 0;
        for (int i = 0; i < ROUNDS; i++) {
            JackpotUpdate tx = createTx(i, u);
            arr[i] = tx;
            u = (u + 1) % 200;
        }

        // WARMUP
        for (int i = 0; i < 100; i++) {
            // MilestoneUpdateItem.optimize(arr);
            ByteBuf encodeO = codec.getValueEncoder().encode(arr);
            JackpotUpdate[] decode = (JackpotUpdate[]) codec.getValueDecoder().decode(encodeO, null);
            encodeO.release();
        }

        long opt1 = System.nanoTime();
        long opt2 = System.nanoTime();
        ByteBuf encodeO = codec.getValueEncoder().encode(arr);
        long opt3 = System.nanoTime();
        assertEquals(ROUNDS, arr.length);


        long start = System.nanoTime();
        ByteBuf encode = codec.getValueEncoder().encode(arr);
        sumBuf += encode.readableBytes();
        sumCap += encode.capacity();

        long start2 = System.nanoTime();
        JackpotUpdate[] decode = (JackpotUpdate[]) codec.getValueDecoder().decode(encode, null);
        long end = System.nanoTime();

        assertEquals(arr.length, decode.length);

        // Comp nArr / arr.opt
//        var nArr2 = MilestoneUpdateItem.optimize(decode);
        assertArrayEquals(arr, decode);

        System.out.println("BufSize: " + sumBuf + " avg:" + (sumBuf / ROUNDS) + " Capacity: " + sumCap);
        System.out.println("Opt (all): " + (opt2 - opt1));
        System.out.println("Encode (all): " + (start2 - start));
        System.out.println("Decode (all): " + (end - start2));
        System.out.println("Encode (opt): " + (opt3 - opt2));

    }

    private JackpotUpdate createTx(int i, int u) {
        return new JackpotUpdate(1 + (i % 100), 999L + (i % 20), 100 + (i % 10), 1000);
    }

}