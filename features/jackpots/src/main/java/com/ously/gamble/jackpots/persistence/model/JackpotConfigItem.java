package com.ously.gamble.jackpots.persistence.model;

import jakarta.persistence.*;

@Entity
@Table(name = "jackpots_configuration")
public class JackpotConfigItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    Integer id;

    @Column(name = "minBet")
    int minBet;

    @Column(name = "probability")
    long probability;

    /**
     * number of repetitions of this setting until next (as 1/probability) statistically
     */
    @Column(name = "repetitions")
    int repetitions;

    public long getProbability() {
        return probability;
    }

    public void setProbability(long probability) {
        this.probability = probability;
    }

    public int getMinBet() {
        return minBet;
    }

    public void setMinBet(int minBet) {
        this.minBet = minBet;
    }

    public int getRepetitions() {
        return repetitions;
    }

    public void setRepetitions(int repetitions) {
        this.repetitions = repetitions;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
