# Jackpots Module Developer Guide

This guide provides technical details for developers working with the Jackpots module. It covers implementation details, best practices, and common development tasks.

## Table of Contents
1. [Development Environment Setup](#development-environment-setup)
2. [Module Structure](#module-structure)
3. [Key Classes and Interfaces](#key-classes-and-interfaces)
4. [Implementation Details](#implementation-details)
5. [Testing Guidelines](#testing-guidelines)
6. [Common Development Tasks](#common-development-tasks)
7. [Performance Optimization](#performance-optimization)
8. [Extending the Module](#extending-the-module)

## Development Environment Setup

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- Redis 6.0 or higher
- MySQL 8.0 or higher

### Local Development Setup

1. Enable the jackpots module in your local configuration:

```yaml
jackpots:
  enabled: true
  initialPotPercentage: 0.0025
  dynamicPotPercentage: 0.0025
```

2. Set up a local Redis instance:

```bash
docker run --name redis -p 6379:6379 -d redis
```

3. Initialize the database schema:

```bash
mvn liquibase:update -Dliquibase.contexts=jackpots
```

## Module Structure

The Jackpots module is organized into the following packages:

- `com.ously.gamble.jackpots.configuration`: Configuration classes
- `com.ously.gamble.jackpots.controller`: REST controllers
- `com.ously.gamble.jackpots.monitoring`: Monitoring and evaluation components
- `com.ously.gamble.jackpots.persistence`: Database entities and repositories
- `com.ously.gamble.jackpots.services`: Service implementations

## Key Classes and Interfaces

### Core Interfaces

- `JackpotService`: Primary interface for jackpot management
- `JackpotStatusService`: Interface for retrieving jackpot information
- `JackpotConfigService`: Interface for jackpot configuration management

### Implementation Classes

- `JackpotServiceImpl`: Implementation of the JackpotService interface
- `JackpotStatusServiceImpl`: Implementation of the JackpotStatusService interface
- `JackpotMonitoringEvaluatorImpl`: Processes game transactions for jackpot updates
- `JackpotMonitorProgressUpdater`: Handles jackpot evaluation and triggering

### Data Transfer Objects

- `GameJackpot`: Represents jackpot information for a game
- `JackpotUpdate`: Represents an update to a jackpot's pot amount
- `JackpotParameters`: Contains calculated parameters for a new jackpot

## Implementation Details

### Jackpot Creation

The `JackpotServiceImpl.createNewJackpot()` method handles jackpot creation:

```java
@Transactional
@Override
public void createNewJackpot(int gameId, long userId) {
    // Check for existing jackpot
    Optional<ActiveJackpot> activeJackpot = activeJPRepo.findByGameId(gameId);
    
    // If jackpot exists and userId > 0, it means a user won the jackpot
    if (activeJackpot.isPresent() && userId > 0) {
        // Mark the jackpot as won and create a historic record
        markWonAndHistorise(activeJackpot.get(), userId, potValue);
    }
    
    // Create a new jackpot with calculated parameters
    ActiveJackpot newJackpot = createNewJackpotEntity(gameId);
    
    // Save to database and update Redis
    activeJPRepo.save(newJackpot);
    jackpotSet.addScore(newJackpot.getInitialPot(), gameId);
    
    // Notify other components about the new jackpot
    njpTopic.publish(gameId);
}
```

### Jackpot Monitoring

The `JackpotMonitoringEvaluatorImpl.handleTx()` method processes game transactions:

```java
@EventListener
public void handleTx(MonitoredItemsBatchEvent<MonitoredGameTx> txItems) {
    List<MonitoredGameTx> items = txItems.getItems();
    List<JackpotUpdate> updates = new ArrayList<>(items.size());
    
    // Process in batches for better performance
    int curpos = 0;
    int remItemCount = items.size();
    int itemCount = items.size();
    
    while (curpos < itemCount) {
        List<MonitoredGameTx> batch = items.subList(curpos, 
            curpos + Math.min(remItemCount, MAX_EVAL_BATCH_SIZE));
        curpos += batch.size();
        remItemCount -= batch.size();
        evalSubBatch(batch, updates);
    }
    
    // Send updates to the progress updater
    if (!updates.isEmpty()) {
        for (JackpotUpdate it : updates) {
            msupdSender.addItem(it);
        }
    }
}
```

### Jackpot Evaluation

The `JackpotMonitorProgressUpdater.executeUpdates()` method evaluates jackpot hits:

```java
private int executeUpdates(int maxDrainSize) {
    // Drain updates from the queue
    int drained = queue.drainTo(updates, maxDrainSize);
    if (drained == 0) return 0;
    
    // Group updates by game ID
    Map<Integer, List<JackpotUpdate>> updatesByGame = updates.stream()
        .collect(Collectors.groupingBy(JackpotUpdate::gameId));
    
    // Process each game's updates
    for (Map.Entry<Integer, List<JackpotUpdate>> entry : updatesByGame.entrySet()) {
        int key = entry.getKey();
        List<JackpotUpdate> gameUpdates = entry.getValue();
        
        // Calculate probability and wager
        double probability = 1.0 / gameUpdates.getFirst().probability();
        double wagerAdded = 0.0;
        long winner = 0L;
        
        // Process each update and check for jackpot hit
        for (JackpotUpdate update : gameUpdates) {
            wagerAdded += update.wager();
            if (winner == 0 && random.nextDouble() < probability) {
                winner = update.userId();
            }
        }
        
        // Update Redis with the wager amount
        jackpotSet.addScore(wagerAdded, key);
        
        // If there's a winner, create a new jackpot
        if (winner != 0) {
            double wonAmount = jackpotSet.getScore(key);
            jackpotService.createNewJackpot(key, winner);
        }
    }
    
    return drained;
}
```

## Testing Guidelines

### Unit Testing

When writing unit tests for the jackpot module:

1. Use `@MockBean` to mock dependencies like repositories and Redis
2. Test each component in isolation
3. Use fixed random seeds for deterministic testing of probabilistic behavior
4. Test edge cases like minimum bets and extreme probabilities

Example unit test for jackpot parameter calculation:

```java
@Test
void testJackpotParameterCalculation() {
    // Arrange
    List<HistoricJackpotData> histData = List.of(
        new HistoricJackpotData(1, 10000, 1000, 20000, 65),
        new HistoricJackpotData(1, 10000, 1000, 20000, 55)
    );
    
    // Act
    JackpotParameters params = JackpotUtils.getJackpotParameters(
        0.5, 0.0025, 0.0025, histData);
    
    // Assert
    assertEquals(10000, params.probability());
    assertEquals(1000, params.initialAmount());
}
```

### Integration Testing

For integration tests:

1. Use `@SpringBootTest` to test with the actual Spring context
2. Configure test-specific properties
3. Use test containers for Redis and database dependencies
4. Test complete workflows from transaction to jackpot hit

Example integration test for jackpot creation:

```java
@SpringBootTest
@Transactional
class JackpotCreationIntegrationTest {
    @Autowired
    private JackpotService jackpotService;
    
    @Autowired
    private ActiveJackpotRepository activeJackpotRepo;
    
    @Test
    void testJackpotCreation() {
        // Act
        jackpotService.createNewJackpot(999, 0);
        
        // Assert
        Optional<ActiveJackpot> jackpot = activeJackpotRepo.findByGameId(999);
        assertTrue(jackpot.isPresent());
        assertEquals(999, jackpot.get().getGameId());
    }
}
```

## Common Development Tasks

### Adding a New Jackpot Feature

To add a new feature to the jackpot module:

1. Define the requirements and design the feature
2. Update the relevant interfaces and DTOs
3. Implement the feature in the appropriate service classes
4. Add unit and integration tests
5. Update documentation

### Modifying Jackpot Calculation Logic

To modify how jackpot parameters are calculated:

1. Update the `JackpotUtils.getJackpotParameters()` method
2. Adjust the probability and initial amount calculations
3. Add tests to verify the new calculation logic
4. Consider adding a feature flag for gradual rollout

### Adding a New API Endpoint

To add a new API endpoint for jackpot information:

1. Create a new method in the appropriate controller class
2. Implement the required service method
3. Add security annotations as needed
4. Document the new endpoint
5. Add tests for the endpoint

## Performance Optimization

### Batch Processing

The jackpot module uses batch processing to improve performance:

```java
// Process in batches for better performance
while (curpos < itemCount) {
    List<MonitoredGameTx> batch = items.subList(curpos, 
        curpos + Math.min(remItemCount, MAX_EVAL_BATCH_SIZE));
    curpos += batch.size();
    remItemCount -= batch.size();
    evalSubBatch(batch, updates);
}
```

Tune the `MAX_EVAL_BATCH_SIZE` constant based on your system's performance characteristics.

### Redis Optimization

Optimize Redis operations by:

1. Using pipelining for multiple operations
2. Minimizing the number of Redis calls
3. Using appropriate data structures (scored sets for jackpots)
4. Monitoring Redis memory usage and performance

### Database Optimization

Optimize database operations by:

1. Using appropriate indexes on frequently queried fields
2. Limiting the number of historic entries per game
3. Using batch operations for database updates
4. Implementing efficient cleanup of old data

## Extending the Module

### Adding New Jackpot Types

To add a new type of jackpot (e.g., progressive jackpots):

1. Define the new jackpot type and its parameters
2. Create new entity classes if needed
3. Extend the existing services to support the new type
4. Update the monitoring and evaluation logic
5. Add API endpoints for the new jackpot type

### Implementing Custom Triggers

To implement custom jackpot triggers beyond random probability:

1. Define the trigger conditions (e.g., specific game events)
2. Create a new evaluator component for the custom triggers
3. Integrate with the existing monitoring infrastructure
4. Add configuration options for the new triggers
5. Update documentation and tests

### Integration with Other Modules

To integrate the jackpot module with other modules:

1. Define clear interfaces for inter-module communication
2. Use events for loose coupling between modules
3. Document the integration points
4. Add integration tests to verify correct behavior
