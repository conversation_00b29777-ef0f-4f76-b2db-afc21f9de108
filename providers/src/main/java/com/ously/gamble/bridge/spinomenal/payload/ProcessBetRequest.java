package com.ously.gamble.bridge.spinomenal.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class ProcessBetRequest {

    @JsonProperty(value = "GameToken", required = true)
    private String gameToken;

    @JsonProperty(value = "TimeStamp", required = true)
    private String timeStamp;

    @JsonProperty(value = "ProviderCode", required = true)
    private String ProviderCode;

    @JsonProperty(value = "GameCode", required = true)
    private String gameCode;

    @JsonProperty(value = "ExternalId", required = true)
    private String ExternalId;

    // md5([TIMESTAMP][TOKEN][PRIVATE_KEY])
    @JsonProperty(value = "Sig", required = true)
    private String sig;

    @JsonProperty(value = "TicketId", required = true)
    private Long ticketId;

    @JsonProperty(value = "RoundId", required = true)
    private Long roundId;

    @JsonProperty(value = "IsRoundFinish", required = true)
    private Boolean isRoundFinish;

    @JsonProperty(value = "TransactionType", required = true)
    private String transactionType;

    @JsonProperty(value = "TransactionDescription", required = true)
    private String transactionDescription;

    @JsonProperty(value = "BetAmount", required = true)
    private BigDecimal betAmount;

    @JsonProperty(value = "WinAmount", required = true)
    private BigDecimal winAmount;

    @JsonProperty(value = "RefTicketId", required = true)
    private String RefTicketId;

    public String getGameToken() {
        return gameToken;
    }

    public void setGameToken(String gameToken) {
        this.gameToken = gameToken;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getProviderCode() {
        return ProviderCode;
    }

    public void setProviderCode(String providerCode) {
        ProviderCode = providerCode;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public String getExternalId() {
        return ExternalId;
    }

    public void setExternalId(String externalId) {
        ExternalId = externalId;
    }


    public String getSig() {
        return sig;
    }

    public void setSig(String sig) {
        this.sig = sig;
    }

    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public Long getRoundId() {
        return roundId;
    }

    public void setRoundId(Long roundId) {
        this.roundId = roundId;
    }

    public Boolean getRoundFinish() {
        return isRoundFinish;
    }

    public void setRoundFinish(Boolean roundFinish) {
        isRoundFinish = roundFinish;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionDescription() {
        return transactionDescription;
    }

    public void setTransactionDescription(String transactionDescription) {
        this.transactionDescription = transactionDescription;
    }

    public BigDecimal getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(BigDecimal betAmount) {
        this.betAmount = betAmount;
    }

    public BigDecimal getWinAmount() {
        return winAmount;
    }

    public void setWinAmount(BigDecimal winAmount) {
        this.winAmount = winAmount;
    }

    public String getRefTicketId() {
        return RefTicketId;
    }

    public void setRefTicketId(String refTicketId) {
        RefTicketId = refTicketId;
    }

    public String getSigPart() {
        return this.timeStamp + this.gameToken;
    }

    @Override
    public String toString() {
        return "ProcessBetRequest{" +
                "gameToken='" + gameToken + '\'' +
                ", timeStamp='" + timeStamp + '\'' +
                ", ProviderCode='" + ProviderCode + '\'' +
                ", gameCode='" + gameCode + '\'' +
                ", ExternalId='" + ExternalId + '\'' +
                ", sig='" + sig + '\'' +
                ", ticketId=" + ticketId +
                ", roundId=" + roundId +
                ", isRoundFinish=" + isRoundFinish +
                ", transactionType='" + transactionType + '\'' +
                ", transactionDescription='" + transactionDescription + '\'' +
                ", betAmount=" + betAmount +
                ", winAmount=" + winAmount +
                ", RefTicketId='" + RefTicketId + '\'' +
                '}';
    }
}
