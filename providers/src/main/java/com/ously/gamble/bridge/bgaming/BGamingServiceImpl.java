package com.ously.gamble.bridge.bgaming;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.bridge.BridgeBase;
import com.ously.gamble.bridge.bgaming.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

@Service
@ConditionalOnBean(BGamingConfiguration.class)
public class BGamingServiceImpl extends BridgeBase implements BGamingService {
    static final BigDecimal MULT100 = BigDecimal.valueOf(100L);
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    BGamingConfiguration config;

    @Autowired
    FeatureConfig fConfig;

    @Autowired
    ObjectMapper om;

    @Autowired
    private RestTemplate bgRestTemplate;
    Mac hmacSHA256;

    @PostConstruct
    void setupTemplate() {
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getAuthToken().getBytes(StandardCharsets.UTF_8));
    }


    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform, GameSettings settings) {

        var sResp = createSession(user, game, platform, settings);
        var token = createUid(user.getId(), getCurrencyForBetlevel(settings.getBetLevel()));
        if (sResp != null) {
            var gameInstance = new GameInstance();
            gameInstance.setGameUrl(sResp.getLaunchOptions().getGameUrl());
            gameInstance.setToken(token);
            gameInstance.setAuthToken(token);
            gameInstance.setCreationTime(LocalDateTime.now());
            gameInstance.setExpiryTime(getExpiryTime());
            return gameInstance;
        }
        return null;
    }

    private String getCurrencyForBetlevel(int betLevel) {
        if (betLevel == 1) {
            return config.getCurrency2();
        }

        if (betLevel == 2) {
            return config.getCurrency3();
        }

        return config.getCurrency();
    }

    private BGCreateSessionResponse createSession(CasinoUser user, CasinoGame game,
                                                  GamePlatform platform, GameSettings settings) {
        try {
            var bgSReq = new BGCreateSessionRequest();
            bgSReq.setCurrency(getCurrencyForBetlevel(settings.getBetLevel()));
            bgSReq.setCasinoId(config.getCasinoId());
            bgSReq.setGame(game.getGameId());
            bgSReq.setClientType((platform == GamePlatform.MOBILE) ? "mobile" : "desktop");
            bgSReq.setIp(settings.getIp());
            bgSReq.setLocale(getLocaleFromSettings(settings));
            var wallet = getWallet(user.getId());
            bgSReq.setBalance(wallet.getBalance().multiply(MULT100).longValue());

            var uid = createUid(user.getId(), getCurrencyForBetlevel(settings.getBetLevel()));

            var bgUser = new BGUser();
            bgUser.setId(uid);
            bgUser.setCountry(settings.getUserCountry());
            bgSReq.setUser(bgUser);

            var bgUrls = new BGUrls();
            var baseUrl = fConfig.getBaseUrl();
            bgUrls.setDepositUrl(baseUrl + "/checkout");
            bgUrls.setReturnUrl(baseUrl + "/checkout");
            bgSReq.setUrls(bgUrls);

            var body = om.writeValueAsString(bgSReq);
            var headers = new HttpHeaders();
            headers.add("X-REQUEST-SIGN", createSignature(body));
            headers.add("Content-Type", "application/json");
            log.debug("Sending Session Create {}/{}", body, headers);

            var entity = new HttpEntity<>(body, headers);
            var respEnt = bgRestTemplate.postForEntity(config.getBaseurl() + "/sessions", entity, BGCreateSessionResponse.class);
            log.debug("Got {}", respEnt);
            return respEnt.getBody();
        } catch (Exception e) {
            log.error("Error creating session", e);
        }
        return null;
    }

    private static String createUid(Long id, final String currency) {
        return "BG-" + currency + '-' + id;
    }

    private synchronized String createSignature(String s) {
        return Hex.encodeHexString(hmacSHA256.doFinal(s.getBytes(StandardCharsets.UTF_8)), true);
    }

    private static String getLocaleFromSettings(GameSettings settings) {
        return settings.getLanguage().toLowerCase(Locale.ROOT);
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public BGPlayResponse play(BGPlayRequest req) {
        log.debug("BGPLAY:{}", req);

        var token = getToken(req.getUserId());
        if (token == null) {
            log.debug("BGPLAY-sessionNotFound:{}", req);
            return BGPlayResponse.error(101, "session not found", 412);
        }

        var w = getWallet(token.getUserId());

        if (req.getGameRound() == null || req.getActions() == null || req.getActions().isEmpty()) {
            var resp = new BGPlayResponse(w.getBalance().multiply(MULT100).longValue(), null);
            resp.setTransactions(null);
            log.debug("BGPLAY-Balance:{}", resp);
            return resp;
        }

        // now process win/bet/directwin
        var prAt = getCurrentTimeAsString();
        var saldo = 0L;
        for (var action : req.getActions()) {
            if ("bet".equalsIgnoreCase(action.getAction())) {
                saldo -= action.getAmount();
            }
        }

        if (saldo < 0L) {
            saldo = Math.abs(saldo);
            var blnc = w.getBalance().multiply(MULT100).longValue();
            log.debug("BGPLAY-SALDO NEGATIVE:{}/{}", saldo, blnc);
            if (saldo > blnc) {
                log.debug("BGPLAY-not-enough-funds");
                var not_enough_funds = BGPlayResponse.error(100, "not enough funds", 412);
                not_enough_funds.setBalance(w.getBalance().multiply(MULT100).longValue());
                return not_enough_funds;
            }
        }
        var resp = new BGPlayResponse();
        resp.setGameId(req.getGameRound());
        List<BGTransaction> transactions = new ArrayList<>(req.getActions().size());
        resp.setTransactions(transactions);
        for (var action : req.getActions()) {
            var txReq = new TxRequest(token);
            txReq.setVendorName(config.getVendorName());
            txReq.setRoundRef(req.getGameRound());
            if ("bet".equalsIgnoreCase(action.getAction())) {
                txReq.setType(TransactionType.BET);
                txReq.setBet(new BigDecimal(action.getAmount()).divide(MULT100, 2, RoundingMode.DOWN));
                txReq.setWin(BigDecimal.ZERO);
                txReq.setExternal_tx_ref(req.getGameRound() + "-BET-" + action.getActionId());
            } else {
                txReq.setType(TransactionType.WIN);
                txReq.setWin(new BigDecimal(action.getAmount()).divide(MULT100, 2, RoundingMode.DOWN));
                txReq.setBet(BigDecimal.ZERO);
                txReq.setExternal_tx_ref(req.getGameRound() + "-WIN-" + action.getActionId());
            }
            txReq.setExternalOrigId(req.getUserId() + ':' + action.getActionId());
            try {
                var txResp = addTxFromProvider(txReq);
                transactions.add(new BGTransaction(action.getActionId(), txResp.getTxId(), prAt));
                resp.setBalance(txResp.getNewBalance().multiply(MULT100).longValue());
            } catch (Exception e) {
                log.warn("BG-Play, Error:{}", e.getMessage(), e);
                return BGPlayResponse.error(500, "unknown error", 412);
            }
        }
        log.debug("BG-PLAY:{}", resp);
        return resp;
    }

    private static String getCurrentTimeAsString() {
        return LocalDateTime.now().atOffset(ZoneOffset.of("Z")).toString();
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public BGRollbackResponse rollback(BGRollbackRequest req) {
        log.debug("BGROLLBACK:{}", req);
        var token = getToken(req.getUserId());
        if (token == null) {
            log.debug("BGROLLBACK-sessionNotFound:{}", req);
            return BGRollbackResponse.error(101, "session not found");
        }

        var userId = token.getUserId();
        var prAt = getCurrentTimeAsString();
        var resp = new BGRollbackResponse();
        List<BGTransaction> transactions = new ArrayList<>(req.getActions().size());
        resp.setTransactions(transactions);
        resp.setGameId(req.getGameRound());
        for (var action : req.getActions()) {
            // 1. find tx
            var oldTx = findByUserIdAndVendorAndOrigId(userId, config.getVendorName(), req.getUserId() + ':' + action.getOriginalActionId());
            if (oldTx == null) {
                var bgt = new BGTransaction();
                bgt.setActionId(action.getActionId());
                bgt.setTxId(UUID.randomUUID().toString());
                bgt.setProcessedAt(prAt);
                transactions.add(bgt);
                continue;
            }
            if (oldTx.isCancelled()) {
                var bgt = new BGTransaction();
                bgt.setActionId(action.getActionId());
                bgt.setTxId(UUID.randomUUID().toString());
                bgt.setProcessedAt(prAt);
                transactions.add(bgt);
                continue;
            }
            // perf. Rollback
            var txResponse = performRollback(oldTx, getWallet(userId));
            var bgTx = new BGTransaction();
            bgTx.setActionId(action.getActionId());
            bgTx.setTxId(Long.toString(txResponse.getTxId()));
            bgTx.setProcessedAt(prAt);
            transactions.add(bgTx);
            resp.setBalance(txResponse.getNewBalance().multiply(MULT100).longValue());
        }
        if (resp.getBalance() == null) {
            resp.setBalance(getWallet(userId).getBalance().multiply(MULT100).longValue());
        }
        log.debug("BGROLLBACK-RESP:{}", resp);
        return resp;
    }
}
