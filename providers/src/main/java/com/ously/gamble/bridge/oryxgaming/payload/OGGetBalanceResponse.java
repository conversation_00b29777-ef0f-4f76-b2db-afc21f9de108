package com.ously.gamble.bridge.oryxgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class OGGetBalanceResponse extends OGBaseResponse {

    @Override
    public String toString() {
        return "OGGetBalanceResponse{" + super.toString() + '}';
    }
}
