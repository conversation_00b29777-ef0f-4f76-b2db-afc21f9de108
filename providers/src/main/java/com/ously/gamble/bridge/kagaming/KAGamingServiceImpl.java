package com.ously.gamble.bridge.kagaming;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.AvailableGame;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.kagaming.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

@Service
@ConditionalOnBean(KAGamingConfiguration.class)
public class KAGamingServiceImpl extends BridgeBaseV2 implements KAGamingService {

    private static final BigDecimal BD100 = BigDecimal.valueOf(100);
    private final KAGamingConfiguration config;
    private final RestTemplate apiTemplate;
    private final ObjectMapper om;
    private final Mac hmacSHA256;
    final Logger log = LoggerFactory.getLogger(KAGamingServiceImpl.class);

    public KAGamingServiceImpl(KAGamingConfiguration config, RestTemplate rTmpl, ObjectMapper om) {
        this.config = config;
        this.apiTemplate = rTmpl;
        this.om = om;
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getSecretKey().getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame (KA GAMING) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(config.getLaunchUrl());

            bld.addParameter("p", config.getOperatorCode());
            bld.addParameter("g", game.getGameId());
            bld.addParameter("u", "KA-" + user.getId());
            bld.addParameter("ak", config.getAccessKey());
            bld.addParameter("cr", config.getCurrency());
            bld.addParameter("t", token);
            bld.addParameter("l", "-1");
            if (platform == GamePlatform.IOS) {
                bld.addParameter("if", "1");
            }
            gameUrl = bld.build().toString();
        }
        var gameInstance = new GameInstance();
        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }


    @Override
    @Transactional
    public KAStartResponse start(KAStartRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new KAStartResponse(100, "token not found");
        }
        var resp = new KAStartResponse();
        resp.setCurrency(config.getCurrency());
        resp.setPlayerId("KA-" + token.getUserId());
        resp.setSessionId(req.getSessionId());
        resp.setBalance(getWallet(token.getUserId()).getBalance().multiply(BD100).longValue());
        return resp;
    }

    @Override
    @Transactional
    public KAPlayResponse play(KAPlayRequest req) {
        var token = getToken(req.getToken());

        if (token == null && req.getBetAmount() > 0) {
            return new KAPlayResponse(100, "token not found");
        }
        GamePlatform gp;
        long gameId;
        long sessionId;
        long userId;
        if (token == null) {
            var sessionByToken = findSessionByToken(req.getToken());
            if (sessionByToken.isEmpty()) {
                return new KAPlayResponse(100, "token not found");
            }
            userId = sessionByToken.get().getUserId();
            gameId = sessionByToken.get().getGameId();
            sessionId = sessionByToken.get().getSessionId();
            gp = sessionByToken.get().getPlatform();
        } else {
            userId = token.getUserId();
            gameId = token.getGameId();
            sessionId = token.getSessionId();
            gp = token.getGp();
        }

        // ok, now write txs

        var origTx = req.getTransactionId() + '-' + req.getRound();
        var round = req.getTransactionId();

        long betAmount = req.getBetAmount();
        if (req.getFreeGames()) {
            betAmount = 0L;
        }

        var roundMode = RoundMode.AUTO;

        var txr = (token == null) ? new TxRequest() : new TxRequest(token);
        if (betAmount > 0 && req.getWinAmount() > 0) {
            var bet = BigDecimal.valueOf(betAmount).divide(BD100, 2, RoundingMode.DOWN);
            var win = BigDecimal.valueOf(req.getWinAmount()).divide(BD100, 2, RoundingMode.DOWN);
            txr.setType(TransactionType.DIRECTWIN);
            txr.setRoundMode(roundMode);
            txr.setWin(win);
            txr.setBet(bet);
        } else if (betAmount > 0 && req.getWinAmount() == 0) {
            var bet = BigDecimal.valueOf(betAmount).divide(BD100, 2, RoundingMode.DOWN);
            txr.setType(TransactionType.BET);
            txr.setRoundMode(roundMode);
            txr.setBet(bet);
            txr.setWin(BigDecimal.ZERO);
        } else {
            var win = BigDecimal.valueOf(req.getWinAmount()).divide(BD100, 2, RoundingMode.DOWN);
            txr.setWin(win);
            txr.setBet(BigDecimal.ZERO);
            txr.setType(TransactionType.WIN);
            txr.setRoundMode(roundMode);
        }
        txr.setGp(gp);
        txr.setExternalOrigId(origTx);
        txr.setRoundRef(round);
        txr.setSessionId(sessionId);
        txr.setUserId(userId);
        txr.setGameId(gameId);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            if (e instanceof OuslyOutOfMoneyException) {
                return new KAPlayResponse(200, "not enough funds");
            }
            log.error("KA-play: Error doing deposit={}", e.getMessage());
            return new KAPlayResponse(1, "unknown error on play:" + e.getMessage());
        }
        var resp = new KAPlayResponse();
        resp.setBalance(txResponse.getNewBalance().multiply(BD100).longValue());
        return resp;
    }

    @Override
    @Transactional
    public KACreditResponse credit(KACreditRequest req) {
        var token = getToken(req.getToken());

        long userId;
        long sessionId;
        long gameId;
        GamePlatform gp;

        if (token == null) {
            var sessionByToken = findSessionByToken(req.getToken());
            if (sessionByToken.isEmpty()) {
                return new KACreditResponse(100, "token not found");
            }
            userId = sessionByToken.get().getUserId();
            gameId = sessionByToken.get().getGameId();
            sessionId = sessionByToken.get().getSessionId();
            gp = sessionByToken.get().getPlatform();
        } else {
            userId = token.getUserId();
            gameId = token.getGameId();
            sessionId = token.getSessionId();
            gp = token.getGp();
        }

        // ok, now write txs

        var origTx = req.getTransactionId() + '-' + req.getCreditIndex() + '-' + req.getType();
        var round = req.getTransactionId();

        var txr = (token == null) ? new TxRequest() : new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        var win = BigDecimal.valueOf(req.getAmount()).divide(BD100, 2, RoundingMode.DOWN);
        txr.setType(TransactionType.WIN);
        txr.setWin(win);
        txr.setBet(BigDecimal.ZERO);
        txr.setGp(gp);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(round);
        txr.setSessionId(sessionId);
        txr.setUserId(userId);
        txr.setGameId(gameId);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            if (e instanceof OuslyOutOfMoneyException) {
                return new KACreditResponse(200, "not enough funds");
            }
            log.error("KA-credit: Error doing credit={}", e.getMessage());
            return new KACreditResponse(1, "unknown error on credit:" + e.getMessage());
        }
        var resp = new KACreditResponse();
        resp.setBalance(txResponse.getNewBalance().multiply(BD100).longValue());
        return resp;
    }

    @Override
    @Transactional
    public KARevokeResponse revoke(KARevokeRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {

            var sessionByToken = findSessionByToken(req.getToken());
            if (sessionByToken.isEmpty()) {
                return new KARevokeResponse(100, "token not found");

            }
            token = new SessionCacheEntry();
            token.setUserId(sessionByToken.get().getUserId());
            token.setGameId(sessionByToken.get().getGameId());
            token.setSessionId(sessionByToken.get().getSessionId());
        }

        var origTx = req.getTransactionId() + '-' + req.getRound();
        var w = getWallet(token.getUserId());
        var resp = new KARevokeResponse();
        resp.setBalance(w.getBalance().multiply(BD100).longValue());

        var oldTx = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), origTx);
        if (oldTx != null && !oldTx.isCancelled()) {
            var txResponse = performRollback(oldTx, w);
            resp.setBalance(txResponse.getNewBalance().multiply(BD100).longValue());
        }
        return resp;
    }

    @Override
    @Transactional
    public KABalanceResponse balance(KABalanceRequest req) {
        var token = getToken(req.getToken());
        if (token == null) {
            return new KABalanceResponse(100, "token not found");
        }

        var resp = new KABalanceResponse();
        resp.setBalance(getWallet(token.getUserId()).getBalance().multiply(BD100).longValue());
        return resp;
    }

    @Override
    public KAEndResponse end(KAEndRequest req) {
        return new KAEndResponse();
    }

    @Override
    public boolean canRetrieveGames() {
        return true;
    }

    @Override
    public synchronized List<AvailableGame> retrieveGames() {
        try {
            var url = config.getApiUrl() + "/gameList"; //gameList?hash=f3017ee28140c10d902c2501a5239562f6aaf14d5ba0683ce0e4ad6886c49ff4

            var getGamesRequest = new KAApiGetGamesRequest(config);
            var req = om.writeValueAsString(getGamesRequest);
            var bytes = hmacSHA256.doFinal(req.getBytes(StandardCharsets.UTF_8));
            var hash = Hex.encodeHexString(bytes).toLowerCase(Locale.ROOT);

            var uri = UriComponentsBuilder
                    .fromHttpUrl(url)
                    .queryParam("hash", hash)
                    .build()
                    .toUri();

            log.debug("KA Sending gamelist req ('{}') body:'{}'", uri, req);

            var getGamesEntity = apiTemplate.postForEntity(uri, req, String.class);
            if (getGamesEntity.getStatusCode().value() == 200) {
                log.debug("Got:{}", getGamesEntity.getBody());
                var gamelist = om.readValue(getGamesEntity.getBody(), KAApiGetGamesResponse.class);
                List<AvailableGame> lag = new ArrayList<>();
                gamelist.getGames().forEach(a -> {
                    var ag = new AvailableGame();
                    ag.setProviderName(config.getVendorName());
                    ag.setDesktop(true);
                    ag.setMobile(true);
                    ag.setGameId(a.getGameId());
                    ag.setName(a.getGameName());
                    var rawData = "{}";
                    try {
                        rawData = getObjectMapper().writeValueAsString(a);
                    } catch (Exception ignored) {
                    }
                    ag.setRawInfo(rawData);
                    ag.setLogoCandidate(a.getIconURLPrefix() + "&type=square");
                    lag.add(ag);
                });
                return lag;
            }

        } catch (Exception e) {
            log.error("Error loading gamelist from vendor KA Gaming", e);
        }
        return new ArrayList<>();
    }


}
