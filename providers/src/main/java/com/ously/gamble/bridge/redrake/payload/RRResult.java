package com.ously.gamble.bridge.redrake.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class RRResult {

    RRStatus status;
    RRResponse response;

    public RRResult() {
    }

    public RRResult(int code, String msg) {
        this.status = new RRStatus(code, msg);
        this.response = new RRResponse();
    }

    public RRResult(BigDecimal balance) {
        this.response = new RRResponse(balance);
        this.status = new RRStatus(0, null);
    }

    public RRStatus getStatus() {
        return status;
    }

    public void setStatus(RRStatus status) {
        this.status = status;
    }

    public RRResponse getResponse() {
        return response;
    }

    public void setResponse(RRResponse response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "RRResult{" +
                "status=" + status +
                ", response=" + response +
                '}';
    }
}
