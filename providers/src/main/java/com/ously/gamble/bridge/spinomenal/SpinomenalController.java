package com.ously.gamble.bridge.spinomenal;


import com.ously.gamble.bridge.spinomenal.payload.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.*;


/**
 * The controller class for all spinomenal requests.
 */
@RestController
@RequestMapping(SpinomenalConfiguration.BRIDGE_SPINOMENAL)
@ConditionalOnBean(SpinomenalConfiguration.class)
public class SpinomenalController {

    @Autowired
    SpinomenalService sSrv;

    @PostMapping("/playerBalance")
    @ResponseBody
    public PlayerBalanceResponse getPlayerBalance(@RequestBody PlayerBalanceRequest pbr) {
        return sSrv.getPlayerBalance(pbr);
    }

    @PostMapping("/authenticate")
    @ResponseBody
    public AuthenticationResponse authenticate(@RequestBody AuthenticationRequest req) {
        return sSrv.authenticate(req);
    }

    @PostMapping("/processBet")
    @ResponseBody
    public ProcessBetResponse processBet(@RequestBody ProcessBetRequest req) {
        return sSrv.processBet(req);
    }


}
