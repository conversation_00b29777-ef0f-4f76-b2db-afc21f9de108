package com.ously.gamble.bridge.everymatrix;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.everymatrix.payload.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(EverymatrixConfiguration.BRIDGE_EVERYMATRIX)
@ConditionalOnBean(EverymatrixConfiguration.class)
public class EverymatrixController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    EverymatrixConfiguration config;

    @Autowired
    EverymatrixService service;

    @Autowired
    ObjectMapper om;

    @PostMapping("/callback")
    @ResponseBody
    public ResponseEntity<? extends EMResponse> callback(@RequestBody String req) {
        log.debug("SPHD: getRequest:{}", req);
        try {
            var jsonNode = om.readTree(req);
            var request = jsonNode.get("Request");

            if (request != null) {
                // Can we instantiate a class from a jsonNode ?
                var resp = switch (request.textValue()) {
                    case "GetAccount" -> getAccount(om.readValue(req, EMGetAccountRequest.class));
                    case "GetBalance" -> getBalance(om.readValue(req, EMGetBalanceRequest.class));
                    case "WalletDebit" -> debit(om.readValue(req, EMTransactionRequest.class));
                    case "WalletCredit" -> credit(om.readValue(req, EMTransactionRequest.class));
                    default -> ResponseEntity.ok(new EMResponse(request.textValue(), 101, "unknown request type"));
                };
                if (log.isTraceEnabled()) {
                    log.trace("Response:{}", om.writeValueAsString(resp));
                }
                return resp;

            }
            return ResponseEntity.ok(new EMResponse("", 101, "missing request type"));
        } catch (Exception e) {
            log.debug("SPHD: Error on callback:{}", req, e);
            return ResponseEntity.ok(new EMResponse("", 101, "unknown error"));
        }
    }


    private ResponseEntity<EMGetAccountResponse> getAccount(EMGetAccountRequest req) {
        log.debug("SPHD: getAccount-REQ:{}", req);
        var account = service.getAccount(req);
        log.debug("SPHD: getAccount-RESP:{}", account);
        return ResponseEntity.ok(account);
    }

    private ResponseEntity<EMGetBalanceResponse> getBalance(EMGetBalanceRequest req) {
        log.debug("SPHD: getBalance-REQ:{}", req);
        var balance = service.getBalance(req);
        log.debug("SPHD: getBalance-RESP:{}", balance);
        return ResponseEntity.ok(balance);
    }

    private ResponseEntity<EMTransactionResponse> debit(EMTransactionRequest req) {
        log.debug("SPHD: debit-REQ:{}", req);
        var debitResp = service.debit(req);
        log.debug("SPHD: debit-RESP:{}", debitResp);
        return ResponseEntity.ok(debitResp);
    }

    private ResponseEntity<EMTransactionResponse> credit(EMTransactionRequest req) {
        log.debug("SPHD: credit-REQ:{}", req);
        var creditResp = service.credit(req);
        log.debug("SPHD: credit-RESP:{}", creditResp);
        return ResponseEntity.ok(creditResp);
    }


}
