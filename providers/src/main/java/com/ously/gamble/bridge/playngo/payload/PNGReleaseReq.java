package com.ously.gamble.bridge.playngo.payload;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serial;

@XmlRootElement(name = "release")
@XmlAccessorType(XmlAccessType.FIELD)
public class PNGReleaseReq extends PNGSessionRequest {
    @Serial
    private static final long serialVersionUID = 1L;


    String transactionId;

    @XmlElement(name = "real")
    String amount;

    String gameSessionId;

    String contextId;

    String roundId;

    String gameMode;

    String channel;

    String freegameExternalId;

    String actualValue;

    String state;

    String totalLoss;

    String totalGain;

    Integer numRounds;

    String type;


    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getGameSessionId() {
        return gameSessionId;
    }

    public void setGameSessionId(String gameSessionId) {
        this.gameSessionId = gameSessionId;
    }

    @Override
    public String getContextId() {
        return contextId;
    }

    @Override
    public void setContextId(String contextId) {
        this.contextId = contextId;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getGameMode() {
        return gameMode;
    }

    public void setGameMode(String gameMode) {
        this.gameMode = gameMode;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getFreegameExternalId() {
        return freegameExternalId;
    }

    public void setFreegameExternalId(String freegameExternalId) {
        this.freegameExternalId = freegameExternalId;
    }

    public String getActualValue() {
        return actualValue;
    }

    public void setActualValue(String actualValue) {
        this.actualValue = actualValue;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getTotalLoss() {
        return totalLoss;
    }

    public void setTotalLoss(String totalLoss) {
        this.totalLoss = totalLoss;
    }

    public String getTotalGain() {
        return totalGain;
    }

    public void setTotalGain(String totalGain) {
        this.totalGain = totalGain;
    }

    public Integer getNumRounds() {
        return numRounds;
    }

    public void setNumRounds(Integer numRounds) {
        this.numRounds = numRounds;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    @Override
    public String toString() {
        return "PNGReleaseReq{" +
                "transactionId='" + transactionId + '\'' +
                ", amount='" + amount + '\'' +
                ", gameSessionId='" + gameSessionId + '\'' +
                ", contextId='" + contextId + '\'' +
                ", roundId='" + roundId + '\'' +
                ", gameMode='" + gameMode + '\'' +
                ", channel='" + channel + '\'' +
                ", freegameExternalId='" + freegameExternalId + '\'' +
                ", actualValue='" + actualValue + '\'' +
                ", state='" + state + '\'' +
                ", totalLoss='" + totalLoss + '\'' +
                ", totalGain='" + totalGain + '\'' +
                ", numRounds=" + numRounds +
                ", type='" + type + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", gameId='" + gameId + '\'' +
                ", productId='" + productId + '\'' +
                ", clientIP='" + clientIP + '\'' +
                ", contextId='" + contextId + '\'' +
                ", externalId='" + externalId + '\'' +
                ", currency='" + currency + '\'' +
                ", externalGameSessionId='" + externalGameSessionId + '\'' +
                "} ";
    }
}
