package com.ously.gamble.bridge.zeusV3.exception;

import java.math.BigDecimal;

public class InsufficientFundsException extends ZeusV3Exception {

    private static final String DEFAULT_MESSAGE = "Insufficient funds";
    private final BigDecimal balance;

    public InsufficientFundsException(BigDecimal balance) {
        super(DEFAULT_MESSAGE);
        this.balance = balance;
    }

    public BigDecimal getBalance() {
        return balance;
    }
}
