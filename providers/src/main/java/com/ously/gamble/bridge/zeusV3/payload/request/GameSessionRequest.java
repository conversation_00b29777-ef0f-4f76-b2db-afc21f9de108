package com.ously.gamble.bridge.zeusV3.payload.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * This is not record but class because we might need
 * agility while building a proper request.
 *
 * This is OUR request TO zeus.
 */
@JsonInclude(Include.NON_NULL)
public class GameSessionRequest implements ZeusRequest {

    // we force to fill in fields that are required by Zeus API
    public GameSessionRequest(String partnerPlayerId, String gameCode, String currencyCode) {
        this.partnerPlayerId = partnerPlayerId;
        this.gameCode = gameCode;
        this.currencyCode = currencyCode;
    }

    @JsonProperty("partnerPlayerId")
    private String partnerPlayerId;

    @JsonProperty("gameCode")
    private String gameCode;

    @JsonProperty("currencyCode")
    private String currencyCode;

    @JsonProperty("lang")
    private String lang = "en";

    // we use only real game mode
    @JsonProperty("playMode")
    private String playMode = "real";

    /**
     * If the partner wants, he can declare home url. If not null, then
     * game will show a button that will point to this url.
     */
    @JsonProperty("homeBtnUrl")
    private String homeBtnUrl;

    /**
     * If the partner wants, he can declare home post message. If not null, then the game will
     * show a button that will post a message to parent window. Can be used when the partner
     * wants to be in control to close out the game
     */
    @JsonProperty("homeBtnPostMessage")
    private String homeBtnPostMessage;

    /**
     * If the partner wants, he can declare fullScreenBtn. If true, then
     * the game will show a button that will perform full screen mode.
     */
    @JsonProperty("fullScreenBtn")
    private Boolean fullScreenBtn = false;

    @JsonProperty("api")
    private ApiProperties api = new ApiProperties();

    public String getPartnerPlayerId() {
        return partnerPlayerId;
    }

    public String getGameCode() {
        return gameCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getPlayMode() {
        return playMode;
    }

    public String getHomeBtnUrl() {
        return homeBtnUrl;
    }

    public void setHomeBtnUrl(String homeBtnUrl) {
        this.homeBtnUrl = homeBtnUrl;
    }

    public String getHomeBtnPostMessage() {
        return homeBtnPostMessage;
    }

    public void setHomeBtnPostMessage(String homeBtnPostMessage) {
        this.homeBtnPostMessage = homeBtnPostMessage;
    }

    public Boolean getFullScreenBtn() {
        return fullScreenBtn;
    }

    public void setFullScreenBtn(Boolean fullScreenBtn) {
        this.fullScreenBtn = fullScreenBtn;
    }

    public ApiProperties getApiProperties() {
        return api;
    }

    public void setApiProperties(ApiProperties api) {
        this.api = api;
    }

    @JsonInclude(Include.NON_NULL)
    public record ApiProperties(
        /*
          If true, API will use Game Round request or else will use Bet and Win requests.
          We strongly recommend to use the Game Round request for simplicity and performance reasons.
         */
        @JsonProperty("useGameRound")
        Boolean useGameRound,

        /*
          If true and API uses Bet and Win requests (UseGameRound = false) then always will send
          Win Requests (even if win equals to zero) else will send Win Request only when have win.
          In case of false, Bet Request may have sessionRound.mainClosed = true when no win.
         */
        @JsonProperty("alwaysWinRequest")
        Boolean alwaysWinRequest
    ) {
        public ApiProperties() {
            this(true, false);
        }
    }

    @Override
    public String toString() {
        return "GameSessionRequest{" +
                "partnerPlayerId='" + partnerPlayerId + '\'' +
                ", gameCode='" + gameCode + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", lang='" + lang + '\'' +
                ", playMode='" + playMode + '\'' +
                ", homeBtnUrl='" + homeBtnUrl + '\'' +
                ", homeBtnPostMessage='" + homeBtnPostMessage + '\'' +
                ", fullScreenBtn=" + fullScreenBtn +
                ", api=" + api.toString() +
                "}";
    }
}
