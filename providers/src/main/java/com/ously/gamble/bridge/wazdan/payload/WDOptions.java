package com.ously.gamble.bridge.wazdan.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class WDOptions {
    private Integer freeSpinsMode;
    private Integer freeRoundsMode;
    private Integer tokenExpiration;
    private Integer postTimeout;

    public Integer getFreeSpinsMode() {
        return freeSpinsMode;
    }

    public void setFreeSpinsMode(Integer freeSpinsMode) {
        this.freeSpinsMode = freeSpinsMode;
    }

    public Integer getFreeRoundsMode() {
        return freeRoundsMode;
    }

    public void setFreeRoundsMode(Integer freeRoundsMode) {
        this.freeRoundsMode = freeRoundsMode;
    }

    public Integer getTokenExpiration() {
        return tokenExpiration;
    }

    public void setTokenExpiration(Integer tokenExpiration) {
        this.tokenExpiration = tokenExpiration;
    }

    public Integer getPostTimeout() {
        return postTimeout;
    }

    public void setPostTimeout(Integer postTimeout) {
        this.postTimeout = postTimeout;
    }

    @Override
    public String toString() {
        return "WDOptions{" +
                "freeSpinsMode=" + freeSpinsMode +
                ", freeRoundsMode=" + freeRoundsMode +
                ", tokenExpiration=" + tokenExpiration +
                ", postTimeout=" + postTimeout +
                '}';
    }
}
