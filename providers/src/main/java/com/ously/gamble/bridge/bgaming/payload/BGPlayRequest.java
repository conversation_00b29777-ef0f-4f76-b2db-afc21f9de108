package com.ously.gamble.bridge.bgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BGPlayRequest {

    @JsonProperty("user_id")
    String userId;
    @JsonProperty("currency")
    String currency;
    @JsonProperty("game")
    String gameKey;
    @JsonProperty("game_id")
    String gameRound;
    @JsonProperty("finished")
    Boolean finished = false;

    @JsonProperty("actions")
    List<BGAction> actions;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getGameKey() {
        return gameKey;
    }

    public void setGameKey(String gameKey) {
        this.gameKey = gameKey;
    }

    public String getGameRound() {
        return gameRound;
    }

    public void setGameRound(String gameRound) {
        this.gameRound = gameRound;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    public List<BGAction> getActions() {
        return actions;
    }

    public void setActions(List<BGAction> actions) {
        this.actions = actions;
    }

    @Override
    public String toString() {
        return "BGPlayRequest{" +
                "userId='" + userId + '\'' +
                ", currency='" + currency + '\'' +
                ", gameKey='" + gameKey + '\'' +
                ", gameRound='" + gameRound + '\'' +
                ", finished=" + finished +
                ", actions=" + actions +
                '}';
    }
}
