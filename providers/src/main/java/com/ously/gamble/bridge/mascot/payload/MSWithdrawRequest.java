package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MSWithdrawRequest extends MSDepositRequest {
    String bonusId;
    Long chargeFreerounds;

    public String getBonusId() {
        return bonusId;
    }

    public void setBonusId(String bonusId) {
        this.bonusId = bonusId;
    }

    public Long getChargeFreerounds() {
        return chargeFreerounds;
    }

    public void setChargeFreerounds(Long chargeFreerounds) {
        this.chargeFreerounds = chargeFreerounds;
    }

    @Override
    public String toString() {
        return "MSWithdrawRequest{" +
                "transactionRef='" + transactionRef + '\'' +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", gameRoundRef='" + gameRoundRef + '\'' +
                ", source='" + source + '\'' +
                ", reason='" + reason + '\'' +
                ", callerId=" + callerId +
                ", playerName='" + playerName + '\'' +
                ", gameId='" + gameId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", sessionAlternativeId='" + sessionAlternativeId + '\'' +
                ", bonusId='" + bonusId + '\'' +
                ", chargeFreerounds=" + chargeFreerounds +
                '}';
    }
}
