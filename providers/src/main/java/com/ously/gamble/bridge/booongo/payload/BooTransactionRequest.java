package com.ously.gamble.bridge.booongo.payload;

public class BooTransactionRequest extends BooRequest {

    BooTransaction args;


    public BooTransaction getArgs() {
        return args;
    }

    public void setArgs(BooTransaction args) {
        this.args = args;
    }

    @Override
    public String toString() {
        return "BooTransactionRequest{" +
                "args=" + args +
                "} " + super.toString();
    }
}
