package com.ously.gamble.bridge.wazdan.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.persistence.model.Wallet;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class WDFunds {
    private Double balance;
    private Double bonus;
    private Boolean cashFirst;

    public WDFunds() {
    }

    public WDFunds(Wallet w) {
        this.balance = w.getBalance().doubleValue();
        this.bonus = null;
        this.cashFirst = null;
    }

    public WDFunds(Double d) {
        this.balance = d;
        this.bonus = null;
        this.cashFirst = null;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Double getBonus() {
        return bonus;
    }

    public void setBonus(Double bonus) {
        this.bonus = bonus;
    }

    public Boolean getCashFirst() {
        return cashFirst;
    }

    public void setCashFirst(Boolean cashFirst) {
        this.cashFirst = cashFirst;
    }

    @Override
    public String toString() {
        return "Funds{" +
                "balance=" + balance +
                ", bonus=" + bonus +
                ", cashFirst=" + cashFirst +
                '}';
    }
}
