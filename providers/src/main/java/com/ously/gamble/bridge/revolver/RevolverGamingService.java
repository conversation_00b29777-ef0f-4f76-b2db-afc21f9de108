package com.ously.gamble.bridge.revolver;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.revolver.payload.*;

public interface RevolverGamingService extends BridgeHandler {

    RVResponse auth(RVAuthRequest req);

    RVResponse balance(RVBalanceRequest req);

    RVResponse debit(RVTransactionRequest req) throws RVException;

    RVResponse credit(RVTransactionRequest req) throws RVException;

    RVResponse rollback(RVTransactionRequest req);

    RVResponse debitAndCredit(RVTransactionRequest req) throws RVException;

    boolean checkSignature(RVRequest pbr);
}
