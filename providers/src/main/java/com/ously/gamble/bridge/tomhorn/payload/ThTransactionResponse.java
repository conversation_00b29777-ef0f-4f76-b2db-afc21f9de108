package com.ously.gamble.bridge.tomhorn.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class ThTransactionResponse extends ThResponse {

    @JsonProperty("Transaction")
    ThBalance transaction;

    public ThTransactionResponse() {
    }

    public ThTransactionResponse(long code, String msg, BigDecimal amount, String currency, long id) {
        super(code, msg);
        this.transaction = new ThBalance(amount, currency, id);
    }

    public ThBalance getTransaction() {
        return transaction;
    }

    public void setTransaction(ThBalance transaction) {
        this.transaction = transaction;
    }

    @Override
    public String toString() {
        return "ThTransactionResponse{" +
                "transaction=" + transaction +
                ", code=" + code +
                ", message='" + message + '\'' +
                "} ";
    }
}
