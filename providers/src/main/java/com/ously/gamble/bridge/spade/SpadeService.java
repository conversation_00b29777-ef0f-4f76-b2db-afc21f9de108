package com.ously.gamble.bridge.spade;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.spade.payload.SPAuthRequest;
import com.ously.gamble.bridge.spade.payload.SPBalanceRequest;
import com.ously.gamble.bridge.spade.payload.SPResponse;
import com.ously.gamble.bridge.spade.payload.SPTransferRequest;

public interface SpadeService extends BridgeHandler {

    int CODE_SUCCESS = 0;
    int CODE_SYSTEM_ERROR = 1;
    int CODE_INVALID_REQUEST = 2;
    int CODE_ACCTID_INVALID = 113;
    int CODE_REF_NOT_FOUND = 109;
    int CODE_MERCHANT_NOT_FOUND = 10113;
    int CODE_INVALID_FORMAT = 118;
    int CODE_TOKEN_VALIDATION_FAILED = 50104;
    int CODE_INSUFFICIENT_BALANCE = 50110;
    int CODE_AMOUNT_INVALID = 50113;
    int CODE_PASSWORD_INVALID = 10104;

    int TYPE_BET = 1;
    int TYPE_CANCEL_BET = 2;
    int TYPE_PAYOUT = 4;
    int TYPE_BONUS = 7;

    SPResponse auth(SPAuthRequest req);

    SPResponse transfer(SPTransferRequest req);

    SPResponse balance(SPBalanceRequest req);

}
