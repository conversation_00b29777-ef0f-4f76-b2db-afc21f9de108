package com.ously.gamble.bridge.oryxgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class OGAuthenticateResponse extends OGBaseResponse {
    private String playerId;
    private String playerUsername;
    private String currencyCode;
    private String languageCode;
    private String nickname;
    private String sessionToken;

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getPlayerUsername() {
        return playerUsername;
    }

    public void setPlayerUsername(String playerUsername) {
        this.playerUsername = playerUsername;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    @Override
    public String toString() {
        return "OGAuthenticateResponse{" +
                super.toString() + ", " +
                "playerId='" + playerId + '\'' +
                ", sessionToken='" + sessionToken + '\'' +
                ", playerUsername='" + playerUsername + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", nickname='" + nickname + '\'' +
                '}';
    }
}
