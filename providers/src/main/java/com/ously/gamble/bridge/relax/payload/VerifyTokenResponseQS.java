package com.ously.gamble.bridge.relax.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class VerifyTokenResponseQS {
    @JsonProperty(value = "customerid", required = true)
    private String customerId;

    @JsonProperty(value = "countrycode", required = true)
    private String countryCode;

    @JsonProperty(value = "cashiertoken", required = true)
    private String cashierToken;

    @JsonProperty(value = "customercurrency", required = true)
    private String customerCurrency;

    @JsonProperty(value = "balance", required = true)
    private Long balance;

    @JsonProperty(value = "jurisdiction", required = true)
    private String jurisdiction;

    @JsonProperty(value = "classification", required = true)
    private String classification;

    @JsonProperty(value = "playermessage", required = true)
    List<PlayerMessage> playerMessages;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCashierToken() {
        return cashierToken;
    }

    public void setCashierToken(String cashierToken) {
        this.cashierToken = cashierToken;
    }

    public String getCustomerCurrency() {
        return customerCurrency;
    }

    public void setCustomerCurrency(String customerCurrency) {
        this.customerCurrency = customerCurrency;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public String getJurisdiction() {
        return jurisdiction;
    }

    public void setJurisdiction(String jurisdiction) {
        this.jurisdiction = jurisdiction;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public List<PlayerMessage> getPlayerMessages() {
        return playerMessages;
    }

    public void setPlayerMessages(List<PlayerMessage> playerMessages) {
        this.playerMessages = playerMessages;
    }

    @JsonAutoDetect(fieldVisibility = Visibility.ANY)
    @JsonInclude(Include.NON_NULL)
    public static class PlayerMessage {

        public PlayerMessage(String title, String message, boolean nonIntrusive) {
            this.title = title;
            this.message = message;
            this.nonIntrusive = nonIntrusive;
        }

        @JsonProperty(value = "title", required = true)
        private String title;

        @JsonProperty(value = "message", required = true)
        private String message;

        @JsonProperty(value = "nonIntrusive", required = true)
        private boolean nonIntrusive;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public boolean isNonIntrusive() {
            return nonIntrusive;
        }

        public void setNonIntrusive(boolean nonIntrusive) {
            this.nonIntrusive = nonIntrusive;
        }
    }

    @Override
    public String toString() {
        return "VerifyTokenResponseQS{" +
                "customerId='" + customerId + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", cashierToken='" + cashierToken + '\'' +
                ", customerCurrency='" + customerCurrency + '\'' +
                ", balance=" + balance +
                ", jurisdiction='" + jurisdiction + '\'' +
                ", classification='" + classification + '\'' +
                '}';
    }
}
