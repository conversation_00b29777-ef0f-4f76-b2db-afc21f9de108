package com.ously.gamble.bridge.playson.payload;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "balance")
public class PSBalance {
    @XmlAttribute(name = "currency", required = true)
    String currency;
    @XmlAttribute(name = "type", required = true)
    String type;
    @XmlAttribute(name = "value", required = true)
    Long value;
    @XmlAttribute(name = "version", required = true)
    Long version;

    public PSBalance() {
    }

    public PSBalance(Long value, Long version, String currency, String type) {
        this.value = value;
        this.currency = currency;
        this.version = version;
        this.type = type;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "PSBalance{" +
                "currency='" + currency + '\'' +
                ", type='" + type + '\'' +
                ", value=" + value +
                ", version=" + version +
                '}';
    }
}
