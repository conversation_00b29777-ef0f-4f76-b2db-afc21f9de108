package com.ously.gamble.bridge.softswiss.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class SSLaunchRequest {

    @JsonProperty("casino_id")
    String casinoId;
    @JsonProperty("client_type")
    String clientType;
    String game;
    String ip;
    String jurisdiction;
    String locale;
    SSPlayer player;
    @JsonProperty("session_payload")
    String sessionPayload;
    Map<String, String> urls;


    public String getCasinoId() {
        return casinoId;
    }

    public void setCasinoId(String casinoId) {
        this.casinoId = casinoId;
    }

    public SSPlayer getPlayer() {
        return player;
    }

    public void setPlayer(SSPlayer player) {
        this.player = player;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getGame() {
        return game;
    }

    public void setGame(String game) {
        this.game = game;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getJurisdiction() {
        return jurisdiction;
    }

    public void setJurisdiction(String jurisdiction) {
        this.jurisdiction = jurisdiction;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getSessionPayload() {
        return sessionPayload;
    }

    public void setSessionPayload(String sessionPayload) {
        this.sessionPayload = sessionPayload;
    }

    public Map<String, String> getUrls() {
        return urls;
    }

    public void setUrls(Map<String, String> urls) {
        this.urls = urls;
    }
}
