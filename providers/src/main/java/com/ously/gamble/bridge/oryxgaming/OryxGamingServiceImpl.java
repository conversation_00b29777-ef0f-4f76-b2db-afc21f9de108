package com.ously.gamble.bridge.oryxgaming;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.OuslyTxAlreadyCancelledException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.oryxgaming.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static com.ously.gamble.persistence.model.TransactionType.*;


@Service
@ConditionalOnBean(OryxGamingConfiguration.class)
public class OryxGamingServiceImpl extends BridgeBaseV2 implements OryxGamingService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static final BigDecimal AMOUNT_MULTIPLIER = new BigDecimal(100);

    @Autowired
    OryxGamingConfiguration config;

    @Override
    @Transactional
    public OGAuthenticateResponse authenticate(OGAuthenticateRequest request,
                                               String token) throws OGError {
        // check expiry,availability
        log.debug("AUTH_REQ:{}, token={}", request, token);

        var optSession = findByAuthToken(token);

        if (optSession.isEmpty()) {
            throw new OGError("TOKEN_NOT_FOUND", "the given token is unknown:" + token, HttpStatus.NOT_FOUND.value());
        }
        try {
            var byTokenAndActive = findByTokenAndActive(optSession.get().getToken(), true);
            if (byTokenAndActive != null && byTokenAndActive.getExpiryTime().isBefore(Instant.now())) {
                throw new OGError("TOKEN_NOT_VALID", "the given token has expired:" + token, HttpStatus.NOT_FOUND.value());
            }
        } catch (Exception e) {
            //
        }
        var session = optSession.get();
        // ok, token valid!
        var resp = new OGAuthenticateResponse();
        // now load user & wallet and return
        var w = getWallet(session.getUserId());
        resp.setBalance(w.getBalance().multiply(AMOUNT_MULTIPLIER).longValue());
        resp.setPlayerId("UID:" + session.getUserId());
        var user = getCasinoUser(session.getUserId());
        var dpName = StringUtils.abbreviate(user.getDisplayName(), 32);
        resp.setNickname(dpName);
        resp.setPlayerUsername(dpName);
        resp.setCurrencyCode(config.getActiveCurrency());
        resp.setSessionToken(session.getToken());
        resp.setResponseCode("OK");
        log.debug("AUTH_RESPONSE:{}", resp);
        return resp;
    }

    @Override
    @Transactional
    public OGGetBalanceResponse getBalance(OGGetBalanceRequest request,
                                           String playerId) throws OGError {
        log.debug("ORYX_GETBAL_REQ:{}, token={}", request, playerId);
        var gbe = getToken(request.getSessionToken());
        checkUserAndToken(gbe, request.getSessionToken());
        // ok, token valid!
        var resp = new OGGetBalanceResponse();
        // now load user & wallet and return
        var w = getWallet(gbe.getWalletId());
        resp.setBalance(w.getBalance().multiply(AMOUNT_MULTIPLIER).longValue());
        resp.setResponseCode("OK");
        log.debug("GETBAL_RESPONSE:{}", resp);
        return resp;
    }

    private static void checkUserAndToken(SessionCacheEntry gbe, String token) throws OGError {
        if (gbe == null) {
            throw new OGError("TOKEN_NOT_FOUND", "the given token is unknown:" + token, HttpStatus.NOT_FOUND.value());
        }
        if (gbe.getExpiry().isBefore(Instant.now())) {
            throw new OGError("TOKEN_NOT_VALID", "the given token has expired:" + token, HttpStatus.NOT_FOUND.value());
        }
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150, multiplier = 2, maxDelay = 2000), maxAttempts = 4)
    public OGGameTransactionResponse gameTransaction(
            OGGameTransactionRequest request) throws OGError {
        log.debug("ORYX_TX_REQ:{}, token={}", request, request.getPlayerId());
        var gbe = getToken(request.getSessionToken());
        checkUserAndToken(gbe, request.getSessionToken());
        // ok, token valid! now check bet/win

        var txReq = new TxRequest(gbe);
        if (request.getRoundAction() == null) {
            // game provider does not send RoundAction, so we openclose
            txReq.setRoundMode(RoundMode.OPENCLOSE);
        } else if ("CLOSE".equals(request.getRoundAction())) {
            txReq.setRoundMode(RoundMode.CLOSE);
        } else {
            txReq.setRoundMode(RoundMode.OPEN);
        }
        txReq.setRoundRef(request.getRoundId());

        // prepare normal transaction
        var bet = BigDecimal.ZERO;
        var win = BigDecimal.ZERO;

        // oryx sends transaction where bet & win == null !!
        // most often with roundAction = CLOSE!
        if (request.getBet() == null && request.getWin() == null) {

            if ("CANCEL".equals(request.getRoundAction())) {

                // find any transactions for that roundId, return ROUND_NOT_FOUND when no round availble
                var roundId = request.getRoundId();

                var txsToCancel = findRoundTransactionsForUserIdAndSessionIdAndRoundRef(gbe.getUserId(), gbe.getSessionId(), roundId);

                if (txsToCancel.isEmpty()) {
                    // NO ROUND FOUND
                    throw new OGError("ROUND_NOT_FOUND", "no booked txs for given roundId:" + roundId, 404);
                }
                var one = getWallet(gbe.getUserId());
                // cancel and return last resp!
                TxResponse txResponse = null;
                for (var tx : txsToCancel) {
                    txResponse = performRollback(tx, one);
                }
                if (txResponse == null) {
                    throw new OGError("ROUND_NOT_FOUND", "no booked txs for given roundId:" + roundId, 404);
                }
                var resp = new OGGameTransactionResponse();
                resp.setBalance(txResponse.getNewBalance().multiply(AMOUNT_MULTIPLIER).longValue());
                resp.setResponseCode("OK");
                log.debug("ORYX_TX_RESP-ign.transaction: {}", resp);
                return resp;
            }

            if (bet.longValue() == 0 && win.longValue() == 0) {
                // just return wallet amount
                var one = getWallet(gbe.getUserId());

                var resp = new OGGameTransactionResponse();
                resp.setBalance(one.getBalance().multiply(AMOUNT_MULTIPLIER).longValue());
                resp.setResponseCode("OK");
                log.debug("ORYX_TX_RESP-ign.transaction: {}", resp);
                return resp;
            }
        }

        if (request.getBet() != null) {
            bet = BigDecimal.valueOf(request.getBet().getAmount()).divide(AMOUNT_MULTIPLIER, 2, RoundingMode.DOWN);
        }
        if (request.getWin() != null) {
            win = BigDecimal.valueOf(request.getWin().getAmount()).divide(AMOUNT_MULTIPLIER, 2, RoundingMode.DOWN);
            var jackpot = BigDecimal.ZERO;
            if (request.getWin().getJackpotAmount() != null) {
                jackpot = BigDecimal.valueOf(request.getWin().getJackpotAmount()).divide(AMOUNT_MULTIPLIER, 2, RoundingMode.DOWN);
            }
            win = win.add(jackpot);
        }

        // txService should fail if transactions has been cancelled already - idempotency not in that case
        txReq.setFailOnCancelledAlready(true);

        TxResponse txResp = null;
        if (bet.signum() != 0 || request.getBet() != null) {
            // create bet tx
            txReq.setExternalOrigId(request.getBet().getTransactionId());
            txReq.setType(BET);
            txReq.setBet(bet);
            txReq.setWin(BigDecimal.ZERO);
            try {
                txResp = addTxFromProvider(txReq);
            } catch (OuslyOutOfMoneyException e) {
                var bal = e.getBalance();
                throw new OGOutOfMoneyError("OUT_OF_MONEY", "bet amount could not be deducted", 200, bal);
            } catch (OuslyTxAlreadyCancelledException e) {
                throw new OGError("ERROR", "tx could not be booked, was already cancelled", 400);
            } catch (OuslyTransactionException ouslyTransactionException) {
                // Out of money (in most cases)
                log.error("Error adding bet tx", ouslyTransactionException);
                throw new OGError("ERROR", "tx could not be booked", 400);
            }
        }
        if (win.signum() != 0 || request.getWin() != null) {
            // create win tx
            txReq.setExternalOrigId(request.getWin().getTransactionId());
            txReq.setType(WIN);
            txReq.setWin(win);
            txReq.setBet(BigDecimal.ZERO);
            try {
                txResp = addTxFromProvider(txReq);
            } catch (OuslyTransactionException ouslyTransactionException) {
                log.error("Error adding win tx", ouslyTransactionException);
                throw new OGError("ERROR", "unknown error", 404);
            }
        }

        var resp = new OGGameTransactionResponse();

        if (txResp == null) {
            log.warn("NULL TXRESP RESPONSE");
        } else {
            log.debug("Returning from txResp {}", txResp);
        }

        if (txResp.getType() == TOMBSTONE) {
            throw new OGError("ERROR", "Tombstone prevents transaction booking", 500);
        }
        resp.setBalance(txResp.getNewBalance().multiply(AMOUNT_MULTIPLIER).longValue());
        resp.setResponseCode("OK");
        log.debug("ORYX_TX_RESP:{}", resp);
        return resp;
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150, multiplier = 2, maxDelay = 2000), maxAttempts = 4)
    public OGTransactionChangeResponse transactionChange(
            OGTransactionChangeRequest request) throws OGError {
        // we need to perform this call even without session-token. What we could do is to check at least the player id
        if (request.getPlayerId() == null || request.getPlayerId().length() <= 4) {
            throw new OGError("ERROR", "unknown player ID", 404);
        }
        long uid;
        try {
            uid = Long.parseLong(request.getPlayerId().substring(4));
        } catch (Exception e) {
            throw new OGError("ERROR", "unknown player ID", 404);
        }

        // 1. Check if the rollback was already performed!
        var rbTx = findByUserIdAndOrigId(uid, request.getTransactionId() + ":ROLLBACK");
        if (rbTx != null) {
            // rollback was already executed return idempotent answer
            var resp = new OGTransactionChangeResponse();
            resp.setResponseCode("OK");
            resp.setBalance(rbTx.getBalanceAfter().longValue() * 100);
            return resp;
        }
        // 2. Check if the given tx was written before performing a rollback
        var oTx = findByUserIdAndOrigId(uid, request.getTransactionId());
        if (oTx == null) {
            // cancel comes in before bet -> place tombstone for that transaction id (ignore user & gameId, they do not matter much)
            // 1. try to find token
            var token = getToken(request.getSessionToken());

            var txReq = (token == null) ? new TxRequest() : new TxRequest(token);
            txReq.setRoundMode(RoundMode.OPENCLOSE);
            txReq.setType(TOMBSTONE);
            txReq.setBet(BigDecimal.ZERO);
            txReq.setWin(BigDecimal.ZERO);
            txReq.setRoundRef(request.getRoundId());

            if (token == null) {
                // use Gamecode to get Vendor
                var optSessionId = findLatestSessionIdByUserIdAndGameKey(uid, request.getGameCode());
                if (optSessionId.isPresent()) {
                    var sessionByUserIdAndSessionId = findSessionByUserIdAndSessionId(uid, optSessionId.get());
                    token = new SessionCacheEntry(sessionByUserIdAndSessionId.get(), null, null);
                } else {
                    token = new SessionCacheEntry();
                    token.setUserId(uid);
                    token.setGameId(findGameIdByKey(request.getGameCode()));
                    token.setSessionId(-1L);
                    token.setGp(GamePlatform.UNKNOWN);
                    token.setJurisdiction(getActiveJurisdiction());
                }

            }

            txReq.setExternalOrigId(request.getTransactionId());
            txReq.setSessionId(token.getSessionId());
            txReq.setGameId((token == null) ? null : token.getGameId());
            txReq.setUserId(uid);
            txReq.setJurisdiction(token.getJurisdiction());
            txReq.setGp(token.getGp());

            try {
                var txResp = addTxFromProvider(txReq);
                var resp = new OGTransactionChangeResponse();
                resp.setResponseCode("TRANSACTION_NOT_FOUND");
                resp.setBalance(txResp.getNewBalance().multiply(AMOUNT_MULTIPLIER).longValue());
                return resp;
            } catch (OuslyTransactionException e) {
                throw new OGError("ERROR", "setting CANCEL tombstone failed", 500);
            }
        }
        if (oTx.getUserId() != uid) {
            throw new OGError("ERROR", "unknown player ID given in changeTransaction", 404);
        }

        var w = getWallet(uid);
        // now we can really undo the tx
        var txResponse = performRollback(oTx, w);
        var resp = new OGTransactionChangeResponse();
        resp.setResponseCode("OK");
        resp.setBalance(txResponse.getNewBalance().multiply(AMOUNT_MULTIPLIER).longValue());
        return resp;
    }

    @Override
    public List<String> getVendorNames() {
        return Arrays.asList(config.getVendorName(), "KALAMBA", "GIVME",
//                "GAMOMAT",
                "GOLDEN HERO", "ARCADEM", "PETERANDSONS", "CANDLEBETS");
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame (ORYXGAMING) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        var gameUrl = "TEST";
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            // create token call to spino
            var bld = new URIBuilder(config.getLauncherurl() + config.getWalletCode() + "/games/" + getMassagedGameCode(game, platform) + "/open");
            bld.addParameter("token", token);
            bld.addParameter("playMode", "REAL");
            bld.addParameter("languageCode", getLangCodeForUser(user.getId(), settings));
            gameUrl = bld.build().toString();
        }

        var gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        var sessionToken = UUID.randomUUID().toString();
        gameInstance.setToken(sessionToken);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    static String getMassagedGameCode(CasinoGame game, GamePlatform platform) {
        if (platform == GamePlatform.MOBILE) {
            return game.getGameId().replace("ORYX_HTML5_", "ORYX_MOBILE_").replace("KLM_", "KLMM_").replace(
                    "GAM_", "GAMM_").replace("GHG_", "GHGM_");
        }
        return game.getGameId().replace("ORYX_MOBILE_", "ORYX_HTML5_").replace("KLMM_", "KLM_").replace(
                "GAMM_", "GAM_").replace("GHGM_", "GHG_");
    }

    private static String getLangCodeForUser(Long userId, GameSettings settings) {
        if (settings != null && settings.getLanguage() != null) {
            return settings.getLanguage().toUpperCase();
        }
        return "EN";
    }

    private String getRealVendorName(SessionCacheEntry gbce) {
        return getRealVendorNameByGameCode(gbce.getGameCode());
    }

    private String getRealVendorNameByGameCode(String gameKey) {

        var comps = gameKey.split("_");

        if (comps.length < 2) {
            return "ORYXGAMING";
        }
        return switch (comps[0]) {
            case "KLMM" -> "KALAMBA";
            case "GAMM" -> "GAMOMAT";
            case "GIVME" -> "GIVME";
            case "GHGM" -> "GOLDEN HERO";
            case "ARC" -> "ARCADEM";
            case "PSO" -> "PETERANDSONS";
            case "CDB" -> "CANDLEBETS";
            default -> config.getVendorName();
        };
    }

}
