package com.ously.gamble.bridge.edict.exceptions;

import com.ously.gamble.bridge.edict.model.authorize.AuthorizationFault;

import java.io.Serial;

public class EdictAuthorizationException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;
    private AuthorizationFault fault;

    public EdictAuthorizationException(String msg, AuthorizationFault fault) {
        super(msg);
        this.fault = fault;
    }

    public EdictAuthorizationException(String msg, Throwable t, AuthorizationFault fault) {
        super(msg, t);
        this.fault = fault;
    }

    public AuthorizationFault getFault() {
        return fault;
    }

    public void setFault(AuthorizationFault fault) {
        this.fault = fault;
    }
}
