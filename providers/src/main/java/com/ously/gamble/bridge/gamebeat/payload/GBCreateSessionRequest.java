package com.ously.gamble.bridge.gamebeat.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GBCreateSessionRequest {

    String locale;
    String ip;
    @JsonProperty("client_type")
    String clientType;
    @JsonProperty("casino_id")
    String casinoId;
    String game;
    String currency;
    GBUrl urls;
    GBUser user;
    @JsonProperty("session_token")
    String sessionToken;

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getCasinoId() {
        return casinoId;
    }

    public void setCasinoId(String casinoId) {
        this.casinoId = casinoId;
    }

    public String getGame() {
        return game;
    }

    public void setGame(String game) {
        this.game = game;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public GBUrl getUrls() {
        return urls;
    }

    public void setUrls(GBUrl urls) {
        this.urls = urls;
    }

    public GBUser getUser() {
        return user;
    }

    public void setUser(GBUser user) {
        this.user = user;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    @Override
    public String toString() {
        return "GBCreateSessionRequest{" +
                "locale='" + locale + '\'' +
                ", ip='" + ip + '\'' +
                ", clientType='" + clientType + '\'' +
                ", casinoId='" + casinoId + '\'' +
                ", game='" + game + '\'' +
                ", currency='" + currency + '\'' +
                ", url=" + urls +
                ", user=" + user +
                ", sessionToken='" + sessionToken + '\'' +
                '}';
    }
}
