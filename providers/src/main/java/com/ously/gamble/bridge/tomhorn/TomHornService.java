package com.ously.gamble.bridge.tomhorn;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.tomhorn.payload.*;

public interface TomHornService extends BridgeHandler {

    ThBalanceResponse getBalance(ThRequest req) throws ThException;

    ThTransactionResponse deposit(ThRequest req) throws ThException;

    ThTransactionResponse withdraw(ThRequest req) throws ThException;

    ThResponse rollback(ThRequest req) throws ThException;


}
