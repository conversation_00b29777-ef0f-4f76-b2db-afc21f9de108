package com.ously.gamble.bridge.booongo.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BooLoginRequest extends BooRequest {

    public static class BooLoginArgs {

        String platform;

        public String getPlatform() {
            return platform;
        }

        public void setPlatform(String platform) {
            this.platform = platform;
        }

        @Override
        public String toString() {
            return "BooLoginArgs{" +
                    "platform='" + platform + '\'' +
                    '}';
        }
    }

    BooLoginRequest() {
        super();
    }

    BooLoginArgs args;

    public BooLoginArgs getArgs() {
        return args;
    }

    public void setArgs(BooLoginArgs args) {
        this.args = args;
    }

    @Override
    public String toString() {
        return "BooLoginRequest{" +
                "args=" + args +
                ',' + super.toString();
    }
}
