package com.ously.gamble.bridge.microgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;


@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MGTransaction {

    Long rowId;
    String loginName;
    Long transactionNumber;
    Long betReference;
    BigDecimal refundAmount;
    Long winReference;
    BigDecimal winAmount;
    String gameName;
    String currency;
    String dateCreated;
    @JsonProperty("progressiveWin")
    Boolean progressiveWin;
    String progressiveDescription;
    String freeGameOffer;
    Long productId;
    String productName;
    Long userId;
    String moduleId;
    String clientId;

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Long getWinReference() {
        return winReference;
    }

    public void setWinReference(Long winReference) {
        this.winReference = winReference;
    }

    public BigDecimal getWinAmount() {
        return winAmount;
    }

    public void setWinAmount(BigDecimal winAmount) {
        this.winAmount = winAmount;
    }

    public Boolean getProgressiveWin() {
        return progressiveWin;
    }

    public void setProgressiveWin(Boolean progressiveWin) {
        this.progressiveWin = progressiveWin;
    }

    public String getProgressiveDescription() {
        return progressiveDescription;
    }

    public void setProgressiveDescription(String progressiveDescription) {
        this.progressiveDescription = progressiveDescription;
    }

    public Long getRowId() {
        return rowId;
    }

    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public Long getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(Long transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public Long getBetReference() {
        return betReference;
    }

    public void setBetReference(Long betReference) {
        this.betReference = betReference;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(String dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getFreeGameOffer() {
        return freeGameOffer;
    }

    public void setFreeGameOffer(String freeGameOffer) {
        this.freeGameOffer = freeGameOffer;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
