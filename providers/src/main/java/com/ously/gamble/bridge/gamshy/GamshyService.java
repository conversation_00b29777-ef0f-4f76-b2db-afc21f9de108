package com.ously.gamble.bridge.gamshy;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.gamshy.payload.GSBalanceRequest;
import com.ously.gamble.bridge.gamshy.payload.GSLoginRequest;
import com.ously.gamble.bridge.gamshy.payload.GSResponse;
import com.ously.gamble.bridge.gamshy.payload.GSWithdrawDepositRequest;

public interface GamshyService extends BridgeHandler {

    GSResponse login(GSLoginRequest req);

    GSResponse getBalance(GSBalanceRequest req);

    GSResponse withdrawDeposit(GSWithdrawDepositRequest req);

}
