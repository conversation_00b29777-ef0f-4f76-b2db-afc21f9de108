package com.ously.gamble.bridge.apollo.payload;

public class APRequest {
    String sessionId;
    String timestamp;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "APRequest{" +
                "sessionId='" + sessionId + '\'' +
                ", timestamp='" + timestamp + '\'' +
                '}';
    }
}
