package com.ously.gamble.bridge.everymatrix;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.everymatrix.payload.*;

public interface EverymatrixService extends Bridge<PERSON><PERSON>ler {

	int CODE_SUCCESS = 0;
	int CODE_UNKNOWN_ERROR = 101;
	int CODE_USER_BLOCKED = 102;
	int CODE_USER_NOT_FOUND = 103;

	int CODE_INSUFFICIENT_FUNDS = 104;
	int CODE_IP_NOT_ALLOWED = 105;
	int CODE_CURRENCY_NOT_SUPPORTED = 106;
	int CODE_TX_PROCESSING = 107;
	int CODE_TX_NOT_FOUND = 108;
	int CODE_LOSS_LIMIT_EXCEEDED = 109;
	int CODE_STAKE_LIMIT_EXCEEDED = 110;
	int CODE_SESSION_LIMIT_EXCEEDED = 111;
	int CODE_MAX_STAKE_EXCEEDED = 112;
	int CODE_USER_SELFEXCLUDED = 113;
	int CODE_USER_INACTIVE = 114;


	EMGetAccountResponse getAccount(EMGetAccountRequest req);

	EMGetBalanceResponse getBalance(EMGetBalanceRequest req);

	EMTransactionResponse debit(EMTransactionRequest req);

	EMTransactionResponse credit(EMTransactionRequest req);

}
