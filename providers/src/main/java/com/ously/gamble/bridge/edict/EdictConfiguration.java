package com.ously.gamble.bridge.edict;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
@ConfigurationProperties(prefix = "edict")
@ConditionalOnProperty(
        value = "edict.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class EdictConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "EDICT";


    private String currency = "FUM";

    private String callerId = "edict_TEST_accnt";

    private String callerPw = "ZTre63324_Funz88";

    private String baseUrl = "https://du100-i.edictmaltaservices.com.mt/gamestart.html";

    private String casinoName = "ously-fm";

    public String getCallerId() {
        return callerId;
    }

    public String getCallerPw() {
        return callerPw;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public String getCasinoName() {
        return casinoName;
    }

    public String getCurrency() {
        return currency;
    }

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return "/bridge/edict";
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return new ArrayList<>();
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public void setCallerPw(String callerPw) {
        this.callerPw = callerPw;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public void setCasinoName(String casinoName) {
        this.casinoName = casinoName;
    }
}
