package com.ously.gamble.bridge.gamshy.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class GSWallet {

    BigDecimal withdrawal;
    BigDecimal deposit;
    BigDecimal balance;
    String currencyIsoCode;

    public GSWallet() {
    }

    public GSWallet(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getWithdrawal() {
        return withdrawal;
    }

    public void setWithdrawal(BigDecimal withdrawal) {
        this.withdrawal = withdrawal;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getCurrencyIsoCode() {
        return currencyIsoCode;
    }

    public void setCurrencyIsoCode(String currencyIsoCode) {
        this.currencyIsoCode = currencyIsoCode;
    }

    @Override
    public String toString() {
        return "GSWallet{" +
                "withdrawal=" + withdrawal +
                ", deposit=" + deposit +
                ", balance=" + balance +
                ", currencyIsoCode='" + currencyIsoCode + '\'' +
                '}';
    }
}
