package com.ously.gamble.bridge.booongo.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BooBonus {

    String campaign;
    String source;
    @JsonProperty("bonus_id")
    Long bonusId;
    @JsonProperty("ext_bonus_id")
    String extBonusid;
    @JsonProperty("bonus_type")
    String bonusType;
    String event;
    @JsonProperty("start_date")
    String startDate;
    @JsonProperty("end_date")
    String endDate;

    @JsonProperty("total_bet")
    String totalBet;
    @JsonProperty("total_win")
    String totalWin;
    @JsonProperty("played_bet")
    String playedBet;
    @JsonProperty("played_win")
    String playedWin;
    String status;

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getBonusId() {
        return bonusId;
    }

    public void setBonusId(Long bonusId) {
        this.bonusId = bonusId;
    }

    public String getExtBonusid() {
        return extBonusid;
    }

    public void setExtBonusid(String extBonusid) {
        this.extBonusid = extBonusid;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getTotalBet() {
        return totalBet;
    }

    public void setTotalBet(String totalBet) {
        this.totalBet = totalBet;
    }

    public String getTotalWin() {
        return totalWin;
    }

    public void setTotalWin(String totalWin) {
        this.totalWin = totalWin;
    }

    public String getPlayedBet() {
        return playedBet;
    }

    public void setPlayedBet(String playedBet) {
        this.playedBet = playedBet;
    }

    public String getPlayedWin() {
        return playedWin;
    }

    public void setPlayedWin(String playedWin) {
        this.playedWin = playedWin;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "BooBonus{" +
                "campaign='" + campaign + '\'' +
                ", source='" + source + '\'' +
                ", bonusId=" + bonusId +
                ", extBonusid='" + extBonusid + '\'' +
                ", bonusType='" + bonusType + '\'' +
                ", event='" + event + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", totalBet='" + totalBet + '\'' +
                ", totalWin='" + totalWin + '\'' +
                ", playedBet='" + playedBet + '\'' +
                ", playedWin='" + playedWin + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
