package com.ously.gamble.bridge.triplecherry.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class TCTransactionRequest {

    @JsonProperty("transaction_id")
    String transactionId;

    @JsonProperty("player_id")
    String playerId;

    @JsonProperty("cycle_id")
    String cycleId;

    @JsonProperty("amount")
    BigDecimal amount;

    @JsonProperty("reason")
    String reason;

    @JsonProperty("gameId")
    String gameId;

    @JsonProperty("promotionId")
    String promotionId;

    @JsonProperty("redeemId")
    String redeemId;

    @JsonProperty("transaction_to_be_invalidated")
    String invalidTransactionId;

    public String getInvalidTransactionId() {
        return invalidTransactionId;
    }

    public void setInvalidTransactionId(String invalidTransactionId) {
        this.invalidTransactionId = invalidTransactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getCycleId() {
        return cycleId;
    }

    public void setCycleId(String cycleId) {
        this.cycleId = cycleId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(String promotionId) {
        this.promotionId = promotionId;
    }

    public String getRedeemId() {
        return redeemId;
    }

    public void setRedeemId(String redeemId) {
        this.redeemId = redeemId;
    }

    @Override
    public String toString() {
        return "TCTransactionRequest{" +
                "transactionId='" + transactionId + '\'' +
                ", playerId='" + playerId + '\'' +
                ", cycleId='" + cycleId + '\'' +
                ", amount=" + amount +
                ", reason='" + reason + '\'' +
                ", gameId='" + gameId + '\'' +
                ", promotionId='" + promotionId + '\'' +
                ", redeemId='" + redeemId + '\'' +
                //", other=" + other +
                ", invalidTransactionId='" + invalidTransactionId + '\'' +
                '}';
    }
}
