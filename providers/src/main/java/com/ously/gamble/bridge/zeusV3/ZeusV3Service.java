package com.ously.gamble.bridge.zeusV3;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.zeusV3.exception.ZeusV3Exception;
import com.ously.gamble.bridge.zeusV3.payload.request.GameRoundRequest;
import com.ously.gamble.bridge.zeusV3.payload.request.PlayerWalletRequest;
import com.ously.gamble.bridge.zeusV3.payload.response.PlayerWalletResponse;

public interface ZeusV3Service extends BridgeHandler {
    PlayerWalletResponse processGameRound(GameRoundRequest request) throws ZeusV3Exception;

    PlayerWalletResponse cancelGameRound(GameRoundRequest request);

    PlayerWalletResponse getPlayerWallet(PlayerWalletRequest request) throws ZeusV3Exception;
}
