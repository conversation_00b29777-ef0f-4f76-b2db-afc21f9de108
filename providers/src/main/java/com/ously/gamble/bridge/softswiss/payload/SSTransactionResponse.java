package com.ously.gamble.bridge.softswiss.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record SSTransactionResponse(
        String id,
        @JsonProperty("id_casino")
        String idCasino,
        @JsonProperty("processed_at")
        String processedAt,
        @JsonProperty("bonus_amount")
        String bonusAmount
) {


}
