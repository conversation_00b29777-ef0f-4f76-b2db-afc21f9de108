package com.ously.gamble.bridge.kagaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class KAApiGame {

    String gameId;
    String gameType;
    String variantType;
    String gameName;
    Integer numReels;
    Integer numRows;
    Integer numSelections;
    Integer[] availableBets;
    Integer maxBet;
    BigDecimal[] availableDenoms;
    String iconURLPrefix;
    Boolean newGame;
    Boolean popular;
    BigDecimal[] availableRTP;

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getGameType() {
        return gameType;
    }

    public void setGameType(String gameType) {
        this.gameType = gameType;
    }

    public String getVariantType() {
        return variantType;
    }

    public void setVariantType(String variantType) {
        this.variantType = variantType;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public Integer getNumReels() {
        return numReels;
    }

    public void setNumReels(Integer numReels) {
        this.numReels = numReels;
    }

    public Integer getNumRows() {
        return numRows;
    }

    public void setNumRows(Integer numRows) {
        this.numRows = numRows;
    }

    public Integer getNumSelections() {
        return numSelections;
    }

    public void setNumSelections(Integer numSelections) {
        this.numSelections = numSelections;
    }

    public Integer[] getAvailableBets() {
        return availableBets;
    }

    public void setAvailableBets(Integer... availableBets) {
        this.availableBets = availableBets;
    }

    public Integer getMaxBet() {
        return maxBet;
    }

    public void setMaxBet(Integer maxBet) {
        this.maxBet = maxBet;
    }

    public BigDecimal[] getAvailableDenoms() {
        return availableDenoms;
    }

    public void setAvailableDenoms(BigDecimal... availableDenoms) {
        this.availableDenoms = availableDenoms;
    }

    public String getIconURLPrefix() {
        return iconURLPrefix;
    }

    public void setIconURLPrefix(String iconURLPrefix) {
        this.iconURLPrefix = iconURLPrefix;
    }

    public Boolean getNewGame() {
        return newGame;
    }

    public void setNewGame(Boolean newGame) {
        this.newGame = newGame;
    }

    public Boolean getPopular() {
        return popular;
    }

    public void setPopular(Boolean popular) {
        this.popular = popular;
    }

    public BigDecimal[] getAvailableRTP() {
        return availableRTP;
    }

    public void setAvailableRTP(BigDecimal... availableRTP) {
        this.availableRTP = availableRTP;
    }
}
