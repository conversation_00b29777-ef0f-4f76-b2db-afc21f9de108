create xx.key/xx.crt/... according to mascot docu

then:

Step one: Convert the x.509 cert and key to a pkcs12 file

openssl pkcs12 -export -in server.crt -inkey client.key \
               -out client.p12 -name [some-alias] \
               -CAfile ca.crt -caname root


openssl pkcs12 -export -in client.crt -inkey client.key -out client.p12 -name mascot
(you will be asked for password: "SECRET123")


Step two: Convert the pkcs12 file to a Java keystore

keytool -importkeystore \
        -deststorepass [changeit] -destkeypass [changeit] -destkeystore server.keystore \
        -srckeystore server.p12 -srcstoretype PKCS12 -srcstorepass some-password \
        -alias [some-alias]


keytool -importkeystore -deststorepass KEYSTOREPASS -destkeypass KEYSTOREPASS -destkeystore client.jks -srckeystore client.p12 -srcstoretype PKCS12 -srcstorepass SECRET123 -alias mascot