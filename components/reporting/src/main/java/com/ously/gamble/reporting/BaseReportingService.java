package com.ously.gamble.reporting;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.DateFormatConverter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Locale;

public class BaseReportingService {
    /**
     * @param wb     workbook
     * @param sheets sheetnames to remove
     */
    static void removeSheets(Workbook wb, String... sheets) {
        Arrays.stream(sheets).forEach(a -> {
            int index = wb.getSheetIndex(a);
            if (index >= 0) {
                wb.removeSheetAt(index);
            }
        });
    }


    static public Sheet cloneSheet(Workbook wb, String oSheetname, String nSheetname) {
        int sheetIndex = wb.getSheetIndex(oSheetname);
        if (sheetIndex >= 0) {
            Sheet nSheet = wb.cloneSheet(sheetIndex);
            wb.setSheetName(wb.getSheetIndex(nSheet), nSheetname);
            return nSheet;
        }
        return null;
    }

    static public CellStyle getDatetimeCellStyle(Workbook wb) {
        CreationHelper createHelper = wb.getCreationHelper();
        CellStyle dtStyle = wb.createCellStyle();
        String excelFormatPattern = DateFormatConverter.convert(Locale.US, "yyyy-MM-dd HH:mm");
        dtStyle.setDataFormat(
                createHelper.createDataFormat().getFormat(excelFormatPattern));
        return dtStyle;
    }

    static public CellStyle getDateCellStyle(Workbook wb) {
        CreationHelper createHelper = wb.getCreationHelper();
        CellStyle dtStyle = wb.createCellStyle();
        String excelFormatPattern = DateFormatConverter.convert(Locale.US, "yyyy-MM-dd");
        dtStyle.setDataFormat(
                createHelper.createDataFormat().getFormat(excelFormatPattern));
        return dtStyle;
    }

    protected static Workbook createWorkbookFromResource(String resPath) throws IOException {
        return WorkbookFactory.create(BaseReportingService.class.getResourceAsStream(resPath));
    }

// --Commented out by Inspection START (07.10.22, 11:29):
//    static String writeWorkbookToTmpFile(Workbook wb, long year, int month) throws Exception {
//        var file = File.createTempFile("report_" + year + '_' + (month + 1), "xlsx");
//        var fOut = new FileOutputStream(file);
//        wb.write(fOut);
//        fOut.close();
//        return file.getAbsolutePath();
//    }
// --Commented out by Inspection STOP (07.10.22, 11:29)

    static byte[] writeWorkbookToByteArray(Workbook wb) throws Exception {
        var bOut = new ByteArrayOutputStream(100000);
        wb.write(bOut);
        return bOut.toByteArray();
    }


    static public void setCellValueInstant(Sheet sheet, int row, int column, Instant value, CellStyle dtStyle) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.STRING);
        }
        if (value != null) {
            cell.setCellValue(LocalDateTime.ofInstant(value, ZoneOffset.UTC));
            cell.setCellStyle(dtStyle);
        } else {
            cell.setCellValue("");
        }
    }


    static public void setCellValueDate(Sheet sheet, int row, int column, LocalDate value, CellStyle dtStyle) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.STRING);
        }
        if (value != null) {
            cell.setCellValue(value);
            cell.setCellStyle(dtStyle);
        } else {
            cell.setCellValue("");
        }
    }


    static public void setCellValueString(Sheet sheet, int row, int column, String value) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.STRING);
        }
        cell.setCellValue(value);
    }

    static public void setCellValueNumeric(Sheet sheet, int row, int column, Number value) {

        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.NUMERIC);
        }

        if(value!=null) {

            if(value instanceof BigDecimal bf){
                cell.setCellValue(bf.setScale(2, RoundingMode.HALF_UP).doubleValue());
            }else if(value instanceof Double db){
                BigDecimal bd = BigDecimal.valueOf(db);
                cell.setCellValue(bd.setScale(2, RoundingMode.HALF_UP).doubleValue());
            }else if(value instanceof Float fl){
                BigDecimal bd = BigDecimal.valueOf(fl);
                cell.setCellValue(bd.setScale(2, RoundingMode.HALF_UP).doubleValue());
            }else {
                cell.setCellValue(value.longValue());
            }
        }
    }

    static public void setCellValueNumeric(Sheet sheet, int row, int column, BigDecimal value) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.NUMERIC);
        }

        if (value != null) {
            cell.setCellValue(value.setScale(2, RoundingMode.HALF_UP).doubleValue());
        }
    }

    static public void setCellValueNumeric(Sheet sheet, int row, int column, Double value) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.NUMERIC);
        }

        if (value != null) {
            cell.setCellValue(value);
        }
    }

    static public void setCellValueFormula(Sheet sheet, int row, int column, String fData) {
        var aRow = sheet.getRow(row);
        if (aRow == null) {
            aRow = sheet.createRow(row);
        }
        var cell = aRow.getCell(column);
        if (cell == null) {
            cell = aRow.createCell(column, CellType.FORMULA);
        }
        cell.setCellFormula(fData);
    }

    static public String getCellFormula(Sheet sheet, int row, int column) {
        return sheet.getRow(row).getCell(column).getCellFormula();
    }

}
