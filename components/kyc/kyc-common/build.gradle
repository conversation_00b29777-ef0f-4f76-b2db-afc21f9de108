buildscript {

    dependencies {


        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}


plugins {
    id 'java-library'
}

test {
    useJUnitPlatform()
    // systemProperty 'de.adesso.junitinsights.enabled', 'true'
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
}

if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation 'io.hypersistence:hypersistence-utils-hibernate-62:3.4.3'

}