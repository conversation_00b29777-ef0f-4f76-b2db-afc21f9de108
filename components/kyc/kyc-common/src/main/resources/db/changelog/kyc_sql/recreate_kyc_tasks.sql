DROP TABLE IF EXISTS kyc_tasks;

CREATE TABLE IF NOT EXISTS kyc_tasks
(
    `user_id`    bigint unsigned                     NOT NULL,
    `task_id`    bigint unsigned                     NOT NULL,
    `provider`   varchar(100)                        NOT NULL,
    `kyc_id`     varchar(100)                        NOT NULL,
    `status`     varchar(20)                         NOT NULL,
    `type`       varchar(20)                         NOT NULL,
    `req_reason` varchar(20)                         NOT NULL,
    `data_path`  varchar(200),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (`user_id`, `task_id`),
    KEY `idx_kyc_id` (`kyc_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

