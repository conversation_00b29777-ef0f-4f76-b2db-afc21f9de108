package com.ously.gamble.huawei.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.conditions.ConditionalOnBackendOrOffloader;
import com.ously.gamble.huawei.api.HuaweiAuthService;
import com.ously.gamble.huawei.configuration.HuaweiAppConfig;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;

@Component
@ConditionalOnBackendOrOffloader
@ConditionalOnProperty(prefix = "huawei", name = "enabled", havingValue = "true")
public class HuaweiAuthServiceImpl implements HuaweiAuthService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final RestTemplate restTemplate;
    private final ObjectMapper om;
    private final HuaweiAppConfig hwConfig;
    private final LoadingCache<String, String> authTkCache;

    public HuaweiAuthServiceImpl(RestTemplate restTemplate,
                                 ObjectMapper om,
                                 HuaweiAppConfig hwConfig,
                                 LCacheFactory<String, String> lCacheFactory) {
        this.restTemplate = restTemplate;
        this.om = om;
        this.hwConfig = hwConfig;
        this.authTkCache = lCacheFactory.registerCacheLoader("hwAuthToken", 10, 1, 45 * 60, this::getAppAT);

    }


    @Override
    public HttpHeaders getAuthHeaders(String type) {
        // TODO: The token can be reused for a certain amount of time. So we cache that with an expiry of N-30min
        String all = authTkCache.get(type);
        if ("IAP".equalsIgnoreCase(type)) {
            String oriString = MessageFormat.format("APPAT:{0}", all);
            String authorization =
                    MessageFormat.format("Basic {0}", Base64.encodeBase64String(oriString.getBytes(StandardCharsets.UTF_8)));
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", authorization);
            headers.add("Content-Type", "application/json; charset=UTF-8");
            return headers;
        } else {
            String authorization =
                    MessageFormat.format("Bearer {0}", all);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", authorization);
            headers.add("Content-Type", "application/json; charset=UTF-8");
            return headers;

        }
    }


    /**
     * Util stuff
     **/
    private String getAppAT(String key) throws Exception {
        // Obtain the access token.
        String grant_type = "client_credentials";
        String msgBody = MessageFormat.format("grant_type={0}&client_secret={1}&client_id={2}", grant_type, URLEncoder.encode(this.hwConfig.getClientSecret(), StandardCharsets.UTF_8), this.hwConfig.getClientId());


        ResponseEntity<String> response = restTemplate.exchange(hwConfig.getTokenUrl(), HttpMethod.POST, new HttpEntity<>(msgBody, createHeaders()), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            JsonNode jsonNode = om.readTree(response.getBody());
            var accessToken = jsonNode.get("access_token").textValue();
            var expires_in = jsonNode.get("expires_in").intValue();
            log.info("Got Token:{}->valid for {}", accessToken, expires_in);
            return accessToken;
        } else {
            log.warn("error getting authToken:{}", response);
        }

        return null;
    }

    private HttpHeaders createHeaders() {
        return new HttpHeaders() {{
            set("Content-Type", "application/json; charset=utf-8");
        }};
    }


}

