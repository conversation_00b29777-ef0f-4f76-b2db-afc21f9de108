package com.ously.gamble.notification.handlers.push;

import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.MessagingErrorCode;
import com.google.firebase.messaging.Notification;
import com.ously.gamble.api.crm.FcmTokenFailedDeliveryEvent;
import com.ously.gamble.api.huawei.HuaweiPushService;
import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationHandler;
import com.ously.gamble.api.notification.PushNotificationStatus;
import com.ously.gamble.api.notification.Receipt;
import com.ously.gamble.persistence.model.NotificationType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;


@Component
public class PushHandler implements NotificationHandler {

    final Logger log = LoggerFactory.getLogger(PushHandler.class);

    private final PushNotificationStatus pStatus;
    private final FirebaseApp firebaseApp;
    private final ApplicationEventPublisher eventPublisher;
    private final HuaweiPushService hwPushService;

    public PushHandler(PushNotificationStatus pns, FirebaseApp firebaseApp, ApplicationEventPublisher eventPublisher,
                       @Autowired(required = false) HuaweiPushService hwPushService) {
        this.pStatus = pns;
        this.firebaseApp = firebaseApp;
        this.eventPublisher = eventPublisher;
        this.hwPushService = hwPushService;
    }

    @Override
    public NotificationType supportedType() {
        return NotificationType.MOBILE_PUSH;
    }

    @Override
    public Receipt sendOut(Message msg) {


        if (!pStatus.isFcmActive()) {
            return Receipt.error(msg, "Push adapter is not configured");
        }

        var lfcm = msg.getDestination();
        if (lfcm != null && lfcm.startsWith("HW_")) {
            if (hwPushService != null) {
                hwPushService.sendPushMessage(msg);
            }
            return Receipt.ok(msg, "SENT");
        }
        if (lfcm != null && !lfcm.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug("Sending PUSH to user={} using FCM {}", msg.getuId(), StringUtils.abbreviate(lfcm, 45));
            }
//            var message = com.google.firebase.messaging.Message.builder()
//                    .setNotification(Notification.builder()
//                            .setTitle(msg.getSubject())
//                            .setBody(msg.getMsg())
//                            .build())
//                    .setToken(lfcm);

            var message = createMessageBuilder(msg, lfcm);

            try {
                var result = FirebaseMessaging.getInstance(firebaseApp).send(message.build());
                return Receipt.ok(msg, result);
            } catch (FirebaseMessagingException e) {
                if (e.getMessagingErrorCode() == MessagingErrorCode.INVALID_ARGUMENT || e.getMessagingErrorCode() == MessagingErrorCode.UNREGISTERED) {
                    // TODO: Send event to remove fcm_token (from user_logins-"set to null" and via FcmTokenServiceImpl )
                    eventPublisher.publishEvent(new FcmTokenFailedDeliveryEvent(msg.getuId(), msg.getDestination()));
                }
                log.error("Error, sending pushMessage to {} via firebase token {} failed: {}", msg.getuId(), StringUtils.abbreviate(msg.getDestination(), 45), e.getMessage());
                return Receipt.error(msg, e.getMessage());
            }
        }
        return Receipt.error(msg, "not send due to missing fcm token: " + lfcm);
    }

    /**
     * Create the actual Push-message. It boils down to check if we have an image / title / ...
     * <p>
     * Rich
     *
     * @param msg  the internal msg structur
     * @param lfcm the fcm token
     * @return the
     */
    private static com.google.firebase.messaging.Message.Builder createMessageBuilder(Message msg, String lfcm) {


        String imgLink = msg.getProperties().getOrDefault("image", null);


        if (imgLink == null) {

            var message = com.google.firebase.messaging.Message.builder()
//                    .setNotification(Notification.builder()
//                            .setTitle(msg.getSubject())
//                            .setBody(msg.getMsg())
//                            .build())
                    .setToken(lfcm);

            var properties = msg.getProperties();
            for (String key : properties.keySet()) {
                message.putData(key, properties.get(key));
            }
            return message;
        } else {
            var message = com.google.firebase.messaging.Message.builder()
                    .setNotification(Notification.builder()
                            .setTitle(msg.getSubject())
                            .setBody(msg.getMsg())
                            .setImage(imgLink)
                            .build())
                    .setToken(lfcm);
            var properties = msg.getProperties();
            for (String key : properties.keySet()) {
                message.putData(key, properties.get(key));
            }
            return message;

        }

    }

}
