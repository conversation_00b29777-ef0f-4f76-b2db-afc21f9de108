package com.ously.gamble.postbacks;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(prefix = "postbacks", name = "enabled", havingValue = "true")
public class PostbacksFeature extends AbstractPlatformFeature implements PlatformFeature {

    private final PostbacksConfig pbConfig;

    public PostbacksFeature(PostbacksConfig pbCfg) {
        this.pbConfig = pbCfg;
    }

    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Postback handling " + ((pbConfig.isEnabled()) ? "enabled" : "disabled"), "sending out postbacks for signups/deposits/...");
    }

}
