package com.ously.gamble.videoads.service;

import com.ously.gamble.api.videoads.VideoAdDailyStatsAggDto;
import com.ously.gamble.api.videoads.VideoAdDailyStatsDto;
import com.ously.gamble.api.videoads.VideoAdDailyStatsService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.videoads.persistence.repository.VideoAdDailyStatsRepository;
import com.ously.gamble.videoads.persistence.repository.VideoAdViewRepository;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

@Service
@ConditionalOnProperty(prefix = "videoads", name = "enabled", havingValue = "true")
@ConditionalOnOffloader
public class VideoAdDailyStatsServiceImpl implements VideoAdDailyStatsService {

    private final VideoAdDailyStatsRepository statsRepository;
    private final VideoAdViewRepository videoAdViewRepository;

    public VideoAdDailyStatsServiceImpl(VideoAdDailyStatsRepository statsRepository, VideoAdViewRepository videoAdViewRepository) {
        this.statsRepository = statsRepository;
        this.videoAdViewRepository = videoAdViewRepository;
    }

    @Override
    public List<VideoAdDailyStatsDto> findByRDateBetween(LocalDate startDate, LocalDate endDate) {
        return statsRepository.findByRDateBetween(Date.valueOf(startDate), Date.valueOf(endDate));
    }

    @Override
    public VideoAdDailyStatsAggDto sumByRDateAndPosition(LocalDate startDate, LocalDate endDate, String position) {
        return statsRepository.sumByRDateAndPosition(Date.valueOf(startDate), Date.valueOf(endDate), position);
    }

    @Override
    @Transactional
    public void archiveVideoAds() {
        var barrier = LocalDate.now().atStartOfDay(ZoneOffset.UTC).toInstant();
        statsRepository.insertOrUpdateVideoAdDailyStats(barrier);
        videoAdViewRepository.deleteVideoAdViewByCreatedAtIsBefore(barrier);
    }
}