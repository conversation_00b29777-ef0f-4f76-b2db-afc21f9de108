package com.ously.gamble.videoads.persistence.model;

import com.ously.gamble.api.videoads.VideoAdStatus;
import jakarta.persistence.*;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "video_ad_views", indexes = {
        @Index(name = "idx_videoadview_userid_createdat", columnList = "user_id, created_at"),
        @Index(name = "idx_videoadview_createdat", columnList = "created_at")
})
public class VideoAdView {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "uuid", length = 36)
    private UUID uuid;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "position", length = 50)
    private String position;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private VideoAdStatus status;

    // Deprecated: Use actualCoins. Represents the potential reward before final calculation/CRM override.
    @Deprecated(since = "VideoAdRewardRefactor")
    @Column(name = "coins")
    private Double coins;

    // Deprecated: Use actualCoins. Represents the potential reward definition.
    @Deprecated(since = "VideoAdRewardRefactor")
    @Column(name = "price_def", length = 50)
    private String priceDef;

    @Column(name = "actual_coins")
    private Double actualCoins; // The actual amount of coins to be rewarded

    @Column(name = "context", length = 50)
    private String context; // Context of the ad view (e.g., "shop", "crm_daily_double", "crm_broke")

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    public VideoAdView() {
    }

    // Constructor for backward compatibility (can be removed later)
    @Deprecated(since = "VideoAdRewardRefactor")
    public VideoAdView(UUID uuid, Long userId, String position, VideoAdStatus status, Double coins, String priceDef, Instant createdAt) {
        this.uuid = uuid;
        this.userId = userId;
        this.position = position;
        this.status = status;
        this.coins = coins; // Keep setting deprecated field for now
        this.priceDef = priceDef; // Keep setting deprecated field for now
        this.actualCoins = coins; // Initialize actualCoins with old value
        this.context = "shop"; // Assume shop context for old records
        this.createdAt = createdAt;
    }

     public VideoAdView(Long userId, String position, VideoAdStatus status, Double actualCoins, String context, Instant createdAt) {
        this.userId = userId;
        this.position = position;
        this.status = status;
        this.actualCoins = actualCoins;
        this.context = context;
        this.createdAt = createdAt;
        // Set deprecated fields to null or reasonable defaults if needed for transition
        this.coins = actualCoins;
        this.priceDef = actualCoins != null && actualCoins > 0 ? "S#" + actualCoins.intValue() : "";
    }


    public UUID getUuid() {
        return uuid;
    }

    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public VideoAdStatus getStatus() {
        return status;
    }

    public void setStatus(VideoAdStatus status) {
        this.status = status;
    }

    @Deprecated(since = "VideoAdRewardRefactor")
    public Double getCoins() {
        return coins;
    }

    @Deprecated(since = "VideoAdRewardRefactor")
    public void setCoins(Double coins) {
        this.coins = coins;
    }

    @Deprecated(since = "VideoAdRewardRefactor")
    public String getPriceDef() {
        return priceDef;
    }

    @Deprecated(since = "VideoAdRewardRefactor")
    public void setPriceDef(String priceDef) {
        this.priceDef = priceDef;
    }

    public Double getActualCoins() {
        return actualCoins;
    }

    public void setActualCoins(Double actualCoins) {
        this.actualCoins = actualCoins;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
}