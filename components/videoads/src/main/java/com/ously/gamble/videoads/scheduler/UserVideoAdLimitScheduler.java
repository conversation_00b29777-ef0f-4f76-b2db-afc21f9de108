package com.ously.gamble.videoads.scheduler;

import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.videoads.service.UserVideoAdLimitService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneOffset;

@Component
@ConditionalOnProperty(prefix = "videoads", name = "enabled", havingValue = "true")
@ConditionalOnOffloader // Run cleanup on the offloader instance if applicable
public class UserVideoAdLimitScheduler {

    private static final Logger log = LoggerFactory.getLogger(UserVideoAdLimitScheduler.class);
    private static final int RETENTION_DAYS = 7; // Keep daily counts for 7 days

    private final UserVideoAdLimitService userVideoAdLimitService;
    private final ScheduleExecutionService scheduleExecutionService;

    public UserVideoAdLimitScheduler(UserVideoAdLimitService userVideoAdLimitService, ScheduleExecutionService scheduleExecutionService) {
        this.userVideoAdLimitService = userVideoAdLimitService;
        this.scheduleExecutionService = scheduleExecutionService;
    }

    // Run daily at 02:15 UTC
    @Scheduled(cron = "0 15 2 * * *")
    @SchedulerLock(name = "cleanupOldVideoAdCounts", lockAtLeastFor = "PT1M")
    public void cleanupOldVideoAdCounts() {
        scheduleExecutionService.doSchedule(
                new ScheduledTaskInformation("videoads", "cleanupOldVideoAdCounts", null),
                this::doCleanup
        );
    }

    private ScheduledTaskInformation doCleanup(ScheduledTaskInformation sti) {
        try {
            LocalDate retentionDate = LocalDate.now(ZoneOffset.UTC).minusDays(RETENTION_DAYS);
            log.info("Starting cleanup of UserVideoAdDailyCount records older than {}", retentionDate);
            int deletedCount = userVideoAdLimitService.cleanupOldCounts(retentionDate);
            log.info("Finished cleanup of UserVideoAdDailyCount records. Deleted {} records.", deletedCount);
            sti.ok();
        } catch (Exception e) {
            log.error("Error during UserVideoAdDailyCount cleanup", e);
            sti.fail(e);
        }
        return sti;
    }
}