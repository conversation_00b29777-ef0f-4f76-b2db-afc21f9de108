package com.ously.gamble.useraudit.service;

import com.ously.gamble.api.user.audit.AddUserAuditEvent;
import com.ously.gamble.api.user.audit.AddUserAuditEventTx;
import com.ously.gamble.api.user.audit.UserAuditDto;
import org.springframework.context.event.EventListener;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

public abstract class BaseUserAuditServiceImpl {

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserAuditingEvent(AddUserAuditEventTx auaEvnt) {
        rerouteEvent(auaEvnt.dto());
    }

    @EventListener
    public void handleUserAuditingEventNonTx(AddUserAuditEvent auaEvnt) {
        rerouteEvent(auaEvnt.dto());
    }

    abstract void rerouteEvent(UserAuditDto dto);

}
