package com.ously.gamble.useraudit.service;


import com.ously.gamble.api.user.audit.UserAuditDto;
import com.ously.gamble.api.user.audit.UserAuditInfoEntry;
import com.ously.gamble.api.user.audit.UserAuditInfos;
import com.ously.gamble.api.user.audit.UserAuditService;
import com.ously.gamble.useraudit.UserAuditConfig;
import com.ously.gamble.useraudit.persistence.model.UserAuditDao;
import com.ously.gamble.useraudit.persistence.model.UserAuditId;
import com.ously.gamble.useraudit.persistence.repository.UserAuditRepository;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "useraudit", name = "enabled", havingValue = "true"
)
public class UserAuditServiceImpl extends BaseUserAuditServiceImpl implements UserAuditService {

    @SuppressWarnings("FieldCanBeLocal")
    private final UserAuditConfig auditConfig;
    private final RabbitTemplate rbTemplate;
    private final UserAuditRepository uaRepo;

    public UserAuditServiceImpl(UserAuditConfig aConfig, RabbitTemplate rbTmpl, UserAuditRepository uaRepo) {
        this.auditConfig = aConfig;
        this.rbTemplate = rbTmpl;
        this.uaRepo = uaRepo;
    }

    @Override
    public Page<UserAuditDto> getAllUserAuditEntries(long userId, int page, int size) {
        PageRequest pg = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<UserAuditDao> daos = uaRepo.findAllByUserId(userId, pg);
        return new PageImpl<>(daos.getContent().stream().map(UserAuditUtil::fromDao).toList(), pg, daos.getTotalElements());

    }

    @Override
    @Transactional
    public Optional<UserAuditDto> addUserAuditEntry(long userId, UserAuditDto uaDto) {
        uaDto.setNum(uaRepo.getNextFreeNumForUser(userId));
        var dao = uaRepo.saveAndFlush(UserAuditUtil.fromDto(uaDto));
        return Optional.of(UserAuditUtil.fromDao(dao));
    }

    @Override
    @Transactional
    public Optional<UserAuditDto> addUserAuditInfo(long userId, int num, UserAuditInfoEntry infoEntry) {
        Optional<UserAuditDao> byId = uaRepo.findById(new UserAuditId(userId, num));
        if (byId.isPresent()) {
            UserAuditDao userAuditDao = byId.get();
            ArrayList<UserAuditInfoEntry> userAuditInfoEntries = new ArrayList<>(userAuditDao.getInfos().entries());
            userAuditInfoEntries.add(infoEntry);
            userAuditDao.setInfos(new UserAuditInfos(userAuditInfoEntries));
            uaRepo.saveAndFlush(userAuditDao);
            return Optional.of(UserAuditUtil.fromDao(userAuditDao));
        }
        return byId.map(UserAuditUtil::fromDao);
    }

    @Override
    void rerouteEvent(UserAuditDto dto) {
        rbTemplate.convertAndSend(com.ously.gamble.useraudit.configuration.UserAuditConfiguration.USERAUDIT_ADD_QUEUE, dto);
    }
}
