package com.ously.gamble.crm.customerio.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CIORepWHIdentifier {
    private String id;
    private String email;
    @JsonProperty("cio_id")
    private String cioId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCioId() {
        return cioId;
    }

    public void setCioId(String cioId) {
        this.cioId = cioId;
    }


    @Override
    public String toString() {
        return "CIORepWHIdentifier{" +
                "id='" + id + '\'' +
                ", email='" + email + '\'' +
                ", cioId='" + cioId + '\'' +
                '}';
    }
}
