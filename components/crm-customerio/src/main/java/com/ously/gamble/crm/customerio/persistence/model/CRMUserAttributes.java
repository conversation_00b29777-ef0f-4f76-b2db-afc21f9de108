package com.ously.gamble.crm.customerio.persistence.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class CRMUserAttributes {

    private final static Map<String, Object> EMPTY_MAP = Collections.emptyMap();

    private Map<String, Object> other;

    @JsonAnyGetter
    public Map<String, Object> any() {
        return Objects.requireNonNullElse(other, EMPTY_MAP);
    }

    @JsonAnySetter
    public void set(final String name, final Object value) {
        other = Objects.requireNonNullElse(other, new HashMap<>(10));
        other.put(name, value);
    }

    /**
     * @param aAtts CRMUserAttributes to diff against
     * @return a new CRMUserAttributes containing only attributes which are different in other
     */
    @JsonIgnore
    public CRMUserAttributes diff(CRMUserAttributes aAtts) {
        CRMUserAttributes diff = new CRMUserAttributes();

        // find all changes in aAtts and add those
        for (Map.Entry<String, Object> oA : aAtts.any().entrySet()) {
            if (any().containsKey(oA.getKey())) {
                if (!any().get(oA.getKey()).toString().equals(oA.getValue().toString())) {
                    diff.set(oA.getKey(), oA.getValue());
                }
            } else {
                diff.set(oA.getKey(), oA.getValue());
            }
        }
        // find all in this which are not in aAtts (and add as null value)
        for (String tKey : this.any().keySet()) {
            if (!aAtts.any().containsKey(tKey)) {
                diff.set(tKey, null);
            }
        }

        return diff;
    }

}
