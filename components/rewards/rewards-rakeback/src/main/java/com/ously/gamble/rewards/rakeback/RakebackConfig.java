package com.ously.gamble.rewards.rakeback;

import com.ously.gamble.api.configuration.DBConfigProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@ConditionalOnProperty(prefix = "features", name = "rakebacks", havingValue = "true")
@ConfigurationProperties(prefix = "rakeback")
@Component
public class RakebackConfig {

    private double dailyGgr;
    private double dailyHedge = 0.05d;
    private double dailyWager;
    private double dailyLevelFactor = 0.005d;

    private boolean weeklyEnabled;
    private double weeklyGgr;
    private double weeklyHedge = 0.015d;
    private double weeklyWager;
    private double weeklyLevelFactor = 0.001d;

    private boolean monthlyEnabled;
    private double monthlyGgr;
    private double monthlyHedge = 0.002d;
    private double monthlyWager;
    private double monthlyLevelFactor = 0.001d;

    @DBConfigProperty(key = "rakeback.weeklyenabled", description = """
            Boolean (true/false) for weekly rakebacks. Default='false'""")
    public boolean isWeeklyEnabled() {
        return weeklyEnabled;
    }

    public void setWeeklyEnabled(boolean weeklyEnabled) {
        this.weeklyEnabled = weeklyEnabled;
    }

    @DBConfigProperty(key = "rakeback.monthlyenabled", description = """
            Boolean (true/false) for monthly rakebacks. Default='false'""")
    public boolean isMonthlyEnabled() {
        return monthlyEnabled;
    }

    public void setMonthlyEnabled(boolean monthlyEnabled) {
        this.monthlyEnabled = monthlyEnabled;
    }


    // hedge *( dailyHedge + (level * dailyLevelFactor))

    @DBConfigProperty(key = "rakeback.weeklyggr", description = """
            The ggr factor for weekly rakebacks. Default='0.00'""")
    public double getWeeklyGgr() {
        return weeklyGgr;
    }

    public void setWeeklyGgr(double weeklyGgr) {
        this.weeklyGgr = weeklyGgr;
    }

    @DBConfigProperty(key = "rakeback.weeklyhedge", description = """
            The hedge factor for weekly rakebacks. Default='0.00'""")
    public double getWeeklyHedge() {
        return weeklyHedge;
    }

    public void setWeeklyHedge(double weeklyHedge) {
        this.weeklyHedge = weeklyHedge;
    }

    @DBConfigProperty(key = "rakeback.weeklywager", description = """
            The wager factor for weekly rakebacks. Default='0.00'""")
    public double getWeeklyWager() {
        return weeklyWager;
    }

    public void setWeeklyWager(double weeklyWager) {
        this.weeklyWager = weeklyWager;
    }

    @DBConfigProperty(key = "rakeback.weeklylevelfactor", description = """
            The level factor for weekly rakebacks. Default='0.00'""")
    public double getWeeklyLevelFactor() {
        return weeklyLevelFactor;
    }

    public void setWeeklyLevelFactor(double weeklyLevelFactor) {
        this.weeklyLevelFactor = weeklyLevelFactor;
    }

    @DBConfigProperty(key = "rakeback.monthlyggr", description = """
            The ggr factor for monthly rakebacks. Default='0.00'""")

    public double getMonthlyGgr() {
        return monthlyGgr;
    }

    public void setMonthlyGgr(double monthlyGgr) {
        this.monthlyGgr = monthlyGgr;
    }

    @DBConfigProperty(key = "rakeback.monthlyhedge", description = """
            The hedge factor for monthly rakebacks. Default='0.00'""")

    public double getMonthlyHedge() {
        return monthlyHedge;
    }

    public void setMonthlyHedge(double monthlyHedge) {
        this.monthlyHedge = monthlyHedge;
    }

    @DBConfigProperty(key = "rakeback.monthlywager", description = """
            The wager factor for monthly rakebacks. Default='0.00'""")
    public double getMonthlyWager() {
        return monthlyWager;
    }

    public void setMonthlyWager(double monthlyWager) {
        this.monthlyWager = monthlyWager;
    }

    @DBConfigProperty(key = "rakeback.monthlylevelfactor", description = """
            The level factor for monthly rakebacks. Default='0.00'""")

    public double getMonthlyLevelFactor() {
        return monthlyLevelFactor;
    }

    public void setMonthlyLevelFactor(double monthlyLevelFactor) {
        this.monthlyLevelFactor = monthlyLevelFactor;
    }

    @DBConfigProperty(key = "rakeback.dailyggr", description = """
            The ggr factor for daily rakebacks. Default='0.00'""")
    public double getDailyGgr() {
        return dailyGgr;
    }

    public void setDailyGgr(double v) {
        this.dailyGgr = v;
    }

    @DBConfigProperty(key = "rakeback.dailyhedge", description = """
            The hedge factor for daily rakebacks. Default='0.05'""")
    public double getDailyHedge() {
        return dailyHedge;
    }

    public void setDailyHedge(double dailyHedge) {
        this.dailyHedge = dailyHedge;
    }

    @DBConfigProperty(key = "rakeback.dailywager", description = """
            The wager factor for daily rakebacks. Default='0.00'""")
    public double getDailyWager() {
        return dailyWager;
    }

    public void setDailyWager(double dailyWager) {
        this.dailyWager = dailyWager;
    }

    @DBConfigProperty(key = "rakeback.dailylevelfactor", description = """
            The level multiplicator added to daily rakebacks. Default='0.00'""")
    public double getDailyLevelFactor() {
        return dailyLevelFactor;
    }

    public void setDailyLevelFactor(double dailyLevelFactor) {
        this.dailyLevelFactor = dailyLevelFactor;
    }
}



