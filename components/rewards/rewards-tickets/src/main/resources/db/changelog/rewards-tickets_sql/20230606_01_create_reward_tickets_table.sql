CREATE TABLE if not exists `rewards_ticket`
(
    `user_id`    BIGINT       NOT NULL,
    `qualifier`  VARCHAR(100) NOT NULL,
    `type`       VARCHAR(50)  NOT NULL,
    `created_at` TIMESTAMP    NOT NULL,
    `expires_at` TIMESTAMP    NOT NULL,
    `claimed`    bit(1)       NOT NULL DEFAULT b'0',
    `details`    JSON         NOT NULL,
    PRIMARY KEY (`user_id`, `qualifier`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

create index rewards_ticket_expiry_idx
    on rewards_ticket (expires_at);