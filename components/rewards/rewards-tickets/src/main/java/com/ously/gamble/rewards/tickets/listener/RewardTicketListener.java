package com.ously.gamble.rewards.tickets.listener;

import com.ously.gamble.api.rewards.tickets.AwardedRewardTicket;
import com.ously.gamble.api.rewards.tickets.RewardTicketService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.rewards.tickets.configuration.RewardTicketConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@ConditionalOnProperty(prefix = "rewards.tickets", name = "enabled", havingValue = "true")
@ConditionalOnOffloader
@RabbitListener(
        queues = {RewardTicketConfiguration.REWARD_CREATE_QUEUE},
        containerFactory =
                "directRabbitListenerContainerFactory", id = "rewardTicketContainer",
        concurrency = "1")
public class RewardTicketListener {
    private final Logger log = LoggerFactory.getLogger(RewardTicketListener.class);
    private final RewardTicketService cbService;

    public RewardTicketListener(RewardTicketService cbService) {
        this.cbService = cbService;
    }

    @RabbitHandler
    @Transactional
    public void createRewardTicketIfNotAvailable(AwardedRewardTicket e) {
        cbService.createNewTicket(e);
    }


}
