package com.ously.gamble.notification.handlers.email.persistence.model;

import com.ously.gamble.api.notification.MailOutLog;
import com.ously.gamble.api.notification.MailOutSentContent;
import com.ously.gamble.persistence.model.idclasses.UserMessageId;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import org.hibernate.annotations.Type;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "email_out")
@IdClass(UserMessageId.class)
public class MailOut implements Persistable<MailOutId> {

    @Column(name = "user_id", nullable = false)
    @Id
    private long userId;

    @Id
    @Column(name = "qualifier", length = 100, nullable = false)
    private String qualifier;

    @Type(JsonType.class)
    @Column(name = "content")
    private UserMessageContent content;

    @Type(JsonType.class)
    @Column(name = "logs")
    private MailOutLog logs;

    @Type(JsonType.class)
    @Column(name = "rendered")
    private MailOutSentContent sentContent;

    @Column(name = "sent_at")
    Instant sentAt;

    @Column(name = "next_send_at")
    Instant nextSendAt;

    @Column(name = "retries")
    int retries;

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Override
    public MailOutId getId() {
        return new MailOutId(userId, qualifier);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getQualifier() {
        return qualifier;
    }

    public void setQualifier(String qualifier) {
        this.qualifier = qualifier;
    }

    public UserMessageContent getContent() {
        return content;
    }

    public void setContent(UserMessageContent content) {
        this.content = content;
    }

    public MailOutLog getLogs() {
        return logs;
    }

    public void setLogs(MailOutLog logs) {
        this.logs = logs;
    }

    public Instant getSentAt() {
        return sentAt;
    }

    public void setSentAt(Instant sentAt) {
        this.sentAt = sentAt;
    }

    public Instant getNextSendAt() {
        return nextSendAt;
    }

    public void setNextSendAt(Instant nextSendAt) {
        this.nextSendAt = nextSendAt;
    }

    public int getRetries() {
        return retries;
    }

    public void setRetries(int retries) {
        this.retries = retries;
    }

    public MailOutSentContent getSentContent() {
        return sentContent;
    }

    public void setSentContent(
            MailOutSentContent sentContent) {
        this.sentContent = sentContent;
    }
}
