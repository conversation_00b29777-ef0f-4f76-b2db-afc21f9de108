package com.ously.gamble.gateways;

import com.ously.gamble.api.games.GameGateway;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GameGatewayConfiguration {

    @Bean
    DirectExchange gamesExchange() {
        return ExchangeBuilder.directExchange("games").durable(true).build();
    }

    @Bean
    Binding gameStatsChangedBinding() {
        return BindingBuilder.bind(gameStatsChangedQueue()).to(gamesExchange()).withQueueName();
    }

    @Bean
    Binding lbExpiryEventBinding() {
        return BindingBuilder.bind(lbExpiryEventQueue()).to(gamesExchange()).withQueueName();
    }

    @Bean
    Queue gameStatsChangedQueue() {
        return QueueBuilder.durable(GameGateway.STATISTICS_UPDATED)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", GameGateway.STATISTICS_UPDATED + "DLQ")
                .build();
    }

    @Bean
    Queue gameStatsChangedQueueDLQ() {
        return QueueBuilder.durable(GameGateway.STATISTICS_UPDATED + "DLQ").build();
    }

    @Bean
    Queue lbExpiryEventQueue() {
        return QueueBuilder.durable(GameGateway.LEADERBOARD_EXPIRY)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", GameGateway.LEADERBOARD_EXPIRY + "DLQ")
                .build();
    }

    @Bean
    Queue lbExpiryEventQueueDLQ() {
        return QueueBuilder.durable(GameGateway.LEADERBOARD_EXPIRY + "DLQ").build();
    }


}
