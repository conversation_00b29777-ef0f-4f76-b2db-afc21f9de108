spring.config.import=classpath:module-config/dataproxy.properties,\
  classpath:module-config/autoconfiguration-exclusions.properties,\
  classpath:module-config/spring.properties


#
# Base features
#
features.jurisdiction=SOC
features.platform=SPINARENA
features.sync.cron=0 53 * * * *
#
# RTP
#
seriously.rtp.minspins=10
#
# server settings
#
server.compression.enabled=true
server.http2.enabled=false
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=2048
server.jetty.max-http-form-post-size=10485760B
server.error.include-stacktrace=never
#
# Task scheduling pool configuration
#
spring.task.scheduling.pool.size=10
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=15s
#
# Conn pooling
#
spring.datasource.hikari.connectionTimeout=10000
spring.datasource.hikari.idleTimeout=900000
spring.datasource.hikari.maxLifetime=36000000
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.leak-detection-threshold=180000
spring.datasource.hikari.minimum-idle=4
spring.datasource.hikari.auto-commit=false
#
# JPA settings
#
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=true
spring.jpa.properties.jakarta.persistence.validation.mode=none
spring.jpa.properties.hibernate.cache-use_second_level_cache=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.properties.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.generate-ddl=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.type.preferred_instant_jdbc_type=TIMESTAMP
#
# Hibernate stuff
#
hibernate.types.print.banner=false
logging.level.org.springframework.ws.server=WARN
logging.level.org.hibernate=WARN
logging.level.org.hibernate.INFO=INFO
logging.level.org.hibernate.SQL=INFO
logging.level.org.hibernate.stat=INFO
logging.level.org.hibernate.cache=INFO
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.hibernate.orm.incubating=ERROR
logging.level.net.javacrumbs.shedlock=INFO
logging.level.org.springframework.amqp.rabbit.listener=WARN
#
# Spring SQL
#
spring.sql.init.mode=never
#
# Spring data
#
spring.data.jpa.repositories.bootstrap-mode=default
spring.data.redis.repositories.enabled=false
#
# default pattern parser
#
spring.mvc.pathmatch.matching-strategy=path_pattern_parser
#
# jackson configuration
#
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC
spring.jackson.deserialization.fail-on-unknown-properties=false
#
# Liquibase defaults
#
logging.level.liquibase=INFO
spring.liquibase.contexts=unittest
#
# Actuator/Management
#
management.endpoint.beans.cache.time-to-live=10s
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path=/actuator
management.endpoints.web.exposure.include=health,prometheus,info
management.endpoint.health.show-details=ALWAYS
management.endpoint.health.enabled=true
management.endpoint.prometheus.enabled=true
management.endpoint.info.enabled=true
management.endpoint.metrics.enabled=true
management.health.mail.enabled=false
management.endpoint.health.probes.enabled=true
##management.metrics.web.server.request.autotime.enabled=false
##management.metrics.web.client.request.autotime.enabled=false
management.endpoints.jmx.exposure.exclude=*
management.httpexchanges.recording.enabled=false
management.metrics.data.repository.autotime.enabled=false
management.auditevents.enabled=false
#management.trace.http.enabled=false
management.health.db.enabled=false
management.health.redis.enabled=false
#
# Multipart upload settings
#
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=11MB
spring.servlet.multipart.enabled=true
spring.servlet.multipart.resolve-lazily=false
spring.servlet.multipart.location=${java.io.tmpdir}
#
# Video Ads Configuration
#
video.ad.maxRewardedAdViewsPerDay=3
video.ad.minRewardAmount=80.0
video.ad.maxRewardAmount=120.0
video.ad.rewardBias=0.5

#
# AWS config
#
cloud.aws.region.static=eu-central-1
cloud.aws.region.auto=false
cloud.aws.stack.auto=false
aws.s3.accessKey=********************
aws.s3.secretKey=v/4EIJfNoXvfMIMGSRe1upEh9xlECPS5+8OQnFbD
aws.s3.bucketname=spinarena-assets
aws.sns.accessKey=********************
aws.sns.secretKey=pQuFWsz0BevFqOxnVYsxuThcXZw+PnDA42Myh1BP
aws.translate.accessKey=********************
aws.translate.secretKey=XpJLX4AV+a9k2NIk5eOMwmXYbMBclqEK+FykXZzM
#
# Rabbit def. settings
#

spring.rabbitmq.listener.type=direct
spring.rabbitmq.listener.direct.prefetch=5
spring.rabbitmq.listener.direct.consumers-per-queue=1
spring.rabbitmq.listener.direct.retry.enabled=true
spring.rabbitmq.listener.direct.retry.initial-interval=100ms
spring.rabbitmq.listener.direct.retry.max-attempts=4
spring.rabbitmq.listener.direct.retry.max-interval=1200ms
spring.rabbitmq.listener.direct.retry.multiplier=2
spring.rabbitmq.listener.direct.default-requeue-rejected=false

# templates
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.retry.initial-interval=200ms
spring.rabbitmq.template.retry.max-interval=1000ms
spring.rabbitmq.template.retry.multiplier=2


# channel
spring.rabbitmq.cache.connection.mode=channel
spring.rabbitmq.cache.channel.size=500
spring.rabbitmq.cache.channel.checkout-timeout=PT5S
spring.rabbitmq.address-shuffle-mode=random

#  connection
#spring.rabbitmq.cache.connection.mode=connection
#spring.rabbitmq.cache.connection.size=25
#spring.rabbitmq.cache.channel.size=512
#spring.rabbitmq.cache.channel.checkout-timeout=PT15S
#spring.rabbitmq.address-shuffle-mode=random

#
# Firebase settings
#
firebase.projectid=spinarena-3a4ff
firebase.projectname=Spinarena
#
# MESSAGES
#
features.messages=true
features.socialfeatures=true
#
# Economic settings
#
level.levelCoinScale=150

#
# Slotoptions
#
slotoptions.levelBarrier1=15
slotoptions.levelBarrier2=30

#
# Leaderboards
#
leaderboards.enabled=true
leaderboards.topentries=15
leaderboards.enabledTypes=MAXMULT_DAYS30,MAXWIN_DAYS30,MAXMULT_ALLTIME,MAXWIN_ALLTIME
leaderboards.ttlseconds=300
leaderboards.cachesize=1000
leaderboards.concurrency=5

#
# Level Settings
#
level.auto.enabled=false
level.auto.maxlevels=500
level.auto.additive-rewards=true
level.auto.console=false
level.auto.coinincrease=0.0110
level.auto.xpincrease=0.02
level.auto.diamonds-remainder=100, 50, 10
level.auto.xpbooster-remainder=6,9
level.auto.coinbooster-remainder=555,1555

#
# Useraudit
#
useraudit.enabled=true

#
# CRM Base settings
#
crm.enabled=false
crm.devices.enabled=false
crm.usertags.enabled=false
crm.usertags.excluded=
crm.events.enabled=false
crm.events.excluded=game_category_list,navigate_to_screen,navigate_from_screen,ClearFilter
customerio.enabled=${settings.customerio.enabled}
customerio.baseUrl=https://track-eu.customer.io/api/v1
#
# freenarker, no check of template directories
#
spring.freemarker.check-template-location=false