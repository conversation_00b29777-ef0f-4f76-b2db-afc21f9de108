#
# Settings / features
#
features.baseUrl=http://localhost:8090
features.autoActive=false
features.mailfrom=<EMAIL>
features.fakeValidation=true
features.stage=dev
features.testing=true
features.popupsEnabled=true
features.jurisdiction=DE
features.thumbsBase=https://d2do3nvj2be99e.cloudfront.net/shared_local
# RTP Service settings
seriously.rtp.minspins=10
#
# Spring DATASOURCE (DataSourceAutoConfiguration & DataSourceProperties)
#
spring.datasource.url=*********************************************************************************************************************
spring.datasource.readurl=*********************************************************************************************************************
spring.datasource.username=cpp
spring.datasource.password=fatass
#
# Support
#
support.email=<EMAIL>
support.system=LC
#
# Actuator/mgmt
#
management.endpoint.mappings.enabled=true
management.endpoint.startup.enabled=true
management.endpoints.jmx.exposure.include=health,mappings
management.endpoints.web.exposure.include=health,prometheus,info,startup
management.endpoints.jmx.exposure.exclude=
#
# Liquibase
#
spring.liquibase.contexts=${features.stage}

#
# RabbitMQ
#
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.addresses=amqp://guest:guest@localhost:5673
#
# Our own redisson properties
#
ously.redisson.host=localhost
ously.redisson.password=fatass69
ously.redisson.port=6380
ously.redisson.ssl=false
# SINGLE, REPLICATED
ously.redisson.type=SINGLE
# SLAVE,MASTER,MASTER_SLAVE (for replicated)
ously.redisson.mode=MASTER_SLAVE
#
# Gamemanager
#
seriously.gamemanager.key=gmgrDevP3r346i3!2e9BE22
#
# jpa logging
#
logging.level.org.hibernate.orm.incubating=ERROR
