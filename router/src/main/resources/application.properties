#
# PROFILE
#
spring.jmx.enabled=false
spring.profiles.active=dev
spring.application.name=ously-router
#spring.config.import=classpath:common-config/shared.properties
server.port=8071
#
# Jackson View
#
spring.jackson.mapper.DEFAULT_VIEW_INCLUSION=true
spring.freemarker.check-template-location=false
springdoc.api-docs.enabled=false
#
# Pragmatic config
#
pragmatic.aggregated=true
#
# Playngo config
#
playngo.aggregated=true
#
# Target defs
#
router.defaultTarget=SA
router.namespace.SA=spinarena-staging
router.namespace.TR=twistroyal-staging