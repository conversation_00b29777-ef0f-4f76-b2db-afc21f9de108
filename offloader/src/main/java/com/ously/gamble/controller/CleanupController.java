package com.ously.gamble.controller;

import com.ously.gamble.maintenance.UserMaintenanceService;
import com.ously.gamble.shared.controller.BaseCleanupController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/admin")
public class CleanupController extends BaseCleanupController {

    @Autowired
    UserMaintenanceService userMaintenanceSrv;

    @Operation(description = "identify and save unused anon accounts", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cleanup/user/unusedAnon")
    @RolesAllowed("ADMIN")
    public void identifyUnusedAnon() {
        userMaintenanceSrv.findAndSaveRemovalCandidatesAnon();
    }

    @Operation(description = "stage 1 cleanup for specific user", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cleanup/user/stage1/{userId}")
    @RolesAllowed("ADMIN")
    public void userCleanupStart(@PathVariable("userId") Long userId) {
        userMaintenanceSrv.triggerStageOne(userId);
    }

    @Operation(description = "stage 1 cleanup for all candidates", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cleanup/user/stage1")
    @RolesAllowed("ADMIN")
    public void userCleanupStartAll() {
        userMaintenanceSrv.triggerStageOne();
    }


    @Operation(description = "identify and save unused non-anon accounts", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cleanup/user/unused")
    @RolesAllowed("ADMIN")
    public void identifyUnused() {
        userMaintenanceSrv.findAndSaveRemovalCandidatesNonAnon();
    }

}
