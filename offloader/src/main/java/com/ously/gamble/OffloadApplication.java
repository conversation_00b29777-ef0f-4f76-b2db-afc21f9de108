package com.ously.gamble;

import com.ously.gamble.util.GitInfo;
import jakarta.annotation.PostConstruct;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.core.SpringVersion;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Locale;
import java.util.TimeZone;

@SpringBootApplication(scanBasePackages = "com.ously.gamble")
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT30m")
@EnableRetry
@EnableJpaAuditing
@EntityScan(basePackages = "com.ously.gamble")
@EnableJpaRepositories(basePackages = "com.ously.gamble")
public class OffloadApplication {

    @PostConstruct
    void init() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }

    public static void main(String... args) {
        System.out.print("LOCALE: " + Locale.getDefault() + " | ");
        System.out.println("GIT: " + GitInfo.getGitInfoString());
        System.out.println("Spring Version : '" + SpringVersion.getVersion() + "', SpringBoot Version: '" + SpringBootVersion.getVersion() + '\'');

        SpringApplication.run(OffloadApplication.class, args);
    }


}
