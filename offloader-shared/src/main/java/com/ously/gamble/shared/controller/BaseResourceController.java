package com.ously.gamble.shared.controller;

import com.ously.gamble.api.resource.ResourceService;
import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.persistence.model.resource.ResourceDataDto;
import com.ously.gamble.persistence.model.resource.ResourceDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

public class BaseResourceController {

    private final ResourceService resourceService;

    public BaseResourceController(ResourceService rSrv) {
        this.resourceService = rSrv;
    }

    // Get all Resource definitions
    @Operation(description = "get all resource definitions", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/resource")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Collection<ResourceDto>> getResources() {
        try {
            return ResponseEntity.ok(resourceService.getAllResources());
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Get a single ResourceDefinition
    @Operation(description = "get a resourceDefinition", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/resource/{component}/{name}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<ResourceDto> getResource(
            @PathVariable(value = "component") String component,
            @PathVariable(value = "name") String name
    ) {
        return ResponseEntity.of(resourceService.getResource(component, name));
    }

    // Get all versions of a Resource
    @Operation(description = "get all versions of a resource", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/resource/{component}/{name}/version")
    @RolesAllowed("ADMIN")
    public ResponseEntity<List<ResourceDataDto>> getResourceVersions(
            @PathVariable(value = "component") String component,
            @PathVariable(value = "name") String name
    ) {
        return ResponseEntity.ok(resourceService.getAllResourceVersions(component, name));
    }

    // Get all versions of a Resource
    @Operation(description = "get a specific version of a resource", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/resource/{component}/{name}/version/{version}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<ResourceDataDto> getResourceVersion(
            @PathVariable(value = "component") String component,
            @PathVariable(value = "name") String name,
            @PathVariable(value = "version") int version
    ) {
        return ResponseEntity.of(resourceService.getResourceVersion(component, name, version));
    }


    // Activate a resource version
    @Operation(description = "activate a resource version", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/resource/{component}/{name}/version/{version}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<ResourceDataDto> activateResourceVersion(@PathVariable(value = "component") String component,
                                                                   @PathVariable(value = "name") String name,
                                                                   @PathVariable(value = "version") int version,
                                                                   @CurrentUser UserPrincipal adminUser) {
        return ResponseEntity.of(resourceService.activateResourceVersion(component, name, version, adminUser.getId()));
    }


    // Create a new resource version
    @Operation(description = "create a new resource version (upload)", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/resource/{component}/{name}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @RolesAllowed("ADMIN")
    public ResponseEntity<ResourceDataDto> createNewResourceVersion(@PathVariable(value = "component") String component,
                                                                    @PathVariable(value = "name") String name,
                                                                    @Parameter(content = @Content(
                                                                            mediaType = MediaType.APPLICATION_OCTET_STREAM_VALUE))
                                                                    @RequestParam(value = "file") MultipartFile file,
                                                                    @CurrentUser UserPrincipal adminUser) throws IOException {
        return ResponseEntity.of(resourceService.storeNewResourceData(component, name, file.getBytes(),
                "'" + file.getName() + "' uploaded by " + adminUser.getId()));
    }

}
