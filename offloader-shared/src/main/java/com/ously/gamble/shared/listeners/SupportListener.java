package com.ously.gamble.shared.listeners;

import com.ously.gamble.api.events.UserEventGateway;
import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationSendService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@Profile("!test")
@RabbitListener(queues = {UserEventGateway.USER_EVENT_CONTACTREQUESTS_QUEUE, UserEventGateway.USER_EVENT_NOTIFICATION_QUEUE}, containerFactory =
        "directRabbitListenerContainerFactory", id = "userContactContainer")
public class SupportListener {

    private final NotificationSendService notSndSrv;

    public SupportListener(NotificationSendService notSndSrv) {
        this.notSndSrv = notSndSrv;
    }

    @RabbitHandler
    @Transactional
    public void handleRequestsBoth(Message message) {
        notSndSrv.sendNotification(message);
    }


}
