package com.ously.gamble.shared.crm;

import com.ously.gamble.api.crm.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(prefix = "customerio", name = "enabled", havingValue = "false",
        matchIfMissing = true)
public class LoggingCrmAdapter implements CRMAdapter {
    final Logger log = LoggerFactory.getLogger(LoggingCrmAdapter.class);

    public LoggingCrmAdapter() {
        log.warn("CRM Service is just logging");
    }


    @Override
    public void storeUserUpdate(CRMUserUpdate upd) {
        if (upd == null) {
            return;
        }
        log.info("CRM-Log: updateUser={}", upd.getUserId());
    }

    @Override
    public void storeUserUpdates(CRMUserUpdates upds) {
        upds.getUpdates().forEach(this::storeUserUpdate);
    }

    @Override
    public void storeEvent(CRMUserEvent upd) {
        log.info("CRM-Log: storeEvent={}:{}", upd.getUserId(), upd.getName());
    }

    @Override
    public void storePush(CRMPushEvent pev) {
        log.info("CRM-Log: pushEvent={}:{}", pev.getDeliveryId(), pev.getEvent());
    }

    @Override
    public void storeDevice(CRMDeviceMessage upd) {
        log.info("CRM-Log: storeDevice={}:{}", upd.getUid(), upd.getDevice());
    }

    @Override
    public void storeEvents(CRMUserEvents events) {
        log.info("CRM-Log: storeEvents, count={}", events.getEvents().size());
    }

    @Override
    public void deleteUID(String uid, boolean suppress) {
        log.info("CRM-Log: removeUID '{}' suppress={}", uid, suppress);
    }

    @Override
    public void deleteDevice(String uid, String deviceId) {
        log.info("CRM-Log: remove device '{}' for user '{}'", deviceId, uid);
    }

    @Override
    public void doUsertagChange(CRMUserTagChangeEvent tagChangeEvent) {
        log.info("CRM-Log: usertag change '{}'", tagChangeEvent);

    }

}
