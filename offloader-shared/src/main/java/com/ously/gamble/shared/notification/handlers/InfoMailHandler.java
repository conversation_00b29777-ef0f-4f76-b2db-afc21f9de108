package com.ously.gamble.shared.notification.handlers;

import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationHandler;
import com.ously.gamble.api.notification.Receipt;
import com.ously.gamble.config.SupportConfig;
import com.ously.gamble.persistence.model.NotificationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;


@Component
public class InfoMailHandler implements NotificationHandler {

    private final Logger log = LoggerFactory.getLogger(InfoMailHandler.class);

    private final SupportConfig sConfig;

    private final JavaMailSender mailSender;

    private final FeatureConfig features;


    public InfoMailHandler(SupportConfig sConfig, JavaMailSender mailSender,
                           FeatureConfig features) {
        this.sConfig = sConfig;
        this.mailSender = mailSender;
        this.features = features;
    }

    @Override
    public NotificationType supportedType() {
        return NotificationType.INFO;
    }

    @Override
    public Receipt sendOut(Message msg) {
        var stage = features.getStage();
        try {
            var mailmsg = new SimpleMailMessage();
            mailmsg.setText(msg.getMsg());
            mailmsg.setFrom(msg.getDestination());
            mailmsg.setTo(sConfig.getEmail());
            mailmsg.setSubject(stage + ':' + msg.getSubject());
            mailSender.send(mailmsg);
        } catch (Exception e) {
            log.error("Sending mail to {} failed: {}", msg.getDestination(), e.getMessage());
            return Receipt.error(msg, e.getMessage());
        }
        return Receipt.ok(msg, "OK:none");
    }

}
