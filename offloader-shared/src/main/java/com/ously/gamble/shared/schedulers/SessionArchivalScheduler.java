package com.ously.gamble.shared.schedulers;

import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.api.session.SessionTaskService;
import com.ously.gamble.config.SessionArchivalConfiguration;
import com.ously.gamble.config.SessionExternalizeConfiguration;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(prefix = "session.archival", name = "enabled", havingValue = "true")
public class SessionArchivalScheduler {

    private final SessionTaskService ssTaskService;
    private final SessionArchivalConfiguration ssArchConfig;
    private final SessionExternalizeConfiguration ssExtConfig;
    private final ScheduleExecutionService schedSrv;

    public SessionArchivalScheduler(SessionTaskService ssTskSrv,
                                    SessionArchivalConfiguration ssCfg,
                                    SessionExternalizeConfiguration ssExtCfg,
                                    ScheduleExecutionService schedSrv) {
        this.ssTaskService = ssTskSrv;
        this.ssArchConfig = ssCfg;
        this.schedSrv = schedSrv;
        this.ssExtConfig = ssExtCfg;
    }

    @Scheduled(cron = "${session.archival.cronexpression:0 16 * * * *}")
    @SchedulerLock(name = "sessionArchivalCandidateScheduler", lockAtLeastFor = "PT3M")
    public void sessionArchivalCandidateSelection() {
        schedSrv.doSchedule(new ScheduledTaskInformation("base", "sessionArchivalCandidateSelection", null), this::doSessionArchivalCandidateSelection);
    }

    private ScheduledTaskInformation doSessionArchivalCandidateSelection(ScheduledTaskInformation sti) {
        long minAgeDays = ssArchConfig.getMinimumagedays();
        long maxLimit = ssArchConfig.getSchedulelimit();
        var numSessScheduled = ssTaskService.createSessionArchivalTasks(minAgeDays, 30, maxLimit);
        return sti;
    }


    @Scheduled(cron = "0 36 * * * *")
    @SchedulerLock(name = "sessionArchivalRetryScheduler", lockAtLeastFor = "PT3M")
    public void sessionArchivalCandidateRetry() {
        schedSrv.doSchedule(new ScheduledTaskInformation("base", "sessionArchivalRetries", null), this::doSessionArchivalRetries);
    }

    private ScheduledTaskInformation doSessionArchivalRetries(ScheduledTaskInformation sti) {
        var numSessScheduled = ssTaskService.resendPendingArchivalTasks(25000);
        return sti;
    }


    @Scheduled(cron = "${session.externalize.cronexpression:0 */15 * * * *}")
    @SchedulerLock(name = "sessionExternalizeCandidateScheduler", lockAtLeastFor = "PT3M")
    public void sessionExternalizeCandidateSelection() {
        if (ssExtConfig.isEnabled()) {
            schedSrv.doSchedule(new ScheduledTaskInformation("base", "sessionExternalizeCandidateSelection", null), this::doSessionExternalizeCandidateSelection);
        }
    }

    private ScheduledTaskInformation doSessionExternalizeCandidateSelection(ScheduledTaskInformation sti) {
        long minAgeDays = ssExtConfig.getMinimumagedays();
        long maxLimit = ssExtConfig.getSchedulelimit();
        var numSessScheduled = ssTaskService.queueSessionExternalizeTasks(minAgeDays, maxLimit);
        return sti;
    }


}
