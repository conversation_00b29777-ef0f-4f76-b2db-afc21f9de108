package com.ously.gamble.shared.sync;

import com.ously.gamble.api.gamemanager.*;
import com.ously.gamble.api.slotcatalog.SlotcatalogService;
import com.ously.gamble.persistence.repository.game.GameRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
class SyncServiceImpl implements SyncService {
    private static final Logger log = LoggerFactory.getLogger(SyncServiceImpl.class);

    private final SlotcatalogService scService;

    private final GameRepository gameRepo;

    private final GameManagerService gManager;
    private final SyncComponent<GMProvider> vSync;
    private final SyncComponent<GMIntegration> iSync;
    private final SyncComponent<GMIntegrationVendor> ivSync;
    private final SyncComponent<GMGameInfo> infoSync;
    private final SyncComponent<GMCategory> catSync;
    private final SyncComponent<GMInfoContent> icSync;
    private final SyncComponent<GMGameImage> giSync;
    private final SyncComponent<GMGame> gameSync;
    private final SyncComponent<GMGameTag> gameTagSync;

    SyncServiceImpl(GameManagerService gMgrService,
                    SyncComponent<GMProvider> vSync,
                    SyncComponent<GMIntegration> giSync,
                    SyncComponent<GMIntegrationVendor> ivSync,
                    SyncComponent<GMGameInfo> iSync,
                    SyncComponent<GMCategory> cSync,
                    SyncComponent<GMInfoContent> icSync,
                    SyncComponent<GMGameImage> gimgSync,
                    SyncComponent<GMGame> gameSync,
                    SyncComponent<GMGameTag> gameTagSync,
                    SlotcatalogService scSrv,
                    GameRepository gRp
    ) {
        this.gManager = gMgrService;
        this.vSync = vSync;
        this.iSync = giSync;
        this.ivSync = ivSync;
        this.infoSync = iSync;
        this.catSync = cSync;
        this.icSync = icSync;
        this.giSync = gimgSync;
        this.gameSync = gameSync;
        this.scService = scSrv;
        this.gameRepo = gRp;
        this.gameTagSync = gameTagSync;
    }

    @Override
    public List<SyncStep> doSync(boolean simulate) {

        var ctx = new SyncContext();

        List<SyncStep> results = new ArrayList<>();
        // Vendors
        var success = syncVendors(ctx, simulate);
        results.add(new SyncStep("vendors", success));
        if (!success) {
            return results;
        }

        // Integrations
        success = syncIntegrations(ctx, simulate);
        results.add(new SyncStep("integrations", success));
        if (!success) {
            return results;
        }

        // Integration-Vendor Mapping
        success = syncIntegrationVendors(ctx, simulate);
        results.add(new SyncStep("integration-vendors", success));
        if (!success) {
            return results;
        }

        // Integration-Vendor Mapping
        success = syncGameTags(ctx, simulate);
        results.add(new SyncStep("game-tags", success));
        if (!success) {
            return results;
        }


        // GameCategories
        // GameInfos
        success = syncGameCategories(ctx, simulate);
        results.add(new SyncStep("game-categories", success));
        if (!success) {
            return results;
        }


        // GameInfos
        success = syncGameInfos(ctx, simulate);
        results.add(new SyncStep("game-infos", success));
        if (!success) {
            return results;
        }


        // GameInfoContentSync
        success = syncGameInfoContents(ctx, simulate);
        results.add(new SyncStep("game-info-content", success));
        if (!success) {
            return results;
        }

        // Game Sync
        success = syncGames(ctx, simulate);
        results.add(new SyncStep("games", success));
        if (!success) {
            return results;
        }

        // Postprocess / relink games
        relinkGames(ctx.getRelinkGameIDs(), simulate);

        // Game Image Sync
        success = syncGameImages(ctx, simulate);
        results.add(new SyncStep("game-images", success));
        return results;
    }

    private void relinkGames(Set<Long> relinkGameIDs, boolean simulate) {

        log.info("Relinking {} games (new,updated)", relinkGameIDs.size());
        if (simulate) {
            return;
        }
        for (var gId : relinkGameIDs) {
            var theGame = gameRepo.findById(gId);
            if (theGame.isPresent()) {
                var game = theGame.get();
                if (game.getGameInfo() == null) {
                    scService.linkGameInfoToGame(null, game.getId(), true);
                } else {
                    scService.linkGameInfoToGame(game.getGameInfo().getId(), game.getId(), true);
                }
            }
        }
    }

    boolean syncGames(SyncContext ctx, boolean simulate) {
        var contents = gManager.getGames();

        if (contents.isEmpty()) {
            log.warn("Cannot load games from gamemanager, aborting sync");
            return false;
        }
        return gameSync.doSync(contents.get(), ctx, simulate);
    }

    boolean syncGameImages(SyncContext ctx, boolean simulate) {
        var contents = gManager.getImages();

        if (contents.isEmpty()) {
            log.warn("Cannot load gameImages from gamemanager, aborting sync");
            return false;
        }
        return giSync.doSync(contents.get(), ctx, simulate);
    }


    boolean syncGameInfoContents(SyncContext ctx, boolean simulate) {
        var contents = gManager.getInfoContents();

        if (contents.isEmpty()) {
            log.warn("Cannot load infoContents from gamemanager, aborting sync");
            return false;
        }
        return icSync.doSync(contents.get(), ctx, simulate);
    }


    boolean syncGameCategories(SyncContext ctx, boolean simulate) {
        var cats = gManager.getGameCategories();

        if (cats.isEmpty()) {
            log.warn("Cannot load game-categories from gamemanager, aborting sync");
            return false;
        }
        return catSync.doSync(cats.get(), ctx, simulate);
    }

    boolean syncGameInfos(SyncContext ctx, boolean simulate) {
        var infos = gManager.getGameInfos();

        if (infos.isEmpty()) {
            log.warn("Cannot load game-infos from gamemanager, aborting sync");
            return false;
        }
        return infoSync.doSync(infos.get(), ctx, simulate);
    }


    public boolean syncIntegrations(SyncContext ctx, boolean simulate) {
        var integrations = gManager.getGameIntegrations();

        if (integrations.isEmpty()) {
            log.warn("Cannot load integrations from gamemanager, aborting sync");
            return false;
        }
        return iSync.doSync(integrations.get(), ctx, simulate);
    }


    public boolean syncIntegrationVendors(SyncContext ctx, boolean simulate) {
        var integrationsVendors = gManager.getGameIntegrationVendors();

        if (integrationsVendors.isEmpty()) {
            log.warn("Cannot load integrationVendor mapping from gamemanager, aborting sync");
            return false;
        }
        return ivSync.doSync(integrationsVendors.get(), ctx, simulate);
    }


    public boolean syncVendors(SyncContext ctx, boolean simulate) {
        var providers = gManager.getProviders();
        if (providers.isEmpty()) {
            log.warn("Cannot load vendors from gamemanager, aborting sync");
            return false;
        }
        return vSync.doSync(providers.get(), ctx, simulate);
    }

    public boolean syncGameTags(SyncContext ctx, boolean simulate) {
        var tags = gManager.getGametags();
        return gameTagSync.doSync(tags.get(), ctx, simulate);
    }


}
