I would propose a rule based Levelling and Achievements system.

Both manageable via database and formulated in a DSL

see https://www.baeldung.com/drools-spring-integration

but the DRL files are loaded (and managed via database)

DRL files per rule,achievement or excel ? ( will clarify, embed at least a simple rule )

evtl. i write a DSL using MPS ... Seems a viable option with a lot of leeway to integrate more enhanced stuff.

# Levelling

Level-Rules are named and an attribute of a game. A Levelling Rule can be used for any number of games. Of course you
can define special rules for single games. I would also like to allow rules to override or extend rules (like for a
short period of time (easter weekend double Bonus levelling))
To make levelling attractive, there should be perks or "free spinz" upon reaching various levels. How to define this ?

Leveling games is based on the GameLevel Entity (where i keep number of plays, wins, ... per game)
Leveling rules should never trigger a levelUp twice! (the level-up goody is only emitted once)

# Achievements

Achievements are global rules which are not necessarily game specific (but could be - i would advise against it in the
beginning).

So i would like to have a bean which contains all specific game levels and achievement rules:

* FastAndTheFurious:  less than 100 spinz bets but more than 500 spinz win.
* Shakalakka:  in any game: more free plays than paid bets
* The librarian: Level 3 in all "book of ..." games (Book of Ra, Book of dead, Book of Spasti)

Achievement rules are triggered near-time and not after every transaction. A user should not notice a delayed
achievement handling though. So at least after 5 seconds the achievement should be triggered.

Achievement rules are a little bit more complex und expensive. The might involve all GameLevels of a user. Evtl. we
organize the cache in LevelManager slightly different.

# Types of rules

- bet/win (rulez are checking against single bets/wins)
- in session (rulez are checking against the current session/game)
- daily (rulez are checking against a day)
- game based (aggregated ALL for user/game category)
    - all book of xxx games played
    - all fruit games played
    - ...

# execution of rulez

- perfect example for a queuing system with priorisation?
- after each play/bet
- check bet/win rulez
- send out msg to check in session and daily

An achievement (or a loot box) contains certain consumables, XP and money. XP and money are added to the wallet when the
achievement is claimed. The consumable are kept in an extra table.

Consumables are:

- certain GEMs
    - red gem allows for Wheel of Fortune plays
    - green gem allows for special WoF plays
    - ...
- multiplier (1.1 - 2.0) for XP and or wins (5min to 2h).
    - multiplier mechanism is already active




