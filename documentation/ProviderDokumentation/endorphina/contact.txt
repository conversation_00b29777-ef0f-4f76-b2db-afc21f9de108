skype: api.endorphina

Hello,

During your development phase, please feel free to write me (via Skype
or email) with any technical questions or the problem you are
experiencing. Both contacts are listed at the bottom of this
introduction e-mail.

INTEGRATION PROCESS

So the integration process for Seamless API via Endorphina works as
follows

0) In attachment you can find all relevant documents for integration
(APIs, testing utilities etc.).

1) The first step would be understanding the Seamless API documentation
'ENDORPHINA_SEAMLESS_GAME_INTEGRATION_API_EN.pdf'. I highly recommend to
take it step by step. It includes all relevant information you will need
for the integration.

2) When you have successfully completed the development against this
documentation you can start performing acceptance tests via SoapUI
testing tool. SoapUI is web service testing application that will
ensures your development is correct via created test cases. You will
find more information about SoapUI testing in folder 'Seamless
Tests/Automatic'. You will find there two files. The .txt contains guide
information how to start testing and the .xml file is the file with test
cases. Notice the version of SoapUI (SoapUI 5.1.3). Please download and
install exactly this version!

3) When you pass all the tests (all are green), you are ready for
testing. You will send us your test results. Then, we require that you
send us your:

Endpoint URL - URL where is Seamless API integrated

Currency - Currency which will be used only in testing (not in
production). Default is EUR.

Then we issue test keys for you and you may proceed to testing scenario
that is described in pdf file 'Seamless API Checklist' in folder
'Seamless Tests'. There you find 6 mandatory scenarios and 1 optional
(If you plan to use local jackpots). Each scenario test specific
functionality of Seamless API. You MUST pass all the test correctly!

If you are experiencing any error, please send us whole server log
message including:

   - What you request you got

   - What you respond to that request (including header fields and
message body)

4) To complete the integration we require from your side a test account
for our testing purposes. Once we verified correctly transposed
integration process we can take the process of integration as finished.

5) The next step is preparing for production. You will discuss this
with our sales department.

6) We are also offering edemo (fun-mode) solution of our games.
Implementation is quite easy and straightforward. You only form an edemo
launch request that will open the selected game and that is it. Every
bet/win/refund/balance communication is running on our side without any
API needed. To restrict others from using your edemo merchant ID
(accountId) we are whitelisting the domain and server IP from which the
edemo launch is requested. So, when requesting an edemo merchant ID
please always provide the domain URL and server IPs that we should
whitelist

Edemo API doc can be found here:
http://edemo.endorphina.com/public/integration/api/

TROUBLESHOOTING

For troubleshooting purposes, please develop a log that tracks HTTP
communication between Endorphina system and External system (your side).

If an issue occurs, you will send us:

   - What you request you got

   - What you responded to that request (including header fields and
message body)

SUMMARY

   We recommend careful reading of the Seamless documentation. Then you
develop your end-point against Seamless API methods. After developing
you will test it via SoapUI acceptance tests. If you pass on those you
send us SoapUI test results and we give you test keys so you can start
testing directly in games with testing scenarios.

Please, be informed that we do accept ONE NODE ID per website, keep it
in mind during your integration.

INFORMATION

IPs for whitelisting

* ************* - cdn.endorphina.com

* ************** - test.endorphina.com, edemo.endorphina.com

Kind regards,

Daniela

Endorphina API Integration Manager