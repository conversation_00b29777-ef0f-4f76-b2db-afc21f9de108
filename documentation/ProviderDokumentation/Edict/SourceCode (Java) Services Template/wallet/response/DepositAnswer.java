package de.edict.eoc.integration.gaming.wallet.response;

import de.edict.eoc.implementation.wallet.dto.response.DepositResponseDTO;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class DepositAnswer implements SoapResponse {

    public static final long serialVersionUID = 1L;

    @XmlElement
    private double balance;

    @XmlElement
    private String transactionId;

    public DepositAnswer() {
    }

    public DepositAnswer(double balance, String transactionId) {
        this.balance = balance;
        this.transactionId = transactionId;
    }

    public DepositAnswer(DepositResponseDTO depositResponseDTO) {
        this(depositResponseDTO.getBalance() != null ? depositResponseDTO.getBalance() : 0, depositResponseDTO.getTransactionId());
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
