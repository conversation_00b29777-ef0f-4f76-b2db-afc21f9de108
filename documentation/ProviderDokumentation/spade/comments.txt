
Hello @<PERSON><PERSON> Enclosed here with new merchant's MIF documents and BO login credentials for your reference and further action.

Staging : http://bo-egame-staging.spadecasino777.com/login.jsp
mercahantCode : SARENA
Username : SARENAADMIN
Password : Aa123456@




"http://lobby-egame-staging.spadecasino777.com/SARENA/auth/?acctId=xxxx&language=en_US&token=xxxx&game=xxxx
https://lobby-egame-staging.spadecasino777.com/SARENA/auth/?acctId=xxxx&language=en_US&token=xxxx&game=xxxx
"

token is used only for auth.

In later stages (balance, transfer) the acctId is used to identify a session. clarification with spade is ongoing if i can use the acctId as token attribute.


------- LIVE

https://lobby.olivelorisplay.com/SARENA/auth/?acctId=xxxx&language=en_US&token=xxxx&game=xxxx

Hello team, Enclosed here with your prod env documents and BO login credentials for your reference and further action.

Live : http://backoffice.oliveloris.com/login.jsp
mercahantCode : SARENA
Username : SARENAADMIN
Password : Aa123456@