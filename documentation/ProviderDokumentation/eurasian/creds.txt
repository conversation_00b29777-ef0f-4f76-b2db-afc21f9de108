"user": "ously_user_01",
        "pass": "ously_passw_p01"

game_list:

https://sd01.gamingworld.net/rgs/api/admin.js?usr=ously_user_01&passw=ously_passw_p01&action=available_games
(brand=gw -> slot)

// LAUNCH URL
/rgs/api/admin?action=get_game&usr=ously_user_01&passw=ously_passw_p01&game_id=118&remote_id=testplayer


"game_url":"https://sd01.gamingworld.net/rgs/views/gw/embed.html?sid=S6ea5ae"


sid is usable as a token. So i will use it prefixed internally (EURASIA-S674393)


