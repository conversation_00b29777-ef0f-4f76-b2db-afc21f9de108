package com.ously.gamble.api.rewards;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;

import java.time.Instant;
import java.time.LocalDate;

public record UserRakebackDto(
        @JsonProperty("userId")
        long userId,
        @JsonProperty("rdate")
        LocalDate rDate,
        @JsonProperty("period")
        String period,
        @JsonProperty("amount")
        int amount,
        @JsonSerialize(using = InstantSerializer.class)
        @JsonProperty("expireAt")
        Instant expireAt,
        @JsonProperty("utxId")
        Long utxId,
        @JsonProperty("vals")
        String vals
) {
}
