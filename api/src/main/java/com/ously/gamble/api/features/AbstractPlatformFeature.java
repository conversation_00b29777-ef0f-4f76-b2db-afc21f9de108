package com.ously.gamble.api.features;

import java.util.Collections;
import java.util.List;

public abstract class AbstractPlatformFeature implements PlatformFeature {

    private static final List<String> EMPTY_STRING_LIST = Collections.emptyList();


    public List<String> getDailyAnalyzeTables() {
        return EMPTY_STRING_LIST;
    }

    public List<String> getWeeklyAnalyzeTables() {
        return EMPTY_STRING_LIST;
    }

}
