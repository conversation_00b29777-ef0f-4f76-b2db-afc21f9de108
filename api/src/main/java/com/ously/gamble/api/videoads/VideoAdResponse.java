package com.ously.gamble.api.videoads;

import java.util.List;
import java.util.UUID;

public record VideoAdResponse(
        UUID uuid,
        VideoAdStatus status,
        int viewCount,
        int maxViews,
        Double reward,
        Double minReward,
        Double maxReward,
        List<VideoAdReward> rewards
) {
    public VideoAdResponse(UUID uuid, VideoAdStatus status, int viewCount, int maxViews, Double reward) {
        this(uuid, status, viewCount, maxViews, reward, null, null, List.of());
    }

    public VideoAdResponse(UUID uuid, VideoAdStatus status, int viewCount, int maxViews, Double minReward, Double maxReward) {
        this(uuid, status, viewCount, maxViews, null, minReward, maxReward, List.of());
    }

    public VideoAdResponse(UUID uuid, VideoAdStatus status, int viewCount, int maxViews) {
        this(uuid, status, viewCount, maxViews, null, null, null, List.of());
    }

    public VideoAdResponse(VideoAdStatus status, int viewCount, int maxViews) {
        this(null, status, viewCount, maxViews, null, null, null, List.of());
        if (status != VideoAdStatus.LIMIT_REACHED) {
            throw new IllegalArgumentException("This constructor is only for LIMIT_REACHED status.");
        }
    }

    @Deprecated
    public VideoAdResponse(UUID uuid, VideoAdStatus status, int viewCount, int maxViews, List<VideoAdReward> rewards) {
        this(uuid, status, viewCount, maxViews, null, null, null, rewards);
    }
}