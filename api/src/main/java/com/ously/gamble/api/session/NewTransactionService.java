package com.ously.gamble.api.session;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.session.SessionRound;
import com.ously.gamble.persistence.model.session.SessionTransaction;

import java.util.List;


public interface NewTransactionService {


    TxResponse addTxFromProvider(TxRequest req) throws OuslyTransactionException;

    SessionTransaction findByUserIdAndSessionIdAndOrigId(long userId, long sessionId, String origId);

    TxResponse performRollback(SessionTransaction tr, Wallet wallet);

    List<SessionTransaction> findRoundTransactionsForUserIdAndSessionId(long userId, long sessionId, String roundId);

    SessionRound findRoundForSessionIdAndRoundReference(long userId, long sessionId, String roundRef);

    SessionTransaction findTransactionForUIDAndOrigIdAndBridgeName(long uid, String transactionId, String vendorName);
}
