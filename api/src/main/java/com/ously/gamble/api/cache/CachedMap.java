package com.ously.gamble.api.cache;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@SuppressWarnings("UnusedReturnValue")
public interface CachedMap<K, V> {
    String getCacheName();

    V get(K key);

    V get(K key, V defValue);

    V put(K key, V value);

    V put(K key, V value, long ttl, TimeUnit ttlUnit);

    V putIfAbsent(K key, V value);

    V putIfAbsent(K key, V value, long ttl, TimeUnit ttlUnit);

    V remove(K key);

    void putFast(K key, V value);

    void putFast(K key, V value, long ttl, TimeUnit ttlUnit);

    void putIfAbsentFast(K key, V value);

    void putIfAbsentFast(K key, V value, long ttl, TimeUnit ttlUnit);

    void removeFast(K key);

    boolean contains(K key);

    int size();

    int localSize();

    void clear();

    Collection<K> getKeys();

    int expireEntries();

    V getOrComputeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction);
}
