package com.ously.gamble.api.events;

import com.ously.gamble.persistence.model.user.UserEventType;

import java.util.HashMap;
import java.util.Map;

public class UserEventBean {

    public UserEventBean() {
    }

    Long userId;
    UserEventType type;
    String remAdr;
    Map<String, Object> other;

    public UserEventBean(Long id, String remAdrrStr, UserEventType evType, Map<String, Object> other) {
        this.userId = id;
        this.remAdr = remAdrrStr;
        this.type = evType;
        this.other = other;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRemAdr() {
        return remAdr;
    }

    public void setRemAdr(String remAdr) {
        this.remAdr = remAdr;
    }

    public Map<String, Object> getOther() {
        return other;
    }

    public void setOther(Map<String, Object> other) {
        this.other = other;
    }

    public UserEventType getType() {
        return type;
    }

    public void setType(UserEventType type) {
        this.type = type;
    }

    public void addToOther(String key, Object value) {
        if (this.other == null) {
            this.other = new HashMap<>();
        }
        this.other.put(key, value);
    }
}
