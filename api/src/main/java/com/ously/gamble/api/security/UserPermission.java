package com.ously.gamble.api.security;

import com.ously.gamble.persistence.model.security.Permission;

public class UserPermission {

    String name;
    String description;

    public UserPermission(Permission p){
        this.name=p.getName();
        this.description=p.getDescription();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
