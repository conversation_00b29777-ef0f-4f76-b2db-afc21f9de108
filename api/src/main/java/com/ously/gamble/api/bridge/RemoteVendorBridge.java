package com.ously.gamble.api.bridge;

import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Jurisdiction;

import java.util.List;
import java.util.Map;

public interface RemoteVendorBridge {

    CreateFreespinsResponse createFreespins(CreateFreespinsRequest req, String tag);

    CreateSessionResponse createGameSession(CreateSessionRequest req, String tag);

    GameInstance createGameInstance(Long userId, CasinoGame game, GamePlatform platform,
                                    GameSettings settings, Long nonce, String crypt,
                                    Jurisdiction jd, String tag) throws Exception;

    List<String> getProviderNames(String tag);

    List<AvailableGame> getAvailableGamesFromVendor(String vendorName, String tag);

    Map<String, List<AvailableGame>> getAvailableGames(String tag);


    @FunctionalInterface
    interface CheckedBridgeMethod<T> {
        T apply() throws Exception;
    }

    @FunctionalInterface
    interface CheckedBridgeMethodC<T, V> {
        T apply(V tag) throws Exception;
    }

    @FunctionalInterface
    interface BridgeMethod<T> {
        T apply();
    }

    @FunctionalInterface
    interface BridgeMethodC<T, V> {
        T apply(V tag);
    }
}
