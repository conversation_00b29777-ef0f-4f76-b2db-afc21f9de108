package com.ously.gamble.api.games;

import com.ously.gamble.persistence.model.game.GameTag;

import java.util.List;
import java.util.Set;

public interface GameTagService {
    List<GameTag> getAllGameTags();

    void updateOrSaveGameTag(CasinoGameTag casinoGameTag);

    List<CasinoGameTag> getAllCasinoGametags();

    Set<Integer> getAllCasinoGametagsForGame(long gameId);

    Set<Integer> updateTagsForGame(long gameId, Set<Integer> newTags2);

    boolean hasGameTagId(long gameId, String tagName);
}
