package com.ously.gamble.api.games;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ously.gamble.persistence.projections.CategoryPJ;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Category implements Serializable, Comparable<Category> {

    private static final long[] emptyLArray = new long[0];

    @NotNull
    String name;
    @NotNull
    int id;
    @NotNull
    long[] games;

    @JsonIgnore
    transient List<Long> addBuffer;

    public Category() {
    }

    public Category(String name, int id) {
        this.name = name;
        this.id = id;
        this.games = emptyLArray;
    }

    public Category(CategoryPJ pj) {
        this.name = pj.getName();
        this.id = pj.getId();
        this.games = Arrays.stream(pj.getGamelist().split(",")).mapToLong(Long::valueOf).toArray();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long[] getGames() {
        return games;
    }

    public void addGame(long gId) {
        if (addBuffer == null) {
            addBuffer = new ArrayList<>(10);
        }
        addBuffer.add(gId);
    }

    public void finish() {
        if (addBuffer != null && !addBuffer.isEmpty()) {
            List<Long> gIds = new ArrayList<>(addBuffer);
            Arrays.stream(games).forEach(gIds::add);
            games = gIds.stream().mapToLong(l -> l).toArray();
        }
        addBuffer = null;
    }

    public void setGames(long... games) {
        this.games = games;
    }

    @Override
    public int compareTo(Category o) {
        return o.id - id;
    }

    public boolean containsGame(long a) {
        for (var l : games) {
            if (a == l) {
                return true;
            }
        }
        return false;
    }

}
