package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

import java.util.Map;

public class WebhookSendFCMPushRequest extends WebhookRequest {
    @NotEmpty
    @JsonProperty(value = "userid")
    long userId;

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    @NotEmpty
    @JsonProperty(value = "eventname")
    String eventName;

    Map<String, String> parameters;

    public Map<String, String> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, String> parameters) {
        this.parameters = parameters;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    @Override
    public String toString() {
        return "WebhookSendFCMPushRequest{" +
                "userId='" + userId + '\'' +
                '}';
    }
}
