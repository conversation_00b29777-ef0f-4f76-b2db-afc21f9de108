package com.ously.gamble.api;

import com.ously.gamble.api.consumable.LevelCoinMultRanges;
import com.ously.gamble.persistence.model.Purchase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface PurchaseManagementService {

    LevelCoinMultRanges getCoinMultRanges();

    LevelCoinMultRanges setCoinMultRanges(LevelCoinMultRanges lcmr);

    int getCoinMultiplierForLevel(int level);

    Optional<Purchase> findById(Long id);

    Optional<Purchase> findByTransactionIdAndUserId(String transactionId, Long userId);

    Optional<Purchase> findByUserIdAndPsp(Long userId, String psp);

    Optional<Purchase> findByPsp(String psp);

    Page<Purchase> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    Page<Purchase> findAllPageableWithinPeriod(LocalDateTime from, LocalDateTime to,
                                               Pageable pageable);

    List<Purchase> getAllByUserId(Long userId);

    List<Purchase> getAllByUserIdOrderByCreatedAtDesc(Long userId);

    Purchase save(Purchase p);

    Purchase saveAndFlush(Purchase p);

    Optional<Purchase> findByUserIdAndOrderRef(Long id, String orderRef);
}
