package com.ously.gamble.api.user;


import com.ously.gamble.persistence.model.user.UserLogins;
import jakarta.validation.constraints.NotNull;

import java.time.Instant;

public class CasinoUserEvent {
    String country;
    @NotNull
    String eventType;
    String ipadress;
    String platform;
    @NotNull
    long userId;
    Instant timestamp;

    public CasinoUserEvent() {
    }

    public CasinoUserEvent(UserLogins ul) {
        country = ul.getCountryCode();
        eventType = "LOGIN";
        ipadress = ul.getIpadress();
        platform = ul.getPlatform().name();
        userId = ul.getUserId();
        timestamp = ul.getCreatedAt();
    }


    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getIpadress() {
        return ipadress;
    }

    public void setIpadress(String ipadress) {
        this.ipadress = ipadress;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Instant getTimestamp() {
        return timestamp;
    }
}
