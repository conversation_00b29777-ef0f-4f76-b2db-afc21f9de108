package com.ously.gamble.api.missions;

import java.io.Serializable;
import java.time.Instant;

public class UMP implements Serializable {
    long targetValue;
    long currentValue;
    long validUntilESUTC;
    long lastUpdateESUTC;

    public UMP() {
    }

    public UMP(long tV, long cV, long vS) {
        this.targetValue = tV;
        this.currentValue = cV;
        this.validUntilESUTC = vS;
        this.lastUpdateESUTC = Instant.now().getEpochSecond();
    }

    public UMP(long tV, long cV, long vS, long lastChng) {
        this.targetValue = tV;
        this.currentValue = cV;
        this.validUntilESUTC = vS;
        this.lastUpdateESUTC = lastChng;
    }

    public UMP(UMP old, long newV) {
        this.targetValue = old.targetValue;
        this.currentValue = newV;
        this.validUntilESUTC = old.validUntilESUTC;
        this.lastUpdateESUTC = Instant.now().getEpochSecond();
    }

    public long getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(long targetValue) {
        this.targetValue = targetValue;
    }

    public long getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(long currentValue) {
        this.currentValue = currentValue;
    }


    public long getValidUntilESUTC() {
        return validUntilESUTC;
    }

    public void setValidUntilESUTC(long validUntilESUTC) {
        this.validUntilESUTC = validUntilESUTC;
    }

    public long getLastUpdateESUTC() {
        return lastUpdateESUTC;
    }

    public void setLastUpdateESUTC(long lastUpdateESUTC) {
        this.lastUpdateESUTC = lastUpdateESUTC;
    }
}
