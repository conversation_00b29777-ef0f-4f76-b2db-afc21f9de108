package com.ously.gamble.api.localisation;


import java.io.Serializable;

public class Localised implements Serializable {

    Long id;
    String literalId;
    LanguageCode langcode;
    String template;

    LocalisationType type;

    public Localised() {
    }

    public Localised(Long id, LanguageCode lc, String literalId, String template, LocalisationType lType) {
        this.id = id;
        this.langcode = lc;
        this.template = template;
        this.literalId = literalId;
        this.type = lType;
    }

    public String getKey() {
        return literalId + ':' + langcode.name();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLiteralId() {
        return literalId;
    }

    public void setLiteralId(String literalId) {
        this.literalId = literalId;
    }

    public LanguageCode getLangcode() {
        return langcode;
    }

    public void setLangcode(LanguageCode langcode) {
        this.langcode = langcode;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public LocalisationType getType() {
        return type;
    }

    public void setType(LocalisationType type) {
        this.type = type;
    }
}
