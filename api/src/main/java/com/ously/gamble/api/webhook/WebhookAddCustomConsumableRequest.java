package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

public record WebhookAddCustomConsumableRequest(
        @NotEmpty
        WebhookAction action,
        @NotEmpty
        @JsonProperty(value = "userid")
        long userId,
        // Java Duration
        @NotEmpty
        String duration,
        boolean popup,
        long consumableId,
        int discount,
        int multiplier,
        String crmId,
        String campaign,
        String title,
        String msgTitle,
        String msgBody
        ) {


}
