package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

import java.util.Map;

public class WebhookAddPopupsRequest extends WebhookRequest {
    @NotEmpty
    @JsonProperty(required = true, value = "userid")
    String userId;

    @JsonProperty(value = "ttl", defaultValue = "PT1H")
    String ttl;

    Map<String, Object> data;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTtl() {
        return ttl;
    }

    public void setTtl(String ttl) {
        this.ttl = ttl;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "WebhookAddPopupsRequest{" +
                "userId='" + userId + '\'' +
                ", ttl='" + ttl + '\'' +
                ", data=" + data +
                ", sender='" + sender + '\'' +
                ", action=" + action +
                '}';
    }
}
