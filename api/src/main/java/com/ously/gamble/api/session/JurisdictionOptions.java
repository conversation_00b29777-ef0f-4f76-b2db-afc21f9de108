package com.ously.gamble.api.session;

import java.math.BigDecimal;

public record JurisdictionOptions(
        boolean socialFeatures, BigDecimal taxFactor, boolean levellingEnabled,
        BigDecimal maxBet, boolean bonusEnabled, BigDecimal maxBonusBet, float bonusWagerRtpMax
) {

    public JurisdictionOptions(
            boolean socialFeatures, BigDecimal taxFactor, boolean levellingEnabled,
            BigDecimal maxBet, boolean bonusEnabled, BigDecimal maxBonusBet) {
        this(socialFeatures, taxFactor, levellingEnabled, maxBet, bonusEnabled, maxBonusBet, 99.9f);
    }

    @Override
    public String toString() {
        return "OtherOptions( taxFactor=" + taxFactor + ", maxBet=" + maxBet + " )";
    }
}
