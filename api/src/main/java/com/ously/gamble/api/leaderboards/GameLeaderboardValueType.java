package com.ously.gamble.api.leaderboards;

public enum GameLeaderboardValueType {

    MAXWIN(0, "maximum win", "maxwin"),
    MAXMULT(1, "maximum multiplier", "maxmult");
    private final int ival;
    private final String valueName;
    private final String lbFieldname;

    GameLeaderboardValueType(int ival, String valueName, String lbFieldname) {
        this.ival = ival;
        this.valueName = valueName;
        this.lbFieldname = lbFieldname;
    }

    public int ival() {
        return ival;
    }

    public String valueName() {
        return valueName;
    }

    public String lbFieldname() {
        return lbFieldname;
    }
}

