package com.ously.gamble.api.games;

import com.ously.gamble.api.vendor.CasinoSlotProvider;
import jakarta.validation.constraints.NotNull;

public class GameProviderInfo {

    @NotNull
    private int id;
    @NotNull
    private String name;
    private String homepage;
    private String blockedCountries;

    public GameProviderInfo(){}

    public GameProviderInfo(CasinoSlotProvider csp){
        this.id=csp.getId();
        this.name=csp.getProviderName();
        this.homepage = csp.getProviderHomepage();
        this.blockedCountries = csp.getBlockedCountries();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHomepage() {
        return homepage;
    }

    public void setHomepage(String homepage) {
        this.homepage = homepage;
    }

    public String getBlockedCountries() {
        return blockedCountries;
    }

    public void setBlockedCountries(String blockedCountries) {
        this.blockedCountries = blockedCountries;
    }
}
