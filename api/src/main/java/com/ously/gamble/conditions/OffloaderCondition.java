package com.ously.gamble.conditions;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.lang.NonNull;

import java.util.Objects;

public class OffloaderCondition implements Condition {
    @Override
    public boolean matches(@NonNull ConditionContext context,
                           @NonNull AnnotatedTypeMetadata metadata) {
        try {
            Objects.requireNonNull(context.getClassLoader()).loadClass("com.ously.gamble.OffloadApplication");
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
