package com.ously.gamble.conditions;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.lang.NonNull;

public class SocialCondition implements Condition {
    @Override
    public boolean matches(@NonNull ConditionContext context,
                           @NonNull AnnotatedTypeMetadata metadata) {
        try {
            return context.getEnvironment().getProperty("features.socialfeatures", boolean.class,
                    false);
        } catch (Exception e) {
            return false;
        }
    }
}
