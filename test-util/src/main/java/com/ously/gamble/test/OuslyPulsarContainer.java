//package com.ously.gamble.test;
//
//import org.testcontainers.containers.PulsarContainer;
//import org.testcontainers.images.PullPolicy;
//
//import java.time.Duration;
//import java.time.temporal.ChronoUnit;
//import java.util.UUID;
//
//public class OuslyPulsarContainer extends PulsarContainer {
//
//    private static PulsarContainer container;
//
//    public static PulsarContainer getInstance() {
//        if (container == null) {
//            try {
/// /                var rbtmp = Files.createTempDirectory(Path.of("build/tmp"), "rbtmp");
/// /                rbtmp.toFile().deleteOnExit();
/// /                var s = rbtmp.toString();
//                container = new OuslyPulsarContainer("3.0.7")
//                        .withEnv("PULSAR_PREFIX_bookieId", UUID.randomUUID().toString())
//                        .withEnv("PULSAR_PREFIX_clusterName", UUID.randomUUID().toString())
//                        .withEnv("PULSAR_MEM", "-Xms512m -Xmx512m -XX:MaxDirectMemorySize=1g")
//                        .withImagePullPolicy(PullPolicy.ageBased(Duration.of(100, ChronoUnit.DAYS)))
//                        //                   .withFileSystemBind(s, "/var/lib/rabbitmq", BindMode.READ_WRITE)
//                        .withStartupTimeout(Duration.ofSeconds(120));
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return container;
//    }
//
//    protected OuslyPulsarContainer(String imgName) {
//        super(imgName);
//    }
//
//    @Override
//    public void stop() {
//        //do nothing, JVM handles shut down
//    }
//}