package com.ously.gamble.shared.listener;

import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.features.LevelUpEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * The event handler could be moved into LevelManager/LevelService
 */
@Service
public class LevelUpListener {

    private final ApplicationEventPublisher eventPublisher;

    public LevelUpListener(ApplicationEventPublisher aep) {
        this.eventPublisher = aep;
    }

    // TODO: we do not need to publish, we could just return the CRMUserEvent

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEvent(LevelUpEvent luEvent) {
        eventPublisher.publishEvent(new CRMUserEvent(luEvent.getId(), "levelup",
                "level_reached", luEvent.getLevel()));
    }

}
