buildscript {
    repositories {
        maven {
            url = uri("https://plugins.gradle.org/m2/")
        }
    }

    dependencies {
        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}


plugins {
    id 'java-library'
}

configurations {
    querydsl.extendsFrom implementation, compileOnly, runtimeOnly, annotationProcessor
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
}


if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}

dependencies {
    implementation "io.swagger.core.v3:swagger-annotations:${swaggerCoreVersion}"
    api "org.liquibase:liquibase-core", {
//        exclude group: "javax.inject", module: "javax.inject"
    }


    api("org.springframework.data:spring-data-commons")
    api("org.springframework.data:spring-data-jpa")
    api("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation 'org.springframework.boot:spring-boot-actuator'
    api 'jakarta.validation:jakarta.validation-api'
    implementation 'org.apache.commons:commons-lang3'

    api 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
    annotationProcessor 'com.querydsl:querydsl-apt:5.0.0:jakarta'
    annotationProcessor("jakarta.persistence:jakarta.persistence-api")
    annotationProcessor("jakarta.annotation:jakarta.annotation-api")
    implementation project(':devtools:querydsl-expressions')

    api project(':devtools:querydsl-expressions')
    implementation 'io.hypersistence:hypersistence-utils-hibernate-62:3.4.3'
    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    api 'org.apache.commons:commons-math3:3.6.1'

//    implementation group: 'org.mariadb.jdbc', name: 'mariadb-java-client'
//    implementation group: 'software.aws.rds', name: 'aws-mysql-jdbc', version: "${awsJdbcVersion}"

    implementation group: 'software.amazon.jdbc', name: 'aws-advanced-jdbc-wrapper', version: "2.5.2"
    implementation 'com.mysql:mysql-connector-j:9.1.0'

    implementation 'net.ttddyy:datasource-proxy:1.7'

    testImplementation 'org.skyscreamer:jsonassert:1.5.1'
}