package com.ously.gamble.persistence.repository.game;


import com.ously.gamble.persistence.dto.GameIntegrationsVendorDto;
import com.ously.gamble.persistence.model.game.GameIntegrationsVendor;
import com.ously.gamble.persistence.model.idclasses.GameIntegrationsVendorId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GameIntegrationVendorRepository extends JpaRepository<GameIntegrationsVendor, GameIntegrationsVendorId> {

    @Query(nativeQuery = true)
    List<GameIntegrationsVendorDto> getAllIntegrationMappings();

    @Query(nativeQuery = true)
    List<GameIntegrationsVendorDto> getAllIntegrationMappingsForIntegration(int iId);
}
