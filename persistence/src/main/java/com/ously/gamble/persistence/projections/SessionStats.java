package com.ously.gamble.persistence.projections;

import java.math.BigDecimal;

public interface SessionStats {

    Long getEarnedXp();

    BigDecimal getMaxBet();

    String getMaxDate();

    BigDecimal getMaxWin();

    Long getNumPlays();

    Long getNumWins();

    BigDecimal getSumBet();


    BigDecimal getSumBoost();

    BigDecimal getSumWin();


    String getType();

    BigDecimal getMaxMultiplier();

    String getStartDate();

    String getEndDate();

}
