package com.ously.gamble.persistence.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.time.Instant;

public record GameInfoContentDto(
        @JsonProperty("infoId")
        int infoId,
        @JsonProperty("contentId")
        int contentId,
        @JsonProperty("filenameCdn")
        String filenameCdn,
        @JsonProperty("visible")
        boolean visible,
        @JsonProperty("fetched")
        boolean fetched,
        @JsonProperty("createdAt")
        Instant createdAt,
        @JsonProperty("updatedAt")
        Instant updatedAt,
        @JsonProperty("format")
        String format) implements Serializable {

    @Override
    public String toString() {
        return getClass().getSimpleName() + '(' +
                "infoId = " + infoId + ", " +
                "contentId = " + contentId + ", " +
                "filenameCdn = " + filenameCdn + ", " +
                "visible = " + visible + ", " +
                "fetched = " + fetched + ", " +
                "createdAt = " + createdAt + ", " +
                "updatedAt = " + updatedAt + ", " +
                "format = " + format + ')';
    }
}
