package com.ously.gamble.persistence.model;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

@MappedSuperclass
public class BaseGeneratedId {
    @Id
    @GeneratedValue(generator = "pooled")
    @GenericGenerator(name = "pooled", type = org.hibernate.id.enhanced.TableGenerator.class, parameters = {
            @Parameter(name = "table_name", value = "custom_sequences"),
            @Parameter(name = "value_column_name", value = "sequence_next_hi_value"),
            @Parameter(name = "prefer_entity_table_as_segment_value", value = "true"),
            @Parameter(name = "optimizer", value = "pooled-lo"),
            @Parameter(name = "initial_value", value = "100000"),
            @Parameter(name = "increment_size", value = "1000")})
    Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
