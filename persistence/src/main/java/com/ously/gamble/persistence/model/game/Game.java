package com.ously.gamble.persistence.model.game;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ously.gamble.persistence.dto.CachedGame;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Set;

@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties(value = {"createdAt", "updatedAt"}, allowGetters = true)
@NamedNativeQuery(name = "Game.getCacheable", query = """
         select g.id,
          g.name,
          g.type,
          g.genre,
          g.jackpot,
          coalesce(g.rtp, gi.rtp) as rtp,
          v.vendor_name   as providerName,
          g.provider_name as bridgeName,
          g.vendor_id as vendorId,
          coalesce(gi.slot_rank,99999)    as slotRank,
          coalesce(g.sort_order,gi.slot_rank,99999)    as sortOrder,
          gcc.catlist     as catlist,
          g.active        as active,
          g.android       as android,
          g.ios           as ios,
          g.desktop       as desktop,
          g.level         as level,
          v.betlevel      as betLevels,
          coalesce(DATE_FORMAT(g.release_date, '%Y-%m-%d'),DATE_FORMAT(gi.release_date, '%Y-%m-%d') ,DATE_FORMAT(g.created_at, '%Y-%m-%d'),'') as rDate,
          coalesce(gmtg.taglist,'') as taglist,
          coalesce(v.blocked_countries,'') as blockedCountries
         from games g
          join vendors v on g.vendor_id = v.id and v.active is true
          left join (select
              game_id,group_concat(concat(gc.id, '§', gc.name, '§',coalesce(gc.parent_id,-1), '§', gc.type, '§',if(gc.hidden = true or gcp.hidden=true, 'true', 'false')) SEPARATOR '§§§') as catlist
              from game_category_link gcl
               join game_category gc on gcl.category_id = gc.id
             left join game_category gcp on gc.parent_id = gcp.id group by game_id) gcc on g.id = gcc.game_id
          left join (select tgs.game_id, group_concat(gt.name) as taglist from game_tags tgs join game_tag gt on tgs.tag_id = gt.id group by tgs.game_id) gmtg on g.id=gmtg.game_id
          left join game_info gi on g.info_id=gi.id
        """, resultSetMapping = "Mapping.CacheableGame")

@NamedNativeQuery(name = "Game.getCacheableById", query = """
         select g.id,
          g.name,
          g.type,
          g.genre,
          g.jackpot,
          coalesce(g.rtp, gi.rtp) as rtp,
          v.vendor_name   as providerName,
          g.vendor_id as vendorId,
          g.provider_name as bridgeName,
          coalesce(gi.slot_rank,99999)    as slotRank,
          coalesce(g.sort_order,gi.slot_rank,99999)    as sortOrder,
          gcc.catlist     as catlist,
          g.active        as active,
          g.android       as android,
          g.ios           as ios,
          g.desktop       as desktop,
          g.level         as level,
          v.betlevel      as betLevels,
          coalesce(DATE_FORMAT(g.release_date, '%Y-%m-%d'),DATE_FORMAT(gi.release_date, '%Y-%m-%d') ,DATE_FORMAT(g.created_at, '%Y-%m-%d'),'') as rDate,
          coalesce(gmtg.taglist,'') as taglist,
          coalesce(v.blocked_countries,'') as blockedCountries
         from games g
          join vendors v on g.vendor_id = v.id and v.active is true
          left join (select game_id, group_concat(concat(gc.id, '§', gc.name, '§',coalesce(gc.parent_id,-1), '§', gc.type, '§',if(gc.hidden = true or gcp.hidden=true, 'true', 'false')) SEPARATOR '§§§') as catlist
          from game_category_link gcl
          join game_category gc on gcl.category_id = gc.id
          left join game_category gcp on gc.parent_id = gcp.id
          where gcl.game_id = ?1
          group by game_id) gcc on g.id = gcc.game_id
          left join (select tgs.game_id, group_concat(gt.name) as taglist from game_tags tgs join game_tag gt on tgs.tag_id = gt.id group by tgs.game_id) gmtg on g.id=gmtg.game_id
          left join game_info gi on g.info_id=gi.id
          where g.id=  ?1
        """, resultSetMapping = "Mapping.CacheableGame")

@SqlResultSetMapping(name = "Mapping.CacheableGame",
        classes = @ConstructorResult(targetClass = CachedGame.class,
                columns = {
                        @ColumnResult(name = "id",
                                type = long.class),
                        @ColumnResult(name = "name",
                                type = String.class),

                        @ColumnResult(name = "type",
                                type = String.class),
                        @ColumnResult(name = "genre",
                                type = String.class),
                        @ColumnResult(name = "jackpot",
                                type = String.class),
                        @ColumnResult(name = "rtp",
                                type = BigDecimal.class),
                        @ColumnResult(name = "providerName",
                                type = String.class),
                        @ColumnResult(name = "vendorId",
                                type = int.class),
                        @ColumnResult(name = "bridgeName",
                                type = String.class),
                        @ColumnResult(name = "slotrank",
                                type = int.class),
                        @ColumnResult(name = "sortOrder",
                                type = int.class),
                        @ColumnResult(name = "catlist",
                                type = String.class),
                        @ColumnResult(name = "active",
                                type = boolean.class),
                        @ColumnResult(name = "android",
                                type = boolean.class),
                        @ColumnResult(name = "ios",
                                type = boolean.class),
                        @ColumnResult(name = "desktop",
                                type = boolean.class),
                        @ColumnResult(name = "level",
                                type = int.class),
                        @ColumnResult(name = "betLevels",
                                type = int.class),
                        @ColumnResult(name = "rDate",
                                type = String.class),
                        @ColumnResult(name = "taglist",
                                type = String.class),
                        @ColumnResult(name =
                                "blockedCountries",
                                type = String.class)
                }))
@Entity
@Table(name = "games",
        uniqueConstraints = @UniqueConstraint(columnNames = {"provider_name", "game_id"}))
public class Game implements Persistable<Long>, Comparable<Game> {

    @Transient
    private boolean isNew = true;

    @Override
    @Transient
    public boolean isNew() {
        return this.isNew;
    }

    @PostLoad
    @PrePersist
        // needs: @EntityListeners(AuditingEntityListener.class)
    void trackNotNew() {
        this.isNew = false;
    }


    //    @CreatedDate
    @Column(name = "created_at")
    private Instant createdAt;

    //    @LastModifiedDate
    @Column(name = "updated_at")
    private Instant updatedAt;

    @Id
    private Long id;

    @Column(name = "provider_name", length = 128, nullable = false)
    private String providerName;

    @OneToOne(cascade = CascadeType.REFRESH, fetch = FetchType.EAGER)
    @JoinColumn(name = "info_id", referencedColumnName = "id")
    private GameInfo gameInfo;

    @OneToOne(cascade = CascadeType.REFRESH, fetch = FetchType.EAGER)
    @JoinColumn(name = "vendor_id", referencedColumnName = "id")
    private Vendor vendor;

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST,
            CascadeType.MERGE})
    @JoinTable(name = "game_category_link", joinColumns = @JoinColumn(name = "game_id"),
            inverseJoinColumns = @JoinColumn(name = "category_id"))
    private Set<GameCategory> categories;

    @Column(name = "game_id", length = 128, nullable = false)
    private String gameId;

    @Column(name = "name")
    @NotBlank
    @Size(max = 100)
    private String name;

    @Column(name = "active", nullable = false)
    private boolean active;

    @Column(name = "mobile", nullable = false)
    private boolean mobile = true;

    @Column(name = "ios", nullable = false)
    private boolean ios = true;

    @Column(name = "android", nullable = false)
    private boolean android = true;

    @Column(name = "desktop", nullable = false)
    private boolean desktop = true;

    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "rtp")
    private BigDecimal rtp;

    @Column(name = "level")
    private int level;

    @Column(name = "key_changed_at")
    private Instant keyChangedAt;

    @Column(name = "type", length = 30)
    @Enumerated(EnumType.STRING)
    private GameType type;

    @Column(name = "genre", length = 50)
    @Enumerated(EnumType.STRING)
    private GameGenre genre;

    @Column(name = "jackpot", length = 30)
    @Enumerated(EnumType.STRING)
    private GameJackpotMode jackpotMode;

    @Column(name = "release_date")
    private LocalDate releaseDate;

    public GameType getType() {
        return type;
    }

    public void setType(GameType type) {
        this.type = type;
    }

    public GameGenre getGenre() {
        return genre;
    }

    public void setGenre(GameGenre genre) {
        this.genre = genre;
    }

    public GameJackpotMode getJackpotMode() {
        return jackpotMode;
    }

    public void setJackpotMode(GameJackpotMode jackpotMode) {
        this.jackpotMode = jackpotMode;
    }

    public LocalDate getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }

    public Instant getKeyChangedAt() {
        return keyChangedAt;
    }

    public void setKeyChangedAt(Instant keyChangedAt) {
        this.keyChangedAt = keyChangedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean getActive() {
        return active;
    }

    public boolean getMobile() {
        return mobile;
    }

    public boolean getIos() {
        return ios;
    }

    public boolean getAndroid() {
        return android;
    }

    public boolean getDesktop() {
        return desktop;
    }

    public boolean isIos() {
        return ios;
    }

    public void setIos(boolean ios) {
        this.ios = ios;
    }

    public boolean isAndroid() {
        return android;
    }

    public void setAndroid(boolean android) {
        this.android = android;
    }

    public GameInfo getGameInfo() {
        return gameInfo;
    }

    public void setGameInfo(GameInfo gameInfo) {
        this.gameInfo = gameInfo;
    }

    public Set<GameCategory> getCategories() {
        return categories;
    }

    public void setCategories(Set<GameCategory> categories) {
        this.categories = categories;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String game_id) {
        gameId = game_id;
    }

    public boolean isMobile() {
        return mobile;
    }

    public void setMobile(boolean mobile) {
        this.mobile = mobile;
    }

    public boolean isDesktop() {
        return desktop;
    }

    public void setDesktop(boolean desktop) {
        this.desktop = desktop;
    }

    public BigDecimal getRtp() {
        return rtp;
    }

    public void setRtp(BigDecimal rtp) {
        this.rtp = rtp;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Vendor getVendor() {
        return vendor;
    }

    public void setVendor(Vendor vendor) {
        this.vendor = vendor;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    @Override
    public int compareTo(Game o) {
        return (int) (this.id - o.id);
    }

}
