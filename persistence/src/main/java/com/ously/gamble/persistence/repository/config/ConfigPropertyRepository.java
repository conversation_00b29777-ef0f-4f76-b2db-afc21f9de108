package com.ously.gamble.persistence.repository.config;

import com.ously.gamble.persistence.dto.ConfigPropertyDto;
import com.ously.gamble.persistence.model.config.ConfigProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConfigPropertyRepository extends JpaRepository<ConfigProperty, String> {

    @Query(nativeQuery = true)
    List<ConfigPropertyDto> getAllProperties();

    @Query(nativeQuery = true)
    Optional<ConfigPropertyDto> getProperty(String key);

}