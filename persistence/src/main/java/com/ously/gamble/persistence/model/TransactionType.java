package com.ously.gamble.persistence.model;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum TransactionType {
    BET((byte)0),
    DEPOSIT((byte)1),
    WIN((byte)2),
    DIRECTWIN((byte)3),

    ROLLBACK((byte)7),
    BONUS((byte)10),
    PURCHASE((byte)12),

    IGNORE((byte)19),

    BOOSTER((byte)30),
    DBLUP((byte)31),
    ACHIEVEMENT((byte)32),

    /**
     * bonus money converted to real money
     */
    // TODO sc-12454 obsolete. Delete
    ROLLOVER((byte)11),
    /**
     * a tombstone to avoid booking that tx
     */
    TOMBSTONE((byte)8);



    private final byte shortCode;

    private static final Map<Byte,TransactionType> mMap = initializeMapping();

    private static Map<Byte, TransactionType> initializeMapping() {
        return Arrays.stream(TransactionType.values()).collect(Collectors.toUnmodifiableMap(TransactionType::shortCode, Function.identity()));
    }

    TransactionType(byte shortCode){
        this.shortCode=shortCode;
    }

    public byte shortCode(){
        return shortCode;
    }

    public static TransactionType fromShortCode(byte code){
        return mMap.get(code);
    }

}
