package com.ously.gamble.persistence.repository.session;

import com.ously.gamble.persistence.model.session.SessionExternedData;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
@ConditionalOnProperty(prefix = "session.archival", name = "enabled", havingValue = "true")
public interface SessionExternedDataRepository extends JpaRepository<SessionExternedData, Long> {
}