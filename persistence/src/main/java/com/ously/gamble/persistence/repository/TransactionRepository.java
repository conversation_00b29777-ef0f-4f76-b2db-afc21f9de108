package com.ously.gamble.persistence.repository;

import com.ously.gamble.persistence.model.Transaction;
import com.ously.gamble.persistence.projections.SessionStats;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    Optional<Transaction> findByVendorAndExternalOrigTxId(String vendor, String externalOrigTxId);

    Optional<Transaction> findByUserIdAndVendorAndExternalOrigTxId(Long userId, String vendor, String externalOrigTxId);

    @Query("SELECT t FROM Transaction t WHERE t.userId = (:id) and t.createdAt >= :from and t.createdAt <= :until")
    Page<Transaction> findAllByUserIdFromUntil(@Param("id") Long id, @Param("from") Instant from, @Param("until") Instant until,
                                               Pageable pageable);


//    /**
//     * Session stat queries
//     */
//    @Query(nativeQuery = true, value = "select type,DATE_FORMAT(max(created_at), '%Y-%m-%dT%TZ') as maxDate," +
//            "sum(bet) as sumBet, sum(win) as sumWin,0 as sumBbet, 0 as sumBwin," +
//            " sum(boost) as sumBoost, max(bet) as maxBet, max(win) as maxWin , " +
//            "sum(earned_xp) as earnedXp, count(DISTINCT if (bet>0,round_ref,null)) as numPlays,count(DISTINCT IF(win >0, round_ref,null)) as numWins from transactions t " +
//            "where user_id = :uid and session_id = :sid and created_at >= :startDate group by type")
//    List<SessionStats> getSessionStatistics(@Param("uid") Long userId, @Param("sid") Long sessionId,
//                                            @Param("startDate") Instant startDate);

    @Query(nativeQuery = true,
            value = "select type,DATE_FORMAT(max(created_at), '%Y-%m-%dT%TZ') as maxDate,sum(bet) as sumBet, " +
                    "sum(win) as sumWin, sum(boost) as sumBoost, max(bet) as maxBet, max(win) as maxWin , " +
                    "sum(earned_xp) as earnedXp, count(DISTINCT if (bet>0,round_ref,null)) as numPlays,count(DISTINCT IF(win >0, round_ref,null)) as numWins from ctransactions t " +
                    "where user_id = :uid and session_id = :sid and created_at >= :startDate and created_at < DATE_ADD(:startDate, INTERVAL 1 DAY) group by type")
    List<SessionStats> getSessionStatisticsHistorical(@Param("uid") Long userId, @Param("sid") Long sessionId,
                                                      @Param("startDate") Instant startDate);


//    List<Transaction> findAllBySessionIdAndTypeOrderByCreatedAtAsc(Long sessionId, TransactionType bonus);

    @Query(nativeQuery = true,
            value = "select *  from transactions t where t.vendor_name = :vendorName and user_id = :uId and round_ref = :roundId")
    List<Transaction> findAllByRoundRefAndVendorNameAndUserId(@Param("roundId") String roundId,
                                                              @Param("vendorName") String vendorName, @Param("uId") Long userId);

    @Query(nativeQuery = true, value = "select *  from transactions t where t.vendor_name = :vendorName and round_ref = :roundId")
    List<Transaction> findAllByRoundRefAndVendorName(@Param("roundId") String roundId, @Param("vendorName") String vendorName);


//    List<Transaction> findAllByVendor(String vendor);


    @Query(nativeQuery = true,
            value = "select * from transactions t where t.user_id = :userId and t.session_id = :sessionId UNION ALL " +
                    "select * from ctransactions t2 where t2.session_id = :sessionId and t2.created_at >= :time1 and t2.created_at <= :time2 ",
            countQuery = "select count(*) from (select t.id from transactions t where t.user_id = :userId and t.session_id = :sessionId  union all select ct.id from ctransactions ct " +
                    "where ct.session_id= :sessionId  and ct.created_at > :time1 and ct.created_at < :time2 ) ua")
    Page<Transaction> findAllTransactionsForSessionPaged(@Param("userId") Long userId, @Param("sessionId") Long sessionId,
                                                         @Param("time1") Instant from, @Param("time2") Instant to,
                                                         Pageable pageable);


}