package com.ously.gamble.persistence.db.routingds;

import java.util.Objects;

public final class DbContextHolder {

    private static final ThreadLocal<DbType> contextHolder = new ThreadLocal<>();

    private DbContextHolder() {
    }

    public static void setDbType(DbType dbType) {
        Objects.requireNonNull(dbType);
        contextHolder.set(dbType);
    }

    public static DbType getDbType() {
        return contextHolder.get();
    }

    public static void clearDbType() {
        contextHolder.remove();
    }
}