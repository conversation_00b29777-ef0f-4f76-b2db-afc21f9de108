package com.ously.gamble.persistence.model.idclasses;

import java.io.Serializable;

public class SessionId implements Serializable {

    Long userId;
    Long sessionId;

    public SessionId() {
    }

    public SessionId(Long userId, Long sessionId) {
        this.userId = userId;
        this.sessionId = sessionId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (SessionId) o;

        return userId.equals(that.userId) && sessionId.equals(that.sessionId);
    }

    @Override
    public int hashCode() {
        var result = userId.hashCode();
        result = 31 * result + sessionId.hashCode();
        return result;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }
}
