insert IGNORE into sec_permission (permission_id, description)
values ('MANAGE_AFFILIATE', 'add,modify and change affiliates');

insert IGNOR<PERSON> into sec_permission (permission_id, description)
values ('MANAGE_CAMPAIGN', 'add,modify and change campaigns and campaign templates');

insert IGNORE into sec_permission (permission_id, description)
values ('AFFILIATE_LIST_CAMPAIGNS', 'Affiliate listing its own campaigns');

insert IGNORE into sec_permission (permission_id, description)
values ('AFFILIATE_CREATE_CAMPAIGNS', 'Affiliate creating campaign from template');

insert IGNOR<PERSON> into sec_role (role_id, description)
values ('CAMPAIGN_MANAGER', 'Manage campaigns and templates');

insert IGNORE into sec_role (role_id, description)
values ('AFFILIATE', 'A user marked as affiliate');

insert IGNORE into sec_roles_permissions (role_id, permission_id)
values ('CAMPAIGN_MANAGER', 'MANAGE_AFFILIATE');

insert IGNOR<PERSON> into sec_roles_permissions (role_id, permission_id)
values ('CAMPAIGN_MANAGER', 'MANAGE_CAMPAIGN');

insert IGNOR<PERSON> into sec_roles_permissions (role_id, permission_id)
values ('AFFILIATE', 'AFFILIATE_LIST_CAMPAIGNS');

insert IGNORE into sec_roles_permissions (role_id, permission_id)
values ('AFFILIATE', 'AFFILIATE_CREATE_CAMPAIGNS');

