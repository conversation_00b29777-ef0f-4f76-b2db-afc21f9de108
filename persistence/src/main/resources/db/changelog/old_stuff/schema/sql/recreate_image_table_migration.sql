CREATE TABLE `game_image2`
(
    `game_id`    bigint                                                       NOT NULL,
    `type`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `created_at` datetime                                                     DEFAULT current_timestamp,
    `updated_at` datetime                                                     DEFAULT current_timestamp,
    `image`      mediumblob                                                   NOT NULL,
    `real_type`  varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `mimetype`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`game_id`, `type`),
    FOREIGN KEY (game_id) references games (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert IGNORE into game_image2
select game_id, image_type as type, created_at, updated_at, image, real_type, mimetype
from game_image
where game_id in (select id as game_id from games);

rename table game_image to game_image_tmp, game_image2 to game_image;

drop table game_image_tmp;