databaseChangeLog:
  - changeSet:
      id: 10100000-04
      author: jkle<PERSON>
      changes:
        - createTable:
            tableName: users2
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: email
                  type: VARCHAR(120)
              - column:
                  name: mobile
                  type: VARCHAR(100)
              - column:
                  name: username
                  type: VARCHA<PERSON>(128)
              - column:
                  name: birthdate
                  type: date
              - column:
                  name: status
                  type: VARC<PERSON><PERSON>(30)
              - column:
                  name: merged_anon
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: anonymous
                  type: BIT(1)
              - column:
                  name: affiliate_id
                  type: BIGINT
              - column:
                  name: linked_date
                  type: datetime
              - column:
                  name: local_id
                  type: VARCHAR(150)
              - column:
                  name: admin
                  type: BIT(1)
              - column:
                  name: display_name
                  type: VARCHAR(128)
              - column:
                  name: email_verified
                  type: BIT(1)
              - column:
                  name: signin_provider
                  type: VARCHAR(70)
              - column:
                  name: crm_update
                  type: datetime

#      modifySql:
#        - append:
#            dbms: mysql
#            value: ENGINE=InnoDB PARTITION BY HASH(id) PARTITIONS 20

  - changeSet:
      id: 10100000-05
      author: jkleemann
      changes:
        - sql:
            sql: "insert into users2 select * from users"

  - changeSet:
      id: 10100000-06
      author: jkleemann
      changes:
        - dropTable:
            tableName: users

  - changeSet:
      id: 10100000-07
      author: jkleemann
      changes:
        - renameTable:
            oldTableName: users2
            newTableName: users

  - changeSet:
      id: 10100000-08
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: local_id
            indexName: UK1dotkott2kjsp8vw4d0m25fb7
            tableName: users
            unique: true
        - createIndex:
            columns:
              - column:
                  name: email
            indexName: UK6dotkott2kjsp8vw4d0m25fb7
            tableName: users
            unique: true