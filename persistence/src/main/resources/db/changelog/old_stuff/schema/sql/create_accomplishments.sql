CREATE TABLE `accomplishments` (
                            `user_id` bigint NOT NULL,
                            `type` VARCHAR(60) NOT NULL,
                            `selector` VARCHAR(100) NOT NULL,
                            `ref` bigint NULL,
                            `rank` bigint default 0,
                            `variables` VARCHAR(200) DEFAULT '',
                            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            `achievement_id` BIGINT NULL,
                            PRIMARY KEY (`user_id`,`type`,`selector`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci PARTITION BY HASH (user_id)
    PARTITIONS 20;


