
CREATE TABLE `tokens2` (
                          `id` bigint(20) NOT NULL,
                          `boost` int(11) DEFAULT NULL,
                          `count` int(11) DEFAULT NULL,
                          `duration` int(11) DEFAULT NULL,
                          `secret` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                          `type` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL,
                          `updated_at` datetime DEFAULT NULL,
                          `user_id` bigint(20) NOT NULL,
                          `version` bigint(20) DEFAULT 1,
                          PRIMARY KEY (`user_id`,`type`,`id`)
                      ) ENGINE=InnoDB AUTO_INCREMENT=763 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci PARTITION BY HASH (user_id)
    PARTITIONS 20;

insert into tokens2 select * from tokens;

RENAME TABLE tokens to tokens_tmp, tokens2 to tokens;

DROP TABLE tokens_tmp;
