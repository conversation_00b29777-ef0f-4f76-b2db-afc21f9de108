CREATE TABLE `popup_definitions2`
(
    `id`              bigint                                  NOT NULL AUTO_INCREMENT,
    `created_at`      TIMESTAMP                                        DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      TIMESTAMP                               NULL,
    `title`           varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
    `body`            varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL,
    `languages`       varchar(64) COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `type`            varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL,
    `assets`          varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `campaign_ref`    varchar(64) COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `var_default`     varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT '',
    `var_definitions` varchar(256) COLLATE utf8mb4_unicode_ci          DEFAULT '',
    `ingame_allowed`  bit(1)                                  NOT NULL DEFAULT b'0',
    `buttons`         varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'OK',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into popup_definitions2 (id, created_at, updated_at, title, body, languages, type, assets, campaign_ref,
                                var_default, var_definitions, ingame_allowed, buttons)
select id,
       created_at,
       updated_at,
       title,
       body,
       languages,
       type,
       background_asset as assets,
       campaign_ref,
       var_default,
       var_definitions,
       ingame_allowed,
       buttons
from popup_definitions;
RENAME TABLE popup_definitions to popup_definitions_tmp, popup_definitions2 to popup_definitions;

DROP TABLE popup_definitions_tmp;