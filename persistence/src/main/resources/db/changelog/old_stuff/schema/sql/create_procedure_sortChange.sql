create
    procedure REORDER(IN newOrder int, IN gameId bigint)
BEGIN
    DECLARE it_order INT;
    DECLARE done INT DEFAULT FALSE;
    DECLARE c_id BIGINT;
    DECLARE c_order INT;

    DECLARE gamesCursor CURSOR FOR SELECT id, games.sort_order FROM games where games.sort_order >= newOrder order by games.sort_order asc;


    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            SELECT 'An error has occurred, operation rollbacked and the stored procedure was terminated';
        END;

    SET it_order = newOrder + 1;

    OPEN gamesCursor;

    WHILE NOT done
        do
            FETCH gamesCursor INTO c_id,c_order;
            IF c_order >= it_order then
                set done = TRUE;
            end if;

            if NOT DONE THEN
                update games set sort_order = it_order where id = c_id;
                set it_order = it_order + 1;
            END IF;

        END WHILE;
    update games set sort_order = newOrder where id = gameId;
    close gamesCursor;
END;

