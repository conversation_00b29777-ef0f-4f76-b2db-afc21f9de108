databaseChangeLog:
  - changeSet:
      id: 1606383959732-1
      author: j<PERSON><PERSON> (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: appstore_def
                  type: VARCHAR(100)
              - column:
                  defaultValueBoolean: false
                  name: claimed
                  type: BIT(1)
              - column:
                  name: description
                  type: VARCHAR(255)
              - column:
                  name: price_def
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  name: qualifier
                  type: VARCHAR(100)
              - column:
                  name: secret
                  type: VARCHAR(50)
              - column:
                  name: title
                  type: VA<PERSON><PERSON><PERSON>(255)
              - column:
                  constraints:
                    nullable: false
                  name: type
                  type: VARCHAR(60)
              - column:
                  constraints:
                    nullable: false
                  name: user_id
                  type: BIGINT
              - column:
                  defaultValueNumeric: 1
                  name: version
                  type: BIGINT
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  name: variables
                  type: VARCHAR(200)
              - column:
                  name: asset_id
                  type: BIGINT
            tableName: achievements
  - changeSet:
      id: 1606383959732-3
      author: j<PERSON><PERSON> (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: expires_at
                  type: datetime
              - column:
                  constraints:
                    unique: true
                  name: hash
                  type: VARCHAR(255)
              - column:
                  name: type
                  type: VARCHAR(60)
              - column:
                  name: user_id
                  type: BIGINT
            tableName: activations
  - changeSet:
      id: 1606383959732-4
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: active
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: created_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: game_url
                  type: VARCHAR(600)
              - column:
                  constraints:
                    nullable: false
                    unique: true
                  name: game_token
                  type: VARCHAR(80)
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: auth_token
                  type: VARCHAR(80)
              - column:
                  name: expires_at
                  type: datetime
              - column:
                  name: game_html
                  type: VARCHAR(15000)
              - column:
                  name: start_level
                  type: BIGINT
              - column:
                  name: start_percnl
                  type: DECIMAL(19, 2)
            tableName: activegames
  - changeSet:
      id: 1606383959732-5
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: city
                  type: VARCHAR(100)
              - column:
                  name: country
                  type: VARCHAR(100)
              - column:
                  name: email
                  type: VARCHAR(120)
              - column:
                  name: mobile
                  type: VARCHAR(100)
              - column:
                  name: name
                  type: VARCHAR(180)
              - column:
                  name: secretCode
                  type: VARCHAR(100)
              - column:
                  constraints:
                    nullable: false
                  name: status
                  type: VARCHAR(30)
              - column:
                  name: street
                  type: VARCHAR(100)
              - column:
                  name: zipcode
                  type: VARCHAR(10)
            tableName: affiliates
  - changeSet:
      id: 1606383959732-6
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: image
                  type: MEDIUMBLOB
              - column:
                  name: mimetype
                  type: VARCHAR(30)
              - column:
                  name: name
                  type: VARCHAR(128)
              - column:
                  name: real_type
                  type: VARCHAR(14)
              - column:
                  name: asset_type
                  type: VARCHAR(30)
            tableName: asset
  - changeSet:
      id: 1606383959732-7
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: active
                  type: BIT(1)
              - column:
                  name: aff_id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: bonuscode
                  type: VARCHAR(150)
              - column:
                  name: maxusage
                  type: INT
              - column:
                  name: pricedef
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  name: type
                  type: VARCHAR(30)
              - column:
                  name: usedcount
                  type: INT
              - column:
                  name: valid_from
                  type: datetime
              - column:
                  name: valid_until
                  type: datetime
              - column:
                  name: version
                  type: BIGINT
              - column:
                  name: user_id
                  type: BIGINT
            tableName: bonuscodes
  - changeSet:
      id: 1606383959732-8
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: price
                  type: DECIMAL(19, 2)
              - column:
                  name: description
                  type: VARCHAR(256)
              - column:
                  name: item_category
                  type: VARCHAR(255)
              - column:
                  name: item_def
                  type: VARCHAR(255)
              - column:
                  name: product_id
                  type: VARCHAR(128)
              - column:
                  name: title
                  type: VARCHAR(128)
              - column:
                  name: valid_from
                  type: datetime
              - column:
                  name: valid_to
                  type: datetime
              - column:
                  name: version
                  type: BIGINT
              - column:
                  name: asset_id
                  type: BIGINT
            tableName: consumables
  - changeSet:
      id: 1606383959732-9
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: balance_after
                  type: DECIMAL(19, 2)
              - column:
                  name: bet
                  type: DECIMAL(19, 2)
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: created_at
                  type: timestamp
              - column:
                  constraints:
                    nullable: false
                  name: ext_tx_numid
                  type: VARCHAR(180)
              - column:
                  name: ext_tx_id
                  type: VARCHAR(180)
              - column:
                  name: type
                  type: VARCHAR(20)
              - column:
                  name: vendor_name
                  type: VARCHAR(100)
              - column:
                  name: win
                  type: DECIMAL(19, 2)
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: user_id
                  type: BIGINT
              - column:
                  name: earned_xp
                  type: BIGINT
              - column:
                  name: level_after
                  type: BIGINT
              - column:
                  name: percnl_after
                  type: DECIMAL(19, 2)
              - column:
                  name: round_ref
                  type: VARCHAR(128)
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  defaultValueBoolean: false
                  name: cancelled
                  type: BIT(1)
              - column:
                  defaultValueComputed: '0.00'
                  name: boost
                  type: DECIMAL(19, 2)
            tableName: ctransactions
  - changeSet:
      id: 1606383959732-10
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: sequence_name
                  type: VARCHAR(255)
              - column:
                  name: sequence_next_hi_value
                  type: BIGINT
            tableName: custom_sequences
  - changeSet:
      id: 1606383959732-11
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: ingame_amount
                  type: DECIMAL(19, 2)
              - column:
                  constraints:
                    nullable: false
                  name: pay_currency
                  type: VARCHAR(3)
              - column:
                  name: pay_description
                  type: VARCHAR(400)
              - column:
                  constraints:
                    nullable: false
                  name: pay_amount
                  type: DECIMAL(19, 2)
              - column:
                  name: pay_details
                  type: VARCHAR(200)
              - column:
                  name: pay_method
                  type: VARCHAR(100)
              - column:
                  name: timestamp
                  type: datetime
              - column:
                  name: wallet_id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
            tableName: deposits
  - changeSet:
      id: 1606383959732-12
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: active
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: chance_factor
                  type: DECIMAL(19, 2)
              - column:
                  constraints:
                    nullable: false
                  name: level
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name
                  type: VARCHAR(128)
              - column:
                  constraints:
                    nullable: false
                  name: win_factor
                  type: DECIMAL(19, 2)
              - column:
                  name: card_url
                  type: VARCHAR(255)
            tableName: doubleup_foes
  - changeSet:
      id: 1606383959732-13
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: finished_at
                  type: datetime
              - column:
                  name: last_round
                  type: INT
              - column:
                  name: maxbet
                  type: DECIMAL(19, 2)
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  name: status
                  type: INT
              - column:
                  name: sum_bet
                  type: DECIMAL(19, 2)
              - column:
                  name: sum_win
                  type: DECIMAL(19, 2)
              - column:
                  name: user_id
                  type: BIGINT
            tableName: doubleup_statistics
  - changeSet:
      id: 1606383959732-14
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: active
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: chance_factor
                  type: DECIMAL(19, 2)
              - column:
                  constraints:
                    nullable: false
                  name: freeforall
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: name
                  type: VARCHAR(128)
              - column:
                  constraints:
                    nullable: false
                  name: win_factor
                  type: DECIMAL(19, 2)
              - column:
                  name: card_url
                  type: VARCHAR(255)
            tableName: doubleup_weapons
  - changeSet:
      id: 1606383959732-15
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: active
                  type: BIT(1)
              - column:
                  name: active_round
                  type: INT
              - column:
                  name: current_maxbet
                  type: DECIMAL(19, 2)
              - column:
                  name: expires
                  type: datetime
              - column:
                  name: secret
                  type: VARCHAR(40)
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  name: sum_bet
                  type: DECIMAL(19, 2)
              - column:
                  name: sum_win
                  type: DECIMAL(19, 2)
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  name: winvector
                  type: INT
            tableName: doubleups
  - changeSet:
      id: 1606383959732-17
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: name
                  type: VARCHAR(200)
              - column:
                  defaultValue: ''
                  name: type
                  type: VARCHAR(50)
              - column:
                  defaultValue: ''
                  name: parents
                  type: VARCHAR(300)
              - column:
                  name: hidden
                  type: BIT(1)
            tableName: game_category
  - changeSet:
      id: 1606383959732-18
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: game_id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: category_id
                  type: BIGINT
            tableName: game_category_link
  - changeSet:
      id: 1606383959732-19
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  name: image_type
                  type: VARCHAR(30)
              - column:
                  name: image
                  type: MEDIUMBLOB
              - column:
                  name: real_type
                  type: VARCHAR(14)
              - column:
                  name: mimetype
                  type: VARCHAR(30)
            tableName: game_image
  - changeSet:
      id: 1606383959732-20
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: Id
                  type: BIGINT
              - column:
                  name: data
                  type: VARCHAR(4096)
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  name: game_key
                  type: VARCHAR(255)
              - column:
                  name: game_name
                  type: VARCHAR(255)
              - column:
                  name: slot_rank
                  type: BIGINT
              - column:
                  name: vendor
                  type: VARCHAR(255)
            tableName: game_info
  - changeSet:
      id: 1606383959732-21
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: game_id
                  type: BIGINT
              - column:
                  name: importance
                  type: DOUBLE
              - column:
                  name: type
                  type: VARCHAR(255)
              - column:
                  name: valid_from
                  type: date
              - column:
                  name: valid_to
                  type: date
              - column:
                  name: feature_text
                  type: VARCHAR(255)
            tableName: game_promotion
  - changeSet:
      id: 1606383959732-22
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: end_at
                  type: datetime
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  name: max_bet
                  type: DECIMAL(19, 2)
              - column:
                  name: max_multiplier
                  type: DECIMAL(19, 2)
              - column:
                  name: max_win
                  type: DECIMAL(19, 2)
              - column:
                  name: num_plays
                  type: BIGINT
              - column:
                  name: start_at
                  type: datetime
              - column:
                  name: sum_bet
                  type: DECIMAL(19, 2)
              - column:
                  name: sum_bonus
                  type: DECIMAL(19, 2)
              - column:
                  name: sum_win
                  type: DECIMAL(19, 2)
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  defaultValueComputed: '0.00'
                  name: boost
                  type: DECIMAL(19, 2)
            tableName: game_statistics
  - changeSet:
      id: 1606383959732-23
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: active
                  type: BIT(1)
              - column:
                  name: desktop
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: game_id
                  type: VARCHAR(128)
              - column:
                  name: mobile
                  type: BIT(1)
              - column:
                  name: name
                  type: VARCHAR(100)
              - column:
                  name: title
                  type: VARCHAR(100)
              - column:
                  constraints:
                    nullable: false
                  name: provider_name
                  type: VARCHAR(128)
              - column:
                  defaultValueNumeric: 0
                  name: level
                  type: INT
              - column:
                  name: info_id
                  type: BIGINT
              - column:
                  defaultValueBoolean: true
                  name: ios
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
                  name: android
                  type: BIT(1)
            tableName: games
  - changeSet:
      id: 1606383959732-24
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: comment
                  type: VARCHAR(2048)
              - column:
                  name: commenter_id
                  type: BIGINT
              - column:
                  name: issue_id
                  type: BIGINT
              - column:
                  name: attachment_contenttype
                  type: VARCHAR(255)
              - column:
                  name: attachment_name
                  type: VARCHAR(255)
              - column:
                  name: attachment
                  type: MEDIUMBLOB
            tableName: issue_comments
  - changeSet:
      id: 1606383959732-25
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: issue_head
                  type: VARCHAR(256)
              - column:
                  name: issue_text
                  type: VARCHAR(2048)
              - column:
                  constraints:
                    nullable: false
                  name: status
                  type: VARCHAR(30)
              - column:
                  name: issue_topic
                  type: VARCHAR(256)
              - column:
                  name: assignee_id
                  type: BIGINT
              - column:
                  name: customer_id
                  type: BIGINT
            tableName: issues
  - changeSet:
      id: 1606383959732-26
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: langcode
                  type: VARCHAR(15)
              - column:
                  name: literal_id
                  type: VARCHAR(100)
              - column:
                  name: template
                  type: VARCHAR(1024)
              - column:
                  name: version
                  type: BIGINT
            tableName: localisations
  - changeSet:
      id: 1606383959732-27
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: destination
                  type: VARCHAR(200)
              - column:
                  name: msg
                  type: VARCHAR(100)
              - column:
                  name: ok
                  type: BIT(1)
              - column:
                  name: receipt
                  type: VARCHAR(256)
              - column:
                  name: requested_at
                  type: datetime
              - column:
                  name: send_at
                  type: datetime
              - column:
                  name: subject
                  type: VARCHAR(50)
              - column:
                  name: type
                  type: VARCHAR(255)
              - column:
                  name: user_id
                  type: BIGINT
            tableName: notifications
  - changeSet:
      id: 1606383959732-28
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: abort_reason
                  type: VARCHAR(512)
              - column:
                  name: notification
                  type: VARCHAR(8192)
              - column:
                  name: notification_timestamp
                  type: datetime
              - column:
                  name: payment_id
                  type: VARCHAR(128)
              - column:
                  name: payment_link
                  type: VARCHAR(200)
              - column:
                  name: platform
                  type: VARCHAR(20)
              - column:
                  name: price_def
                  type: VARCHAR(200)
              - column:
                  name: price_value
                  type: DECIMAL(19, 2)
              - column:
                  name: status
                  type: VARCHAR(20)
              - column:
                  name: type
                  type: VARCHAR(20)
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  name: consumable_id
                  type: BIGINT
            tableName: payments
  - changeSet:
      id: 1606383959732-29
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: applied_items
                  type: VARCHAR(255)
              - column:
                  name: consumable_id
                  type: BIGINT
              - column:
                  name: applied_cost
                  type: DECIMAL(19, 2)
              - column:
                  name: platform
                  type: VARCHAR(255)
              - column:
                  name: receipt
                  type: VARCHAR(8192)
              - column:
                  constraints:
                    nullable: false
                  name: transaction_id
                  type: VARCHAR(255)
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  name: affiliate_id
                  type: BIGINT
            tableName: purchases
  - changeSet:
      id: 1606383959732-30
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: boost
                  type: INT
              - column:
                  name: count
                  type: INT
              - column:
                  name: duration
                  type: INT
              - column:
                  name: secret
                  type: VARCHAR(255)
              - column:
                  name: type
                  type: VARCHAR(60)
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: user_id
                  type: BIGINT
              - column:
                  name: version
                  type: BIGINT
            tableName: tokens
  - changeSet:
      id: 1606383959732-31
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: balance_after
                  type: DECIMAL(19, 2)
              - column:
                  name: bet
                  type: DECIMAL(19, 2)
              - column:
                  name: created_at
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: ext_tx_numid
                  type: VARCHAR(180)
              - column:
                  name: ext_tx_id
                  type: VARCHAR(180)
              - column:
                  name: type
                  type: VARCHAR(20)
              - column:
                  name: vendor_name
                  type: VARCHAR(100)
              - column:
                  name: win
                  type: DECIMAL(19, 2)
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id
                  type: BIGINT
              - column:
                  name: earned_xp
                  type: BIGINT
              - column:
                  name: level_after
                  type: BIGINT
              - column:
                  name: percnl_after
                  type: DECIMAL(19, 2)
              - column:
                  name: round_ref
                  type: VARCHAR(128)
              - column:
                  name: session_id
                  type: BIGINT
              - column:
                  defaultValueBoolean: false
                  name: cancelled
                  type: BIT(1)
              - column:
                  defaultValueComputed: '0.00'
                  name: boost
                  type: DECIMAL(19, 2)
            tableName: transactions
  - changeSet:
      id: 1606383959732-32
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: country
                  type: VARCHAR(2)
              - column:
                  name: event_type
                  type: VARCHAR(20)
              - column:
                  name: ipadress
                  type: VARCHAR(60)
              - column:
                  name: platform
                  type: VARCHAR(20)
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id
                  type: BIGINT
              - column:
                  name: devicename
                  type: VARCHAR(200)
              - column:
                  name: fcmtoken
                  type: VARCHAR(200)
              - column:
                  name: osversion
                  type: VARCHAR(200)
              - column:
                  name: logindata
                  type: VARCHAR(2048)
              - column:
                  name: deviceid
                  type: VARCHAR(200)
            tableName: user_events
  - changeSet:
      id: 1606383959732-33
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: favorites
                  type: VARCHAR(1200)
              - column:
                  name: playlist
                  type: VARCHAR(1200)
              - column:
                  name: updated_at
                  type: datetime
            tableName: user_game_attributes
  - changeSet:
      id: 1606383959732-34
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id
                  type: BIGINT
              - column:
                  name: agb
                  type: BIT(1)
              - column:
                  name: birthdate
                  type: date
              - column:
                  name: crm_update
                  type: datetime
              - column:
                  name: directContact
                  type: BIT(1)
              - column:
                  name: gender
                  type: VARCHAR(10)
              - column:
                  name: mobile
                  type: VARCHAR(100)
              - column:
                  name: name
                  type: VARCHAR(100)
              - column:
                  name: newsletter
                  type: BIT(1)
              - column:
                  name: surname
                  type: VARCHAR(100)
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  defaultValue: EN
                  name: pref_lang
                  type: VARCHAR(4)
              - column:
                  defaultValueBoolean: true
                  name: slot_sound
                  type: BIT(1)
              - column:
                  defaultValueBoolean: true
                  name: game_sound
                  type: BIT(1)
              - column:
                  name: reg_bonus
                  type: datetime
            tableName: user_info
  - changeSet:
      id: 1606383959732-35
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: updated_at
                  type: datetime
              - column:
                  name: email
                  type: VARCHAR(120)
              - column:
                  name: mobile
                  type: VARCHAR(100)
              - column:
                  name: username
                  type: VARCHAR(128)
              - column:
                  name: birthdate
                  type: date
              - column:
                  name: status
                  type: VARCHAR(30)
              - column:
                  name: merged_anon
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: anonymous
                  type: BIT(1)
              - column:
                  name: affiliate_id
                  type: BIGINT
              - column:
                  name: linked_date
                  type: datetime
              - column:
                  name: local_id
                  type: VARCHAR(150)
              - column:
                  name: admin
                  type: BIT(1)
              - column:
                  name: display_name
                  type: VARCHAR(128)
              - column:
                  name: email_verified
                  type: BIT(1)
              - column:
                  name: signin_provider
                  type: VARCHAR(70)
              - column:
                  name: crm_update
                  type: datetime
            tableName: users
  - changeSet:
      id: 1606383959732-36
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  name: balance
                  type: DECIMAL(19, 2)
              - column:
                  name: num_played
                  type: BIGINT
              - column:
                  name: num_won
                  type: BIGINT
              - column:
                  name: level
                  type: BIGINT
              - column:
                  name: mbp
                  type: BIGINT
              - column:
                  name: percnl
                  type: DECIMAL(3, 2)
              - column:
                  name: sum_bet
                  type: DECIMAL(19, 2)
              - column:
                  name: sum_win
                  type: DECIMAL(19, 2)
              - column:
                  name: version
                  type: BIGINT
              - column:
                  name: xp
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id
                  type: BIGINT
              - column:
                  name: active_perks
                  type: VARCHAR(200)
              - column:
                  name: saveup
                  type: DECIMAL(10, 2)
              - column:
                  name: spintimer
                  type: datetime
            tableName: wallets
  - changeSet:
      id: 1606383959732-37
      author: jkleemann (generated)
      changes:
        - createTable:
            columns:
              - column:
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: datetime
              - column:
                  name: claimed
                  type: BIT(1)
              - column:
                  name: price_def
                  type: VARCHAR(255)
              - column:
                  name: secret
                  type: VARCHAR(50)
              - column:
                  name: super_spin
                  type: BIT(1)
              - column:
                  constraints:
                    nullable: false
                  name: user_id
                  type: BIGINT
              - column:
                  name: version
                  type: BIGINT
            tableName: wheelspins
  - changeSet:
      id: 1606383959732-38
      author: jkleemann (generated)
      changes:
        - addPrimaryKey:
            columnNames: user_id, type, qualifier, id
            constraintName: PRIMARY
            tableName: achievements
  - changeSet:
      id: 1606383959732-39
      author: jkleemann (generated)
      changes:
        - addUniqueConstraint:
            columnNames: user_id, type, duration, boost
            constraintName: UK1lgdp8dqyenins71bali7tjlu
            tableName: tokens
  - changeSet:
      id: 1606383959732-40
      author: jkleemann (generated)
      changes:
        - addUniqueConstraint:
            columnNames: user_id, vendor_name, ext_tx_numid
            constraintName: transaction_main_idx
            tableName: transactions
  - changeSet:
      id: 1606383959732-41
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id
            indexName: FK1apdjcmlyi3uuyc1fles686r7
            tableName: activegames
  - changeSet:
      id: 1606383959732-42
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: commenter_id
            indexName: FK1cwv1o145vwja1v6c3gjw28vk
            tableName: issue_comments
  - changeSet:
      id: 1606383959732-43
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: game_id
            indexName: FK1ren8qp57lp80p8be68w1oklf
            tableName: activegames
  - changeSet:
      id: 1606383959732-44
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: assignee_id
            indexName: FK6tkde1c2odhrtreahor01p5fb
            tableName: issues
  - changeSet:
      id: 1606383959732-45
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: customer_id
            indexName: FKcf7q73nojgjq16kip3i9ilt1w
            tableName: issues
  - changeSet:
      id: 1606383959732-46
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: category_id
            indexName: FKh082qbpdnj6js2mns0hkkr6wa
            tableName: game_category_link
  - changeSet:
      id: 1606383959732-47
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: issue_id
            indexName: FKnvnj0204928o0w1th5jsx4f28
            tableName: issue_comments
  - changeSet:
      id: 1606383959732-48
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: info_id
            indexName: FKr4gg6xk6bpwbh4xrqqpexqx8e
            tableName: games
  - changeSet:
      id: 1606383959732-49
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: local_id
            indexName: UK1dotkott2kjsp8vw4d0m25fb7
            tableName: users
            unique: true
  - changeSet:
      id: 1606383959732-50
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: email
            indexName: UK6dotkott2kjsp8vw4d0m25fb7
            tableName: users
            unique: true
  - changeSet:
      id: 1606383959732-51
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: game_id
              - column:
                  name: image_type
            indexName: UK8ap6d90kttt157vbcetvqgtql
            tableName: game_image
            unique: true
  - changeSet:
      id: 1606383959732-53
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: transaction_id
            indexName: UK_q3yrrym1rlkuyjcxt10cevhou
            tableName: purchases
            unique: true
  - changeSet:
      id: 1606383959732-54
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: name
              - column:
                  name: asset_type
            indexName: UKb8bje59o0x1bmjw74fgrdg3ac
            tableName: asset
            unique: true
  - changeSet:
      id: 1606383959732-55
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: provider_name
              - column:
                  name: game_id
            indexName: UKfp5ppgbibxvwreixw7xwgn4k8
            tableName: games
            unique: true
  - changeSet:
      id: 1606383959732-56
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: bonuscode
            indexName: UKm45oi6cy5vyoglx1xkscsmt5i
            tableName: bonuscodes
            unique: true
  - changeSet:
      id: 1606383959732-57
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: start_at
            indexName: created_at_hidx
            tableName: game_statistics
  - changeSet:
      id: 1606383959732-58
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: wallet_id
            indexName: deposits___fkToWallets
            tableName: deposits

  - changeSet:
      id: 1606383959732-60
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: game_id
            indexName: game_id_hidx
            tableName: game_statistics
  - changeSet:
      id: 1606383959732-61
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: payment_id
            indexName: pmnts_payment_id_idx
            tableName: payments
  - changeSet:
      id: 1606383959732-62
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id
            indexName: pmnts_user_id_idx
            tableName: payments
  - changeSet:
      id: 1606383959732-63
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: session_id
            indexName: session_id_hidx
            tableName: game_statistics
  - changeSet:
      id: 1606383959732-64
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id
              - column:
                  name: vendor_name
              - column:
                  name: ext_tx_numid
            indexName: transaction_main_idx
            tableName: ctransactions
  - changeSet:
      id: 1606383959732-65
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: created_at
            indexName: transactions__index_created_at
            tableName: transactions
  - changeSet:
      id: 1606383959732-66
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: created_at
            indexName: transactions_index_created_at
            tableName: ctransactions
  - changeSet:
      id: 1606383959732-67
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: round_ref
            indexName: transactions_round_ref_index
            tableName: ctransactions
  - changeSet:
      id: 1606383959732-68
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: round_ref
            indexName: transactions_round_ref_index
            tableName: transactions
  - changeSet:
      id: 1606383959732-69
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: session_id
            indexName: transactions_session_id_index
            tableName: ctransactions
  - changeSet:
      id: 1606383959732-70
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: session_id
            indexName: transactions_session_id_index
            tableName: transactions
  - changeSet:
      id: 1606383959732-71
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: vendor_name
              - column:
                  name: ext_tx_numid
            indexName: transactions_vendor_name_ext_tx_numid_index
            tableName: transactions
  - changeSet:
      id: 1606383959732-72
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id
            indexName: user_id_hidx
            tableName: game_statistics
  - changeSet:
      id: 1606383959732-73
      author: jkleemann (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id
              - column:
                  name: event_type
              - column:
                  name: created_at
            indexName: userevent_type_idx
            tableName: user_events
  - changeSet:
      id: 1606383959732-74
      author: jkleemann (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: game_id
            baseTableName: game_category_link
            constraintName: FKfjmvp5nana2m0kq9v7vxvo0eo
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id
            referencedTableName: games
            validate: true
  - changeSet:
      id: 1606383959732-75
      author: jkleemann (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: category_id
            baseTableName: game_category_link
            constraintName: FKh082qbpdnj6js2mns0hkkr6wa
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id
            referencedTableName: game_category
            validate: true
  - changeSet:
      id: 1606383959732-76
      author: jkleemann (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: info_id
            baseTableName: games
            constraintName: FKr4gg6xk6bpwbh4xrqqpexqx8e
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: Id
            referencedTableName: game_info
            validate: true
  - changeSet:
      id: 1606383959732-77
      author: jkleemann (generated)
      changes:
        - createView:
            fullDefinition: false
            remarks: VIEW
            selectQuery: select `transactions`.`type` AS `type`,cast(`transactions`.`created_at`
              as date) AS `pfrom`,cast(`transactions`.`created_at` as date) AS
              `pto`,sum((case when (`transactions`.`type` = 'BET') then 1 else
              0 end)) AS `cntplays`,sum((case when (`transactions`.`win` >= 1)
              then 1 else 0 end)) AS `cntwins`,sum((case when (`transactions`.`type`
              = 'BET') then `transactions`.`bet` when (`transactions`.`type`
              = 'DIRECTWIN') then `transactions`.`bet` else 0 end)) AS `sumbet`,sum((case
              when (`transactions`.`type` = 'WIN') then `transactions`.`win`
              when (`transactions`.`type` = 'DIRECTWIN') then `transactions`.`win`
              else 0 end)) AS `sumwin` from `transactions` where ((`transactions`.`type`
              in ('WIN','BET','DIRECTWIN')) and (`transactions`.`created_at` is
              not null)) group by `transactions`.`type`,cast(`transactions`.`created_at`
              as date),cast(`transactions`.`created_at` as date)
            viewName: transaction_aggregation

