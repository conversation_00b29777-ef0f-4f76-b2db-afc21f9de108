CREATE TABLE `game_statistics2`
(
    `session_id`     bigint(20) NOT NULL,
    `user_id`        bigint(20) NOT NULL,
    `game_id`        bigint(20) NOT NULL,
    `start_at`       timestamp  NOT NULL                    DEFAULT CURRENT_TIMESTAMP,
    `end_at`         timestamp  NOT NULL                    DEFAULT CURRENT_TIMESTAMP,
    `max_bet`        decimal(19, 2)                         DEFAULT '0.00',
    `max_multiplier` decimal(19, 2)                         DEFAULT NULL,
    `max_win`        decimal(19, 2)                         DEFAULT '0.00',
    `num_plays`      bigint(20)                             DEFAULT '0',
    `num_wins`       bigint(20)                             DEFAULT '0',
    `sum_bet`        decimal(19, 2)                         DEFAULT '0.00',
    `sum_bonus`      decimal(19, 2)                         DEFAULT '0.00',
    `sum_win`        decimal(19, 2)                         DEFAULT '0.00',
    `boost`          decimal(19, 2)                         DEFAULT '0.00',
    `platform`       varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT 'ANDROID',
    `start_balance`  decimal(19, 2)                         DEFAULT '0.00',
    <PERSON><PERSON>AR<PERSON> KEY (`start_at`, `session_id`),
    KEY `idx_gs_session_id` (`session_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    PARTITION BY RANGE (unix_timestamp(`start_at`))
        (PARTITION p_first VALUES LESS THAN (1609459200) ENGINE = InnoDB,
        PARTITION p202101 VALUES LESS THAN (1612137600) ENGINE = InnoDB,
        PARTITION p202102 VALUES LESS THAN (1614556800) ENGINE = InnoDB,
        PARTITION p202103 VALUES LESS THAN (1617235200) ENGINE = InnoDB,
        PARTITION p202104 VALUES LESS THAN (1619827200) ENGINE = InnoDB,
        PARTITION p202105 VALUES LESS THAN (1622505600) ENGINE = InnoDB,
        PARTITION p202106 VALUES LESS THAN (1625097600) ENGINE = InnoDB,
        PARTITION p202107 VALUES LESS THAN (1627776000) ENGINE = InnoDB,
        PARTITION p202108 VALUES LESS THAN (1630454400) ENGINE = InnoDB,
        PARTITION p202109 VALUES LESS THAN (1633046400) ENGINE = InnoDB,
        PARTITION p202110 VALUES LESS THAN (1635724800) ENGINE = InnoDB,
        PARTITION p202111 VALUES LESS THAN (UNIX_TIMESTAMP('2021-12-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-01-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p_future VALUES LESS THAN MAXVALUE ENGINE = InnoDB);

insert into game_statistics2
select *
from game_statistics;

RENAME TABLE game_statistics to game_statistics_tmp, game_statistics2 to game_statistics;

DROP TABLE game_statistics_tmp;
