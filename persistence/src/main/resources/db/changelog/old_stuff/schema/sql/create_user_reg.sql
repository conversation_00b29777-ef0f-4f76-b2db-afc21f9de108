CREATE TABLE `user_registration`
(
    `local_id`       VARCHAR(128) COLLATE utf8mb4_unicode_ci NOT NULL,
    `user_id`        bigint(20)                              NOT NULL,
    `created_at`     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `status`         VARCHAR(30) COLLATE utf8mb4_unicode_ci  NOT NULL,
    `login_provider` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
    PRIMARY KEY (`local_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci PARTITION BY KEY (local_id)
    PARTITIONS 10;


CREATE TABLE `user_mail`
(
    `email`    VARCHAR(128) COLLATE utf8mb4_unicode_ci NOT NULL,
    `local_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci NOT NULL,
    `user_id`  bigint(20)                              NOT NULL,
    PRIMARY KEY (`email`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci PARTITION BY KEY (email)
    PARTITIONS 10;