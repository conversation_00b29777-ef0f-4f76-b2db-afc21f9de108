databaseChangeLog:
  - changeSet:
      id: 20211126-01
      author: j<PERSON><PERSON>
      changes:
        - addColumn:
            tableName: user_info
            columns:
              - column:
                  name: birthplace
                  type: VARCHAR(100)
                  constraints:
                    nullable: true
              - column:
                  name: nationality
                  type: VARCHAR(10)
                  constraints:
                    nullable: true
              - column:
                  name: adr_city
                  type: VARCHAR(100)
                  constraints:
                    nullable: true
              - column:
                  name: adr_street
                  type: VARCHAR(100)
                  constraints:
                    nullable: true
              - column:
                  name: adr_street_nr
                  type: VARCHAR(20)
                  constraints:
                    nullable: true
              - column:
                  name: adr_zipcode
                  type: VARCHAR(20)
                  constraints:
                    nullable: true
              - column:
                  name: adr_country
                  type: VARCHAR(10)
                  constraints:
                    nullable: true
              - column:
                  name: kyc_timestamp
                  type: TIMESTAMP
                  constraints:
                    nullable: true
              - column:
                  name: kyc_status
                  type: VARCHAR(25)
                  constraints:
                    nullable: true
              - column:
                  name: kyc_id
                  type: VARCHAR(40)
                  constraints:
                    nullable: true
