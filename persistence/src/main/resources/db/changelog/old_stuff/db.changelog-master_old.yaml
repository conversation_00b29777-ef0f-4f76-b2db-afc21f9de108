databaseChangeLog:
  - includeAll:
      path: db/changelog/kyc/*.yaml
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/schema/base_schema.yaml

  - include:
      file: db/changelog/schema/base_schema_procedures.yaml

  - include:
      file: db/changelog/data/unittest_data.yaml

  - include:
      file: db/changelog/data/dev_data.yaml

  - include:
      file: db/changelog/schema/consumable_addEligible.yaml

  - include:
      file: db/changelog/schema/extend_gameurl_column.yaml

  - include:
      file: db/changelog/schema/userinfo_addAppSettings.yaml

  - include:
      file: db/changelog/schema/remove_foreign_key_constraints.yaml

  - include:
      file: db/changelog/schema/remove_autoincr_users.yaml

  - include:
      file: db/changelog/schema/change_charsets.yaml

  - include:
      file: db/changelog/schema/recreate_user_events.yaml

  - include:
      file: db/changelog/schema/game_info_index.yaml

  - include:
      file: db/changelog/schema/recreate_tokens.yaml

  - include:
      file: db/changelog/schema/usergameattributes_addUnlocks.yaml

  - include:
      file: db/changelog/schema/add_wheels_table.yaml

  - include:
      file: db/changelog/schema/recreate_wheelspins.yaml

  - include:
      file: db/changelog/schema/purchase_addMethod.yaml

  - include:
      file: db/changelog/schema/payment_addMethod.yaml

  - include:
      file: db/changelog/schema/purchase_addPSP.yaml

  - include:
      file: db/changelog/schema/recreate_payments.yaml

  - include:
      file: db/changelog/schema/recreate_purchases.yaml

  - include:
      file: db/changelog/schema/recreate_games.yaml

  - include:
      file: db/changelog/schema/create_vendors.yaml

  - include:
      file: db/changelog/schema/rename_vendor_order.yaml

  - include:
      file: db/changelog/schema/recreate_games2.yaml

  - include:
      file: db/changelog/schema/add_internal_issues.yaml

  - include:
      file: db/changelog/schema/refactor_purchase.yaml

  - include:
      file: db/changelog/schema/purchase_addOrderRef.yaml

  - include:
      file: db/changelog/schema/vendor_add_game_order.yaml

  - include:
      file: db/changelog/schema/refactor_activegames.yaml

  - include:
      file: db/changelog/schema/refactor_game_statistics.yaml

  - include:
      file: db/changelog/schema/vendor_add_blocked_countries.yaml

  - include:
      file: db/changelog/schema/add_missions_table.yaml

  - include:
      file: db/changelog/schema/create_user_progress.yaml

  - include:
      file: db/changelog/schema/game_instance_add_vendorandplatformandcountry.yaml

  - include:
      file: db/changelog/schema/create_levels.yaml

  - include:
      file: db/changelog/data/levels_data.yaml

  - include:
      file: db/changelog/schema/recreate_user_progress.yaml

  - include:
      file: db/changelog/schema/create_accomplishments.yaml

  - include:
      file: db/changelog/schema/game_statistics_add_platform.yaml

  - include:
      file: db/changelog/schema/wheelspins_remove_id.yaml

  - include:
      file: db/changelog/schema/achievements_remove_version_appstoredef.yaml

  - include:
      file: db/changelog/schema/create_game_rank_daily.yaml

  - include:
      file: db/changelog/schema/create_user_reg.yaml

  - include:
      file: db/changelog/schema/users_remove_anonymous_and_more.yaml

  - include:
      file: db/changelog/schema/remove_foreign_key_deposits.yaml

  - include:
      file: db/changelog/schema/drop_unused_indices_tx_and_games.yaml

  - include:
      file: db/changelog/schema/drop_activation_table.yaml

  - include:
      file: db/changelog/schema/game_info_add_attributes.yaml

  - include:
      file: db/changelog/schema/games_add_sortOrder.yaml

  - include:
      file: db/changelog/schema/init_games_sort_order.yaml

  - include:
      file: db/changelog/schema/game_info_add_modified_at.yaml

  - include:
      file: db/changelog/schema/recreate_game_category.yaml

  - include:
      file: db/changelog/schema/remove_unused_indices.yaml

  - include:
      file: db/changelog/schema/recreate_game_statistics2.yaml

  - include:
      file: db/changelog/schema/create_gameinfo_content.yaml

  - include:
      file: db/changelog/schema/userinfo_add_username_public.yaml

  - include:
      file: db/changelog/schema/gameinfo_add_description_de.yaml

  - include:
      file: db/changelog/schema/user_events_add_client_version.yaml

  - include:
      file: db/changelog/schema/user_events_add_build_numbers.yaml

  - include:
      file: db/changelog/schema/user_events_add_device_infos.yaml

  - include:
      file: db/changelog/schema/mission_add_counts_and_index.yaml

  - include:
      file: db/changelog/schema/create_role_perm_table.yaml

  - include:
      file: db/changelog/schema/create_user_role_table.yaml

  - include:
      file: db/changelog/data/permissions_and_roles.yaml

  - include:
      file: db/changelog/schema/activegames_add_start_balance.yaml

  - include:
      file: db/changelog/schema/gamestats_add_start_balance.yaml

  - include:
      file: db/changelog/schema/recreate_users.yaml

  - include:
      file: db/changelog/schema/users_rename_system_field.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch.yaml

  - include:
      file: db/changelog/schema/create_view_gamesearch.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch_nonull.yaml

  - include:
      file: db/changelog/schema/recreate_view_gamesearch.yaml

  - include:
      file: db/changelog/schema/recreate_game_statistics3.yaml

  - include:
      file: db/changelog/schema/create_maintenance_tables.yaml

  - include:
      file: db/changelog/schema/create_maintenance_procedure.yaml

  - include:
      file: db/changelog/schema/create_view_vendorsearch.yaml

  - include:
      file: db/changelog/schema/create_user_verification_table.yaml

  - include:
      file: db/changelog/schema/userinfo_addEmail.yaml

  - include:
      file: db/changelog/schema/create_popup_tables.yaml

  - include:
      file: db/changelog/schema/recreate_popupDefinition_table.yaml

  - include:
      file: db/changelog/schema/consumable_addFilterExpression.yaml

  - include:
      file: db/changelog/schema/create_transaction_round_table.yaml

  - include:
      file: db/changelog/schema/create_maintenance_event_table.yaml

  - include:
      file: db/changelog/schema/create_user_removal_table.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch_properjoins.yaml

  - include:
      file: db/changelog/schema/create_session_table.yaml

  - include:
      file: db/changelog/schema/create_session_round_table_new.yaml

  - include:
      file: db/changelog/schema/add_jurisdiction_and_hversion.yaml

  - include:
      file: db/changelog/schema/create_session_transaction_table.yaml

  - include:
      file: db/changelog/schema/change_wallet_scale.yaml

  - include:
      file: db/changelog/schema/recreate_session_round_table_new.yaml

  - include:
      file: db/changelog/schema/create_user_transaction_table.yaml

  - include:
      file: db/changelog/schema/set_customseq_newtx.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch_namemailsearchfield.yaml

  - include:
      file: db/changelog/schema/change_charsets.yaml

  - include:
      file: db/changelog/schema/add_new_ranks_mechanics.yaml

  - include:
      file: db/changelog/schema/add_user_statistics_mechanics.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch_userstats.yaml

  - include:
      file: db/changelog/schema/create_user_login_table.yaml

  - include:
      file: db/changelog/schema/create_user_login_table2.yaml

  - include:
      file: db/changelog/schema/add_first_and_last_login_user_statistics.yaml

  - include:
      file: db/changelog/schema/add_user_statistics_login_triggers_and_proc.yaml

  - include:
      file: db/changelog/schema/create_view_usersearch_user_stats_login.yaml

  - include:
      file: db/changelog/schema/change_wallet_scale2.yaml

  - include:
      file: db/changelog/schema/add_user_statistics_login_triggers_and_proc2.yaml

  - include:
      file: db/changelog/schema/add_user_statistics_session_proc2.yaml

  - include:
      file: db/changelog/schema/user_progress_remove_partitioning.yaml

  - include:
      file: db/changelog/schema/remove_unused_tables_migrating.yaml

  - include:
      file: db/changelog/schema/user_game_attribute_remove_locking.yaml

  - include:
      file: db/changelog/schema/user_mail_add_pwhsh.yaml

  - include:
      file: db/changelog/schema/user_mail_remove_partitioning.yaml

  - include:
      file: db/changelog/schema/add_userinfo_address_kyc.yaml

  - includeAll:
      path: db/changelog/sessionsAndTransactions/*.yaml
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/games/*.yaml
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/users/*.yaml
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/schema/refactor_accomplishments.yaml

  - include:
      file: db/changelog/schema/refactor_notifications_nullable_dtts.yaml

  - includeAll:
      path: db/changelog/affiliates/*.yaml
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/security/*.yaml
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/schema/create_view_vendorsearch_migr.yaml

  - include:
      file: db/changelog/schema/d_games_and_info_add_vendor_id.yaml

  - include:
      file: db/changelog/schema/g_0_create_tables.yaml

  - include:
      file: db/changelog/schema/g_2_recreate_category_tables.yaml

  - include:
      file: db/changelog/schema/recreate_images_migr_table.yaml

  - include:
      file: db/changelog/schema/userview_add_logincounts.yaml

  - include:
      file: db/changelog/schema/add_new_aff_user_tables.yaml

  - include:
      file: db/changelog/schema/drop_unused_tables.yaml
