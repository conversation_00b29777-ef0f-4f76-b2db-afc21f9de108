DROP TABLE IF EXISTS `promotions`;

CREATE TABLE `promotions`
(
    `id`                     bigint                                 NOT NULL AUTO_INCREMENT,
    `type`                   VARCHAR(255) DEFAULT 'DEFAULT'         NOT NULL,
    `name`                   VARCHAR(255)                           NOT NULL,
    `enabled`                TINYINT      DEFAULT 0,
    `max_count_per_user`     int          DEFAULT 1                 NOT NULL,
    `cms_id`                 int          DEFAULT 0                 NOT NULL,
    `enabled_on_bonus_count` int          DEFAULT -1                NOT NULL,
    `aff_id`                 bigint       DEFAULT -1                NOT NULL,
    `config`                 JSON                                   NOT NULL,
    `created_by`             bigint                                 NOT NULL,
    `created_at`             TIMESTAMP    DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `valid_from`             TIMESTAMP    DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `valid_until`            TIMESTAMP NULL,
    `wager_factor`           int          DEFAULT 30                NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;