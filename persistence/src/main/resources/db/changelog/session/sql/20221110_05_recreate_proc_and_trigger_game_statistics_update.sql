CREATE TRIGGER game_stats_on_update
    AFTER UPDATE
    ON `game_statistics`
    FOR EACH ROW
    BEGIN
    CALL REPLACE_SESSION_RANK_ENTRY(NEW.start_at, NEW.session_id);
    IF ((NEW.num_plays - OLD.num_plays) > 0) THEN
        INSERT into user_statistics (user_id, ss_session_count, ss_first_session_at, ss_last_session_at, ss_num_spins,
                                     ss_num_wins, ss_sum_bet, ss_sum_win, ss_total_session_sec)
        values (NEW.user_id, 0, NEW.start_at,NEW.start_at, NEW.num_plays - OLD.num_plays, NEW.num_wins - OLD.num_wins, NEW.sum_bet - OLD.sum_bet,
                NEW.sum_win - OLD.sum_win,
                ABS(TIME_TO_SEC(TIMEDIFF(NEW.start_at, NEW.end_at))) -
                ABS(TIME_TO_SEC(TIMEDIFF(OLD.start_at, OLD.end_at))))
        on duplicate key update ss_sum_win = ss_sum_win + NEW.sum_win - OLD.sum_win,
                                ss_sum_bet = ss_sum_bet + NEW.sum_bet - OLD.sum_bet,
                                ss_num_spins = ss_num_spins + NEW.num_plays - OLD.num_plays,
                                ss_num_wins = ss_num_wins + NEW.num_wins - OLD.num_wins,
                                ss_last_session_at = GREATEST(COALESCE(ss_last_session_at, '2001-01-01'), NEW.start_at),
                                ss_first_session_at= LEAST(COALESCE(ss_first_session_at, '2035-01-01'), NEW.start_at),
                                ss_total_session_sec=ss_total_session_sec + ABS(TIME_TO_SEC(TIMEDIFF(NEW.start_at, NEW.end_at))) -
                                                     ABS(TIME_TO_SEC(TIMEDIFF(OLD.start_at, OLD.end_at)));
    END IF;
 END;
