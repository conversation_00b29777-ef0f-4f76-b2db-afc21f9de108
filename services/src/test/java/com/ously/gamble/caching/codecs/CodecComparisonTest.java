package com.ously.gamble.caching.codecs;

import com.ously.gamble.api.user.UserStats;
import org.junit.jupiter.api.Test;
import org.nustaq.serialization.FSTConfiguration;
import org.redisson.client.codec.Codec;
import org.redisson.client.handler.State;
import org.redisson.codec.FstCodec;
import org.redisson.codec.FuryCodec;

import java.io.IOException;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SuppressWarnings("deprecation")
public class CodecComparisonTest {


    @Test
    void testCodecs() throws IOException {

        FuryCodec defFury = new FuryCodec();

        var fstConfiguration1 = FSTConfiguration.createDefaultConfiguration();
        fstConfiguration1.setPreferSpeed(true);
        fstConfiguration1.setShareReferences(true);
        Codec defFst = new FstCodec(fstConfiguration1);

        testCodec(defFst, 100000);
        testCodec(defFury, 100000);
    }

    private long testCodec(Codec defFst, int iterations) throws IOException {
        Random rnd = new Random();
        UserStats us = new UserStats();
        org.redisson.client.handler.State state = new State();
        long size = 0L;
        long start = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            long uVal = rnd.nextLong();
            us.setMpb(uVal);
            var buf = defFst.getValueEncoder().encode(us);
            size += buf.readableBytes();
            UserStats decode = (UserStats) defFst.getValueDecoder().decode(buf, state);
            assertEquals(us.getMpb(), decode.getMpb());
            buf.release();
        }
        long end = System.currentTimeMillis();

        System.out.println(defFst.getClass().getName() + "->" + size + " took " + (end - start) + " ms");
        return size;
    }

}
