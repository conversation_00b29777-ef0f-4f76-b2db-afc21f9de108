package com.ously.gamble.caching;

import com.ously.gamble.api.cache.ScriptFactory;
import com.ously.gamble.api.cache.WrappedRedisScript;
import org.redisson.api.RScript.Mode;
import org.redisson.api.RScript.ReturnType;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("rawtypes")
@Component
public class RedisScriptFactory<T> implements ScriptFactory<T> {
    final Logger log = LoggerFactory.getLogger(RedisScriptFactory.class);

    private final RedissonClient client;

    private final Map<String, WrappedRedisScript> scripts = new ConcurrentHashMap<>();


    public RedisScriptFactory(RedissonClient rclnt) {
        this.client = rclnt;
    }


    @Override
    @SuppressWarnings("unchecked")
    public WrappedRedisScript<T> createScript(final String name, final String script,
                                              final ScriptMode mode,
                                              final ScriptReturnType rtype,
                                              final Class codecClass) {
        return scripts.computeIfAbsent(name, a -> new WrappedRedisScriptImpl<T>(client, name, script,
                getMode(mode), getType(rtype), getCodec(codecClass)));
    }

    private static Codec getCodec(Class codecClass) {
        return switch (codecClass.getName()) {
            case "Integer", "int" -> IntegerCodec.INSTANCE;
            case "Long", "long" -> LongCodec.INSTANCE;
            default -> StringCodec.INSTANCE;
        };
    }

    private static ReturnType getType(ScriptReturnType rtype) {
        return switch (rtype) {
            case MULTI -> ReturnType.MULTI;
            case BOOLEAN -> ReturnType.BOOLEAN;
            case VALUE -> ReturnType.VALUE;
            case STATUS -> ReturnType.STATUS;
            case INTEGER -> ReturnType.INTEGER;
            case MAPVALUE -> ReturnType.MAPVALUE;
            case MAPVALUELIST -> ReturnType.MAPVALUELIST;
        };
    }

    private static Mode getMode(ScriptMode mode) {
        return switch (mode) {
            case READ_ONLY -> Mode.READ_ONLY;
            case READ_WRITE -> Mode.READ_WRITE;
        };
    }


}
