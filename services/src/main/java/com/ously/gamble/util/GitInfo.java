package com.ously.gamble.util;

import java.io.IOException;
import java.util.Properties;

public class GitInfo {

    final String branch;
    final String commitId;
    final String commitIdShort;
    final Boolean dirty;
    final String messageShort;
    final String commitTime;

    GitInfo() {

        var p = new Properties();
        try {
            p.load(GitInfo.class.getResourceAsStream("/git.properties"));
        } catch (IOException e) {
            //
        }
        branch = p.getProperty("git.branch", "unknown");
        commitId = p.getProperty("git.commit.id", "-/-");
        commitIdShort = p.getProperty("git.commit.id.abbrev", "-/-");
        dirty = Boolean.valueOf(p.getProperty("git.dirty", "true"));
        messageShort = p.getProperty("git.commit.message.short", "-/-");
        commitTime = p.getProperty("git.commit.time", "-/-");
    }


    public static String getGitInfoString() {
        return new GitInfo().toDisplayString();
    }

    private String toDisplayString() {
        return commitIdShort + '/' + branch + '@' + commitTime + "( dirty: " + dirty + ")->" + messageShort;
    }
}
