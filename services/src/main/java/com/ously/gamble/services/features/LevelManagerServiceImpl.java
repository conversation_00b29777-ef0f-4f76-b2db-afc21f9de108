package com.ously.gamble.services.features;

import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.features.EntityChangeListener;
import com.ously.gamble.api.features.LevelChangedEvent;
import com.ously.gamble.api.features.LevelInfo;
import com.ously.gamble.api.features.LevelManagerService;
import com.ously.gamble.config.LevelConfiguration;
import com.ously.gamble.persistence.model.social.Level;
import com.ously.gamble.persistence.repository.LevelRepository;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class LevelManagerServiceImpl extends BaseOuslyService implements LevelManagerService {

    final Logger log = LoggerFactory.getLogger(LevelManagerServiceImpl.class);

    private final LevelRepository lRepo;
    private final RTopic changedLevelTopic;
    private final TransactionTemplate readOnlyTxTemplate;
    private final LevelConfiguration eConfig;
    private final WalletRepository wRepo;
    private List<LevelInfo> levelInfos;
    private List<LevelInfo> levelInfosApps;

    final List<EntityChangeListener<Integer>> listeners = new ArrayList<>(5);

    public LevelManagerServiceImpl(LevelRepository lRepo,
                                   RedissonClient client,
                                   PlatformTransactionManager transactionManager,
                                   LevelConfiguration eConfig, WalletRepository wRepo
    ) {
        this.lRepo = lRepo;
        this.eConfig = eConfig;
        readOnlyTxTemplate = new TransactionTemplate(transactionManager);
        readOnlyTxTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        readOnlyTxTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        changedLevelTopic = client.getTopic("levelChanged", new StringCodec());
        changedLevelTopic.addListener(String.class, (channel, message) -> flush(message));
        this.wRepo = wRepo;
    }

    @PreDestroy
    public void reap() {
        if (changedLevelTopic != null) {
            changedLevelTopic.removeAllListeners();
        }
    }


    @Override
    public void addEntityChangeListener(EntityChangeListener<Integer> a) {
        listeners.add(a);
    }

    @Override
    public void removeEntityChangeListener(EntityChangeListener<Integer> a) {
        listeners.remove(a);
    }


    private void flush(String e) {
        var lId = Integer.valueOf(e);
        log.info("Topic received/Flushing Levels due to changed/new Level {}", lId);
        levelInfos = null;
        getLevels();
        getLevelInfosFinalInternal();
        for (var listener : listeners) {
            listener.entityChanged(lId);
        }

    }

    @Override
    public List<LevelInfo> getLevelInfos() {
        if (levelInfos == null) {
            getLevels();
            getLevelInfosFinalInternal();
        }
        return levelInfos;
    }

    @Override
    public String getLevelInfosCsv() {
        if (levelInfos == null) {
            getLevels();
            getLevelInfosFinalInternal();
        }

        StringBuilder b = new StringBuilder();

        b.append("level");
        b.append("spinsStart");
        b.append("spinsEnd");
        b.append("xpStart");
        b.append("xpEnd");
        b.append("coins");
        b.append("\n");


        for (var li : levelInfos) {
            b.append(li.getLevel()).append(',');
            b.append(li.getStartSpins()).append(',');
            b.append(li.getEndSpins()).append(',');
            b.append(li.getStartXp()).append(',');
            b.append(li.getEndXp()).append(',');
            b.append(li.getAchievementCoins());
            b.append("\n");
        }
        return b.toString();
    }

    @Override
    @Transactional(readOnly = true)
    public List<LevelInfo> getLevelInfosFinal(long userId, int previous, int next) {
        long userLevel = wRepo.getUserLevel(userId) + 1;
        List<LevelInfo> levelInfosFinalInternal = getLevelInfosFinalInternal();
        int slFrom;
        int slTo = levelInfosFinalInternal.size();
        if (previous < 0) {
            slFrom = 0;
        } else {
            slFrom = Math.min(Math.max((int) userLevel - previous, 0), slTo);
        }
        if (next >= 0) {
            slTo = Math.min((int) userLevel + next, slTo);
        }

        return levelInfosFinalInternal.subList(slFrom, slTo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LevelInfo> getLevelInfosFinal(long userId) {
        if (userId < 0L) {
            return getLevelInfosFinalInternal();
        } else {
            return getLevelInfosFinal(userId, -1, -1);
        }
    }


    public synchronized List<LevelInfo> getLevelInfosFinalInternal() {
        if (levelInfosApps == null) {
            if (levelInfos == null) {
                getLevels();
            }

            List<LevelInfo> finalInfos = new ArrayList<>(levelInfos.size());
            for (var li : levelInfos) {

                var nli = new LevelInfo();
                nli.setLevel(li.getLevel()+1);
                nli.setEndXp(li.getEndXp());
                nli.setStartXp(li.getStartXp());
                nli.setStartSpins(li.getStartSpins());
                nli.setEndSpins(li.getEndSpins());
                var coinsApplied = false;

                List<Reward> nRewards = new ArrayList<>(li.getRewards().size() + 1);
                for (var r : li.getRewards()) {
                    var nr = new Reward();
                    nr.setAmount(r.getAmount());
                    nr.setCount(r.getCount());
                    nr.setMinutes(r.getMinutes());
                    nr.setMultiplier(r.getMultiplier());
                    nr.setType(r.getType());
                    if ("S".equalsIgnoreCase(nr.getType())) {
                        long scaledLevelUpCoins = eConfig.getScaledLevelUpCoins((long) (nr.getAmount().doubleValue() + li.getAchievementCoins()));
                        nr.setAmount(Double.valueOf((double) scaledLevelUpCoins));
                        coinsApplied = true;
                    }
                    nRewards.add(nr);
                }
                if (!coinsApplied) {
                    var cA = new Reward();
                    cA.setType("S");
                    long scaledLevelUpCoins = eConfig.getScaledLevelUpCoins(li.getAchievementCoins());
                    cA.setAmount(Double.valueOf((double) scaledLevelUpCoins));
                    nRewards.add(cA);
                }
                nli.setRewards(nRewards);
                nli.setParameter(li.getParameter());
                finalInfos.add(nli);
            }
            levelInfosApps = finalInfos;
        }

        return levelInfosApps;
    }


    private void getLevels() {
        if (levelInfos == null) {
            var levels = new ArrayList<>(
                    readOnlyTxTemplate.execute(result -> lRepo.findAll(Sort.by(Direction.ASC, "level")))
            );
            levelInfos = LevelManagerUtil.autogenerateLevels(levels.stream().map(LevelInfo::new).collect(Collectors.toList()),
                    eConfig);
        }
    }


    private LevelInfo saveLevel(Level level) {
        lRepo.saveAndFlush(level);
        levelInfos.set(level.getLevel(), new LevelInfo(level));
        publishEvent(new LevelChangedEvent(level.getLevel()));
        return new LevelInfo(level);
    }

    @Override
    public LevelInfo getLevelInfo(int level) {
        if(levelInfosApps==null){
            getLevelInfosFinalInternal();
        }
        return levelInfosApps.get(level);
    }

    @Override
    @Transactional
    public LevelInfo updateLevel(Integer lId, LevelInfo lInfo) {
        var level = lRepo.findById(lId).orElseThrow();
        lInfo.updateModel(level);
        return saveLevel(level);
    }


    @EventListener(LevelChangedEvent.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void onLevelChanged(LevelChangedEvent lvlChanged) {
        try {
            var publish = changedLevelTopic.publish(Long.toString(lvlChanged.getId()));
            log.info("Pushing Level change to {} listeners", publish);
        } catch (Exception e) {
            log.error("Error on EventChangedListener:{}", lvlChanged, e);
        }
        log.info("Pushed Level change to topic");
        for (var listener : listeners) {
            listener.entityChanged(lvlChanged.getId());
        }
    }

}
