package com.ously.gamble.services.user;

import com.ously.gamble.api.events.DeduplicationService;
import com.ously.gamble.api.events.UserLoginDeduplicationRequest;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.configprops.UserLoginsDeduplicationConfiguration;
import com.ously.gamble.persistence.repository.user.UserLoginsRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@ConditionalOnOffloader
public class DeduplicationServiceImpl extends BaseOuslyService implements DeduplicationService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final UserLoginsRepository ulsRepo;
    private final UserLoginsDeduplicationConfiguration ulDedupConfig;


    public DeduplicationServiceImpl(UserLoginsRepository ulsRep, UserLoginsDeduplicationConfiguration uldedupConfig) {
        this.ulsRepo = ulsRep;
        this.ulDedupConfig = uldedupConfig;
    }

    @Override
    public List<String> requestUserLoginDeduplication(LocalDate from, LocalDate to, boolean simulate) {
        List<String> daysRequested = new ArrayList<>(10);
        for (var start = from; start.isBefore(to); start = start.plusDays(1)) {
            for (int i = 0; i < ulDedupConfig.getBuckets(); i++) {
                publishEvent(new UserLoginDeduplicationRequest(start, ulDedupConfig.getBuckets(), i, simulate));
            }
            daysRequested.add(start.toString());
        }
        return daysRequested;
    }

    @Override
    @Transactional(timeout = 120)
    public void doUserLoginDeduplication(UserLoginDeduplicationRequest req) {
        if (req.simulate()) {
            log.info("Simulation of dedupl req: {}", req);
        } else {
            log.info("Deduplication (SIMPLE) for {} starting", req.date());
            int insertions = ulsRepo.identifyAndInsertDuplicatesSimpleCase(req.date(), req.buckets(), req.reqBucket());
            int deletions = 0;
            if (insertions > 0) {
                deletions = ulsRepo.removeDuplicatesSimpleCase(req.date(), req.buckets(), req.reqBucket());
            }
            log.info("Deduplication (SIMPLE) for {} has removed {} simple duplicates", req, (deletions - insertions));
        }
    }

}
