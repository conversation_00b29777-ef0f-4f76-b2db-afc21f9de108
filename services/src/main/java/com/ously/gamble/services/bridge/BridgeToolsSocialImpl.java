package com.ously.gamble.services.bridge;

import com.ously.gamble.api.achievements.AchievementManagementService;
import com.ously.gamble.api.bridge.BridgeTools;
import com.ously.gamble.api.bridge.UserSessionBonusStatus;
import com.ously.gamble.api.features.TokenService;
import com.ously.gamble.api.features.WheelUtilService;
import com.ously.gamble.api.rewards.tickets.RewardTicketService;
import com.ously.gamble.api.user.UserActivePerk;
import com.ously.gamble.api.user.UserStats;
import com.ously.gamble.config.PiggyBankConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * A Cache for user, wallet, tokens ... which when not available tries to read entity from db
 */

@Component
@Lazy
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
        matchIfMissing = true)
public class BridgeToolsSocialImpl extends BridgeToolsBase implements BridgeTools {

    @Autowired
    private Optional<PiggyBankConfiguration> pbc;

    @Autowired(required = false)
    private RewardTicketService rewTkService;

    @Autowired
    private WheelUtilService wheelUtil;

    @Autowired
    TokenService tMgmt;

    @Autowired
    AchievementManagementService achMgmSrv;

    /**
     * @return true if something is expired
     */
    @Override
    boolean filterExpiredPerks(UserStats stats) {
        if (stats.getActivePerks() == null || stats.getActivePerks().isEmpty()) {
            return false;
        }
        var size = stats.getActivePerks().size();
        stats.setActivePerks(stats.getActivePerks().stream().filter(UserActivePerk::isValid).collect(Collectors.toList()));
        return (size != stats.getActivePerks().size());
    }

    @Override
    public UserStats createUserStatsEntry(Long id, String langCode) {
        return transactionTemplate.execute(result -> {
                    var achievements = achMgmSrv.getUserAchievements(id, langCode, false);
                    var tokens = tMgmt.getTokensByUserIdGTZero(id);
                    var wallet = wRepo.findById(id).get();
                    Integer ucRew = (rewTkService == null) ? null : rewTkService.getUnclaimedRewardTicketCount(id);
                    var us = UserStats.NewForSocial(wallet, achievements, tokens, null, ucRew);
                    us.setPiggyStatus(calculatePiggyStatus(wallet.getSaveup()));
                    if (wheelUtil != null) {
                        us.setCpws(wheelUtil.getCostForLevel(wallet.getLevel()));
                    }
                    return us;
                }
        );
    }

    @Override
    public void updateCostPerSpin(UserStats us, int level) {
        if (wheelUtil != null) {
            us.setCpws(wheelUtil.getCostForLevel(level));
        }
    }

    @Override
    public UserSessionBonusStatus getUserSessionBonusStatus(long userId, long gameId) {
        return UserSessionBonusStatus.FORBIDDEN;
    }

    @Override
    public Long calculatePiggyStatus(BigDecimal saveup) {
        if (pbc.isEmpty()) {
            return null;
        }

        if (saveup == null) {
            return 0L;
        }
        if (saveup.signum() == 0) {
            return 0L;
        }
        var statF = saveup.divide(pbc.get().getActivationAmount(), 2, RoundingMode.HALF_UP).doubleValue();
        return (long) (statF * 100);
    }


}
