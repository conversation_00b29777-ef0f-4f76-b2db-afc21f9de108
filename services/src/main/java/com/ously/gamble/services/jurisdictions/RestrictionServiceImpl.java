package com.ously.gamble.services.jurisdictions;


import com.ously.gamble.api.cache.CachedSet;
import com.ously.gamble.api.cache.CodecType;
import com.ously.gamble.api.events.GeoIpLookup;
import com.ously.gamble.api.jurisdiction.CasinoJurisdictionCountry;
import com.ously.gamble.api.jurisdiction.RestrictionResponse;
import com.ously.gamble.api.jurisdiction.RestrictionService;
import com.ously.gamble.caching.RedisCachedMapFactory;
import com.ously.gamble.persistence.model.jurisdiction.JurisdictionCountry;
import com.ously.gamble.persistence.repository.jurisdiction.JurisdictionCountryRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(prefix = "restrictions", name = "enabled", havingValue = "true")
public class RestrictionServiceImpl implements RestrictionService {
    @SuppressWarnings("FieldCanBeLocal")
    private final Logger log = LoggerFactory.getLogger(RestrictionServiceImpl.class);

    private final JurisdictionCountryRepository jdCRepo;

    private final GeoIpLookup geoIpService;

    private final CachedSet<String> blockedCountries;


    public RestrictionServiceImpl(JurisdictionCountryRepository jdCRepo,
                                  Optional<GeoIpLookup> geoIpService,
                                  RedisCachedMapFactory<String, String> rcmF) {
        this.jdCRepo = jdCRepo;
        this.geoIpService = geoIpService.orElse(null);
        blockedCountries = rcmF.createCachedSet("cntr_block", CodecType.DEFAULT, String.class, 0);
        initBlocklist();
        log.info("Running with global region restrictions, {} regions blocked", blockedCountries.size());
    }

    private void initBlocklist() {
        var collect = jdCRepo.findAll().stream().filter(jdc -> jdc.isBlocked() || jdc.isBoycot()).map(JurisdictionCountry::getCode).collect(Collectors.toSet());
        blockedCountries.addAll(collect);
        blockedCountries.retainAll(collect);
    }

    @Override
    public boolean isBlocked(String countryCode) {
        if (StringUtils.isEmpty(countryCode)) {
            return false;
        }
        return blockedCountries.contains(countryCode);
    }

    @Override
    public List<CasinoJurisdictionCountry> getAllCountries() {
        return jdCRepo.findAll().stream().map(CasinoJurisdictionCountry::new).collect(Collectors.toList());
    }

    @Override
    public Optional<CasinoJurisdictionCountry> getCountryForCode(String code) {
        return jdCRepo.findById(code).map(CasinoJurisdictionCountry::new);
    }

    @Override
    public Optional<RestrictionResponse> getByIp(String ip) {
        if (geoIpService == null) {
            return Optional.empty();
        }
        try {
            var country = geoIpService.getCountry(InetAddress.getByName(ip));
            var jurisdiction = getCountryForCode(country);
            return jurisdiction.map(casinoJurisdictionCountry -> new RestrictionResponse(country, casinoJurisdictionCountry)).or(() -> Optional.of(new RestrictionResponse(country, null)));
        } catch (Exception ignored) {
            return Optional.empty();
        }

    }

    @Override
    @Transactional
    public Optional<CasinoJurisdictionCountry> updateCountry(CasinoJurisdictionCountry cnt) {

        return jdCRepo.findById(cnt.code()).map(a -> {
            a.setName(cnt.name());
            a.setBlocked(cnt.blocked());
            a.setBoycot(cnt.boycot());
            jdCRepo.saveAndFlush(a);
            if (a.isBlocked() || a.isBoycot()) {
                blockedCountries.add(a.getCode());
            } else {
                blockedCountries.remove(a.getCode());
            }
            return new CasinoJurisdictionCountry(a);
        });

    }

    @Override
    @Transactional
    public Optional<CasinoJurisdictionCountry> addCountry(CasinoJurisdictionCountry cnt) {
        var optCCd = jdCRepo.findById(cnt.code());
        if (optCCd.isPresent()) {
            return Optional.of(new CasinoJurisdictionCountry(optCCd.get()));
        }
        var nJC = new JurisdictionCountry();
        nJC.setBoycot(cnt.boycot());
        nJC.setBlocked(cnt.blocked());
        nJC.setName(cnt.name());
        nJC.setCode(cnt.code());
        jdCRepo.saveAndFlush(nJC);
        if (nJC.isBlocked() || nJC.isBoycot()) {
            blockedCountries.add(nJC.getCode());
        } else {
            blockedCountries.remove(nJC.getCode());
        }
        return Optional.of(new CasinoJurisdictionCountry(nJC));
    }
}
