package com.ously.gamble.services.game;

import com.ously.gamble.api.games.GameGateway;
import com.ously.gamble.api.session.GameStatisticsChangedEvent;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class GameGatewayImpl implements GameGateway {

    private final RabbitTemplate rbTmpl;

    public GameGatewayImpl(@Qualifier("rabbitTemplateNoTx") RabbitTemplate tbT) {
        this.rbTmpl = tbT;
    }


    @Override
    public void sendGameStatisticsChangedEvent(GameStatisticsChangedEvent gsChangedEvent) {
        try {
            rbTmpl.convertAndSend(GameGateway.STATISTICS_UPDATED, gsChangedEvent);
        } catch (Exception e) {
        }
    }

    @Override
    public void sendLeaderboardExpiry(GameStatisticsChangedEvent expEvent) {
        try {
            rbTmpl.convertAndSend(GameGateway.LEADERBOARD_EXPIRY, expEvent);
        } catch (Exception e) {
        }
    }
}
