package com.ously.gamble.services.user;

import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.localisation.LanguageCode;
import com.ously.gamble.api.localisation.LocalisationService;
import com.ously.gamble.api.notification.EmailHandler;
import com.ously.gamble.api.user.UserMessageActionResult;
import com.ously.gamble.api.user.UserMessageHandler;
import com.ously.gamble.api.user.UserMessageService;
import com.ously.gamble.api.user.UserMessageStatus;
import com.ously.gamble.api.user.removal.UserCleanupEvent;
import com.ously.gamble.api.util.CryptUtilityService;
import com.ously.gamble.persistence.dto.UserMessageDto;
import com.ously.gamble.persistence.model.idclasses.UserMessageId;
import com.ously.gamble.persistence.model.messages.UserMessage;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import com.ously.gamble.persistence.repository.UserMessageRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleStateException;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ously.gamble.api.user.UserMessageActionResult.failed;

@ConditionalOnProperty(prefix = "features", name = "messages", havingValue = "true")
@Service
public class UserMessageServiceImpl extends BaseOuslyService implements UserMessageService {

    private final Logger log = LoggerFactory.getLogger(UserMessageServiceImpl.class);

    private final UserMessageRepository umRepo;
    private final LocalisationService locService;
    private final CryptUtilityService cryptUtil;
    private final EmailHandler emailHndlr;


    private final Map<UserMessageType, List<UserMessageHandler>> msgHandlers;


    public UserMessageServiceImpl(UserMessageRepository umRepo, LocalisationService locService,
                                  CryptUtilityService crUtil,
                                  Optional<EmailHandler> emHandler,
                                  List<UserMessageHandler> mHandlers) {
        this.umRepo = umRepo;
        this.locService = locService;
        this.cryptUtil = crUtil;
        this.emailHndlr = emHandler.orElse(null);

        msgHandlers = new EnumMap<>(UserMessageType.class);
        mHandlers.forEach(
                h -> h.supportedTypes().forEach(t -> msgHandlers.computeIfAbsent(t, key -> new ArrayList<>()).add(h))
        );


    }

    @Override
    @Transactional
    public Page<UserMessageDto> getMessages(long userId, String langCode, Pageable pgl) {
        var all = umRepo.findAllByUserId(userId, pgl);
        return new PageImpl<>(all.getContent().stream().map(m -> {
            var type = getMessageType(m.getContent());
            var titleBodyPair = resolveContent(m.getContent(), langCode);
            return new UserMessageDto(getSecret(m), m.getQualifier(), type, titleBodyPair.getValue0(),
                    titleBodyPair.getValue1(), m.getCreatedAt(), m.getExpireAt(), m.isRead(),
                    m.isPopup(), m.getContent().getFiltered());
        }).toList(), pgl, all.getTotalElements());
    }

    private static UserMessageType getMessageType(UserMessageContent content) {
        if (content != null && content.type() != null) {
            return content.type();
        }
        return UserMessageType.CUSTOM;
    }


    @Override
    @Transactional
    public boolean createNewUserMessage(long userId,
                                        String qualifier,
                                        UserMessageContent content, boolean popup) {
        return createNewUserMessage(userId, qualifier, content, popup, true);
    }

    @Override
    @Transactional
    public boolean createNewUserMessage(long userId,
                                        String qualifier,
                                        UserMessageContent content, boolean popup, boolean flushStats) {
        if (content.type().isInbox()) {
            var byUserIdAndQualifier = umRepo.findByUserIdAndQualifier(userId, qualifier);
            if (byUserIdAndQualifier.isEmpty()) {
                var um = new UserMessage();
                um.setUserId(userId);
                var cType = content.type();
                var titleLit = (StringUtils.isAllBlank(content.title()) ? cType.title() : content.title());
                var bodyLit = (StringUtils.isAllBlank(content.content())) ? cType.body() : content.content();


                content = new UserMessageContent(cType, titleLit, bodyLit, (content.ttl() == null) ? cType.expiry() : content.ttl(),
                        content.variables(), content.actions());

                content = checkAndRepairContent(content);


                um.setContent(content);
                um.setExpireAt(createExpiryInstant(content.ttl()));
                um.setQualifier(qualifier);
                um.setPopup(popup);
                um.setRead(false);
                umRepo.saveAndFlush(um);
                log.debug("Added message {} to user {} inbox", content.type(), userId);
                publishEvent(new RefreshUserMessagesEvent(userId));
            }
        }
        if (emailHndlr != null && content.type().isEmail()) {
            log.debug("Sending this Message {} to user {} via email", content.type()
                    , userId);
            emailHndlr.sendOut(userId, qualifier, content);
        }
        // Sent out CRM event
        if (content.type().eventName() != null) {
            publishEvent(CRMUserEvent.fromMap(userId, content.type().eventName(),
                    content.variables()));
        }

        return true;
    }

    static UserMessageContent checkAndRepairContent(UserMessageContent content) {

        if (content.type() == UserMessageType.REWARD) {
            var r1 = content.variables().getOrDefault("rewards", "");
            var rewardsArrayFromDefinition = Reward.createRewardsArrayFromDefinition(r1);
            var r2 =
                    Arrays.stream(rewardsArrayFromDefinition).map(Reward::getStringDefinition).collect(Collectors.joining(","));
            if (r1.compareTo(r2) != 0) {
                Map<String, String> nVars = new HashMap<>(content.variables());
                nVars.put("rewards", r2);
                return new UserMessageContent(content.type(), content.title(), content.content(),
                        content.ttl(),
                        nVars,
                        content.actions());
            }
        }


        return content;
    }

    private Instant createExpiryInstant(String ttl) {
        if (StringUtils.isAllBlank(ttl)) {
            return null;
        }
        try {
            var dt = Duration.parse(ttl);
            return Instant.now().plus(dt.toSeconds(), ChronoUnit.SECONDS);
        } catch (Exception e) {
            log.warn("Given ttl '{}' cannot be parsed", ttl);
            return null;
        }
    }


    private Pair<String, String> resolveContent(UserMessageContent content, String langCode) {
        var stringStringMap = locService.resolveTemplatesForLang(content.variables(), getLangCodeFor(langCode), content.title(),
                content.content());

        return Pair.with(stringStringMap.get(content.title()), stringStringMap.get(content.content()));
    }

    private static LanguageCode getLangCodeFor(String langCode) {
        // TODO: align with upcoming multilang changes
        return switch (langCode.toUpperCase(Locale.ROOT)) {
            case "DE" -> LanguageCode.DE;
            default -> LanguageCode.EN;
        };
    }

    @Transactional
    @Override
    public UserMessageStatus getMessageStatusForUser(long userId) {
        var umStatVal = umRepo.getMessageStatus(userId);
        return new UserMessageStatus((int) (umStatVal / 1000), (int) (umStatVal % 1000));
    }

    @Override
    @Transactional
    public Optional<UserMessageDto> getOldestPopupMessage(long userId, String langCode) {
        var optPopup = umRepo.findFirstByUserIdAndReadIsFalseAndPopupIsTrueOrderByCreatedAtAsc(userId);
        return resolveContentToOptionalUserMessageDto(langCode, optPopup);
    }

    @Override
    @Transactional
    @Retryable(
            retryFor = {OptimisticLockException.class, SQLException.class, SQLIntegrityConstraintViolationException.class, StaleStateException.class},
            backoff = @Backoff(delay = 100L))
    public UserMessageActionResult doAction(String secret, String action) {

        var objects = decodeSecret(secret);
        Long userId = objects.getValue0();
        if (userId <= 0) {
            log.warn("Invalid userId '{}'", userId);
            return failed(action);
        }

        var messageId = new UserMessageId(userId, objects.getValue1());
        var optMessage = umRepo.findById(messageId);
        if (optMessage.isEmpty()) {
            log.warn("Message not found '{}'", messageId);
            return failed(action);
        }

        var userMessage = optMessage.get();
        UserMessageType contentType = userMessage.getContent().type();
        var userMessageHandlers = msgHandlers.get(contentType);
        if (userMessageHandlers == null || userMessageHandlers.isEmpty()) {
            log.warn("No message handler found for content type '{}'", contentType);
            return failed(action);
        }

        for (var msgHndlr : userMessageHandlers) {
            var userMessageActionResult = msgHndlr.handleMessage(userMessage, action);
            if (userMessageActionResult.success()) {
                sendMsgEvent(userMessage.getUserId(), userMessage.getContent());
                userMessage.setRead(true);
                prolongExpiry(userMessage);
                return userMessageActionResult;
            }
        }
        log.warn("All handlers failed");
        return failed(action);
    }

    private void sendMsgEvent(long userId, UserMessageContent content) {
        if (content.variables() == null || content.variables().isEmpty()) {
            return;
        }
        var convId = content.variables().get("@conversionId");
        if (convId != null) {
            try {
                var comps = convId.split(":");
                publishEvent(new CRMUserEvent(userId, "MSG_ACK", "campaign_id", comps[0], "campaign_type", comps[1],
                        "delivery_id", comps[2]));
            } catch (Exception e) {
                //
            }
        }
    }


    @Override
    @Transactional
    public boolean markMessageRead(String secret) {
        var objects = decodeSecret(secret);
        if (objects.getValue0() > 0) {
            var byId = umRepo.findById(new UserMessageId(objects.getValue0(), objects.getValue1()));
            if (byId.isEmpty()) {
                publishEvent(new RefreshUserMessagesEvent(objects.getValue0()));
                return false;
            }
            var userMessage = byId.get();

            if (userMessage.isRead()) {
                publishEvent(new RefreshUserMessagesEvent(objects.getValue0()));
                return false;
            }

            userMessage.setRead(true);
            sendMsgEvent(userMessage.getUserId(), userMessage.getContent());
            prolongExpiry(userMessage);
            publishEvent(new RefreshUserMessagesEvent(userMessage.getUserId()));
            return true;
        }
        return false;
    }

    private void prolongExpiry(UserMessage userMessage) {
        try {
            // PROLONG EXPIRY
            if (StringUtils.isAllBlank(userMessage.getContent().ttl())) {
                var parse = Duration.parse(userMessage.getContent().type().expiryRead());
                userMessage.setExpireAt(userMessage.getCreatedAt().plus(parse));
            }
        } catch (Exception e) {
            log.error("Error parsing duration {}", userMessage.getContent().type().expiryRead(), e);
        }
        umRepo.saveAndFlush(userMessage);
    }

    @Override
    @Transactional
    public boolean deleteMessage(String secret) {
        var objects = decodeSecret(secret);
        if (objects.getValue0() > 0) {
            var byId = umRepo.findById(new UserMessageId(objects.getValue0(), objects.getValue1()));
            if (byId.isEmpty()) {
                return false;
            }
            sendMsgEvent(byId.get().getUserId(), byId.get().getContent());
            umRepo.delete(byId.get());
            publishEvent(new RefreshUserMessagesEvent(byId.get().getUserId()));
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean markMessageReadQ(String qualifier, long userId) {
        var byId = umRepo.findById(new UserMessageId(userId, qualifier));
        if (byId.isEmpty()) {
            return false;
        }

        var userMessage = byId.get();

        if (userMessage.isRead()) {
            return false;
        }
        userMessage.setRead(true);
        sendMsgEvent(userMessage.getUserId(), userMessage.getContent());

        prolongExpiry(userMessage);
        publishEvent(new RefreshUserMessagesEvent(userMessage.getUserId()));
        return true;
    }

    @Override
    @Transactional
    public boolean deleteMessageQ(String qualifier, long userId) {
        var byId = umRepo.findById(new UserMessageId(userId, qualifier));
        if (byId.isEmpty()) {
            return false;
        }
        sendMsgEvent(byId.get().getUserId(), byId.get().getContent());
        umRepo.delete(byId.get());
        publishEvent(new RefreshUserMessagesEvent(byId.get().getUserId()));
        return true;
    }

    @Override
    @Transactional
    public Optional<UserMessageDto> getMessageByQualifier(long userId, String language,
                                                          String qualifier) {
        var msg = umRepo.findByUserIdAndQualifier(userId, qualifier);

        return resolveContentToOptionalUserMessageDto(language, msg);
    }

    @Override
    @Transactional
    public void expireMessages() {
        umRepo.expireMessages();
    }

    private Optional<UserMessageDto> resolveContentToOptionalUserMessageDto(String language,
                                                                            Optional<UserMessage> msg) {
        if (msg.isEmpty()) {
            return Optional.empty();
        }

        var titBodyPair = resolveContent(msg.get().getContent(), language);
        return Optional.of(new UserMessageDto(getSecret(msg.get()),
                msg.get().getQualifier(),
                getMessageType(msg.get().getContent()), titBodyPair.getValue0(),
                titBodyPair.getValue1(), msg.get().getCreatedAt(), msg.get().getExpireAt(), msg.get().isRead(),
                msg.get().isPopup(), msg.get().getContent().getFiltered()));
    }

    @Override
    @Transactional
    public boolean markAllMessagesRead(Long userId) {
        try {
            umRepo.markAllAsReadByUserId(userId);
            publishEvent(new RefreshUserMessagesEvent(userId));
            return true;
        } catch (Exception ignored) {
            return false;
        }
    }

    private String getSecret(UserMessage userMessage) {
        return encrypt(userMessage.getUserId() + "@" + userMessage.getQualifier() + '@' + UUID.randomUUID());
    }

    private Pair<Long, String> decodeSecret(String secret) {
        try {
            var dec = decrypt(secret);
            var split = dec.split("@");
            return Pair.with(Long.valueOf(split[0]), split[1]);
        } catch (Exception e) {
            log.error("Error decrypting:{}", secret);
            return Pair.with(-1L, "");
        }
    }

    private String encrypt(String key) {
        try {
            return cryptUtil.encryptStringBase64(key);
        } catch (Exception e) {
            return null;
        }
    }

    private String decrypt(String secret) {
        try {
            return cryptUtil.decryptStringBase64(secret);
        } catch (Exception e) {
            return "-1:";
        }
    }

    @EventListener
    public void handleCleanup(UserCleanupEvent evnt) {
        umRepo.deleteAllByUserId(evnt.id());
    }
}
