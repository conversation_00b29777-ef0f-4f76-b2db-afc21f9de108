package com.ously.gamble.services.popups;

import com.ously.gamble.api.popups.CasinoPopupDefinition;
import com.ously.gamble.api.popups.CasinoPopupRequest;
import com.ously.gamble.api.popups.CasinoUserPopup;
import com.ously.gamble.api.popups.PopupManagementService;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.events.UserStatsExpiryEvent;
import com.ously.gamble.persistence.model.idclasses.UserPopupId;
import com.ously.gamble.persistence.model.popups.PopupDefinition;
import com.ously.gamble.persistence.model.popups.UserPopup;
import com.ously.gamble.persistence.repository.PopupDefinitionRepository;
import com.ously.gamble.persistence.repository.UserPopupRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * Popup Management (currently uncached)
 */
@Service
@ConditionalOnNotMonitor
public class PopupManagementServiceImpl extends BaseOuslyService implements PopupManagementService {
    private final PopupDefinitionRepository pdRepo;
    private final UserPopupRepository upRepo;

    public PopupManagementServiceImpl(PopupDefinitionRepository pdRepo,
                                      UserPopupRepository upRepo) {
        this.pdRepo = pdRepo;
        this.upRepo = upRepo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CasinoPopupDefinition> getDefinitions() {
        return pdRepo.findAll().stream().map(CasinoPopupDefinition::new).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CasinoPopupDefinition> getDefinition(long id) {
        var byId = pdRepo.findById(id);
        return byId.map(CasinoPopupDefinition::new);
    }

    @Override
    @Transactional
    public Optional<CasinoPopupDefinition> updateDefinition(CasinoPopupDefinition pd) {
        if (pd.id() == null) {
            return Optional.empty();
        }
        var byId = pdRepo.findById(pd.id());
        if (byId.isEmpty()) {
            return Optional.empty();
        }
        var pdE = byId.get();
        pd.updateModel(pdE);
        pdRepo.save(pdE);
        return Optional.of(new CasinoPopupDefinition(pdE));
    }

    @Override
    @Transactional
    public Optional<CasinoPopupDefinition> newDefinition(CasinoPopupDefinition pd) {
        if (pd.id() != null) {
            return Optional.empty();
        }
        var pdE = new PopupDefinition();
        pd.updateModel(pdE);
        pdE = pdRepo.saveAndFlush(pdE);
        return Optional.of(new CasinoPopupDefinition(pdE));
    }

    @Override
    @Transactional
    public Optional<CasinoUserPopup> createOrUpdatePopupForUser(CasinoPopupRequest cpReq,
                                                                long userId) {
        long popupDefId = cpReq.popupDefinitionId();
        var byId = pdRepo.findById(popupDefId);
        if (byId.isEmpty()) {
            return Optional.empty();
        }
        var up = new UserPopup();
        up.setUserId(cpReq.userId());
        up.setPopupId(popupDefId);
        up.setSelector(cpReq.selector());
        up.setExpiresAt(cpReq.expiresAt());
        up.setCreatedAt(Instant.now());
        if (cpReq.variables() != null) {
            up.setVariables(String.join(",", cpReq.variables()));
        } else {
            up.setVariables("");
        }
        up = upRepo.saveAndFlush(up);
        publishEvent(new UserStatsExpiryEvent(userId));
        return Optional.of(new CasinoUserPopup(up));
    }

    @Override
    @Transactional(readOnly = true)
    public List<CasinoUserPopup> getPopupsForUser(long userId) {
        return upRepo.findAllByUserId(userId).stream().map(CasinoUserPopup::new).toList();
    }

    @Override
    @Transactional
    public void removeExpiredPopups() {
        upRepo.deleteExpired();
    }

    @Override
    public void createPopupIfNotExists(CasinoPopupRequest cpr, long uid) {
        long popupDefId = cpr.popupDefinitionId();
        var byId = pdRepo.findById(popupDefId);
        if (byId.isEmpty()) {
            return;
        }

        // Check selector, if empty create a "daily" selector for today:
        var selector = cpr.selector();
        if (StringUtils.isEmpty(selector)) {
            selector = String.format("%s-%tF", popupDefId, LocalDateTime.now());
        }

        var upId = new UserPopupId(uid, popupDefId, selector);
        if (upRepo.existsById(upId)) {
            return;
        }

        var expiresAt = cpr.expiresAt();
        if (expiresAt == null) {
            expiresAt = Instant.now().plus(8, ChronoUnit.HOURS);
        }


        var up = new UserPopup();
        up.setUserId(cpr.userId());
        up.setPopupId(popupDefId);
        up.setSelector(selector);
        up.setExpiresAt(expiresAt);
        up.setCreatedAt(Instant.now());
        if (cpr.variables() != null) {
            up.setVariables(String.join(",", cpr.variables()));
        } else {
            up.setVariables("");
        }
        upRepo.saveAndFlush(up);
        publishEvent(new UserStatsExpiryEvent(cpr.userId()));
    }

}
