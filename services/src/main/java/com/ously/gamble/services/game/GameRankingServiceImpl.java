package com.ously.gamble.services.game;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.cache.LCacheLoader;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.games.GameRankEntry;
import com.ously.gamble.api.games.GameRankingService;
import com.ously.gamble.persistence.model.game.DailyGameRank;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.repository.game.DailyGameRankRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class GameRankingServiceImpl implements GameRankingService {

    private static final int LOOKBACK_PERIOD = 14;
    private static final int CURRENT_RANK_SIZE = 50;

    private final DailyGameRankRepository dgrRepo;
    private final FeatureConfig fConfig;


    private final LoadingCache<GamePlatform, List<Long>> currentRanks;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public GameRankingServiceImpl(LCacheFactory lcf, FeatureConfig fConfig,
                                  DailyGameRankRepository dgrRepo) {
        this.fConfig = fConfig;
        this.dgrRepo = dgrRepo;
        currentRanks = lcf.registerCacheLoader("gameranks", 20, 20, 2 * 60 * 60, (LCacheLoader<GamePlatform, List<Long>>) this::loadCurrentGameRankingsIdsOnly);
    }

    @Transactional(readOnly = true)
    @Override
    public List<GameRankEntry> getGameRankingFor(LocalDate ldt, int limit) {
        if ("live".equalsIgnoreCase(fConfig.getStage())) {
            return dgrRepo.getDailyGameRanks(ldt, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();
        }
        return dgrRepo.getDailyGameRanksTest(ldt, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();
    }

    @Transactional
    @Override
    public List<GameRankEntry> createRankingsFor(LocalDate ldt, int limit) {
        List<GameRankEntry> entries;

        if ("live".equalsIgnoreCase(fConfig.getStage())) {
            entries = dgrRepo.getDailyGameRanks(ldt, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();
        } else {
            entries = dgrRepo.getDailyGameRanksTest(ldt, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();
        }

        // Clean up old (if any)
        dgrRepo.deleteRanksForDate(ldt);

        // Now update in DB
        var collect = entries.stream().map(a -> {
            var dgr = new DailyGameRank();
            dgr.setGameId(a.getGameId());
            dgr.setDate(a.getDate());
            dgr.setSessions(a.getSessions());
            dgr.setSpins(a.getSpins());
            dgr.setSumBet(a.getSumBet());
            dgr.setSumWin(a.getSumWin());
            dgr.setRtp(a.getRtp());
            dgr.setTotalSessionTime(a.getTotalSessionTimeSec());
            return dgr;
        }).toList();
        if (!collect.isEmpty()) {
            dgrRepo.saveAll(collect);
        }
        return entries;
    }

    @Transactional(readOnly = true)
    @Override
    public List<GameRankEntry> getCurrentGameRankings(int shift, int limit) {
        var ldt = LocalDate.now();
        var ldtFrom = ldt.minusDays(LOOKBACK_PERIOD);
        var ldtTo = ldt;
        var currentRanking = dgrRepo.getRankingFrom(ldtFrom, ldtTo, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();

        ldtFrom = ldt.minusDays(LOOKBACK_PERIOD + shift);
        ldtTo = ldt.minusDays(shift);
        var previousRanking = dgrRepo.getRankingFrom(ldtFrom, ldtTo, limit).stream().map(a -> new GameRankEntry(ldt, a)).toList();
        // set previous ranking and current rank
        var rank = 1;
        for (var gre : currentRanking) {
            gre.setRank(rank);
            rank++;
            int pRank = findRank(previousRanking, gre.getGameId());
            if (pRank > 0) {
                gre.setPreviousRank(pRank);
            }
        }
        return currentRanking;
    }

    @Override
    public List<Long> getCurrentGameRankingsId(GamePlatform gp) {
        return currentRanks.get(gp);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> findGameIdsWithMostSpinsActiveOnAll() {
        return dgrRepo.findGameIdsWithMostSpinsActiveOnAll();
    }

    private List<Long> loadCurrentGameRankingsIdsOnly(GamePlatform gp) {
        var ldtTo = LocalDate.now();
        var ldtFrom = ldtTo.minusDays(LOOKBACK_PERIOD);

        List<Long> rank50 = new ArrayList<>();

        switch (gp) {
            case IOS -> rank50.addAll(dgrRepo.getRankingIdsForIos(ldtFrom, ldtTo, 50));
            case ANDROID -> rank50.addAll(dgrRepo.getRankingIdsForAndroid(ldtFrom, ldtTo, 50));
            default -> rank50.addAll(dgrRepo.getRankingIdsForWeb(ldtFrom, ldtTo, 50));
        }
        if (rank50.size() > CURRENT_RANK_SIZE) {
            return new ArrayList<>(rank50.subList(0, CURRENT_RANK_SIZE));
        }
        return rank50;
    }

    private static Integer findRank(List<? extends GameRankEntry> previousRanking, long gameId) {
        var i = 1;
        for (var gre : previousRanking) {
            if (gre.getGameId() == gameId) {
                return i;
            }
            i++;
        }
        return -1;
    }

}
