package com.ously.gamble.services.features;

import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.features.LevelInfo;
import com.ously.gamble.config.LevelConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public final class LevelManagerUtil {

    private static final Logger log = LoggerFactory.getLogger(LevelManagerUtil.class);


    private LevelManagerUtil() {
    }

    public static List<LevelInfo> autogenerateLevels(List<LevelInfo> levels, LevelConfiguration eConfig) {

        if (levels == null || levels.isEmpty() || levels.size() >= eConfig.getAuto().getMaxlevels() || !eConfig.getAuto().isEnabled()) {
            log.info("Scaling levels not applied: {} ", eConfig);
            return levels;
        }

        // base autogen values
        int maxLevels = eConfig.getAuto().getMaxlevels();
        double xpRaise = eConfig.getAuto().getXpincrease();
        double coinRaise = eConfig.getAuto().getCoinincrease();

        List<LevelInfo> nLevels = new ArrayList<>(levels);

        // Initialise state with last level
        var currentL = levels.getLast();
        var currentLp = levels.get(levels.size() - 2);

        int lnum = currentL.getLevel();
        long lcoins = currentL.getAchievementCoins();
        long lcoinsDiff = currentL.getAchievementCoins() - currentLp.getAchievementCoins();
        long start = currentL.getStartXp();
        long end = currentL.getEndXp();

        while (nLevels.size() <= maxLevels) {
            lnum++;

            long lcoinsN = (long) (lcoins + (lcoinsDiff + lcoinsDiff * coinRaise));
            lcoinsDiff = lcoinsN - lcoins;
            lcoins = lcoinsN;

            long cDiff = end - start;
            start = end;
            end = (long) ((start) + cDiff + (cDiff) * xpRaise);

            LevelInfo l = new LevelInfo();
            l.setLevel(lnum);
            l.setAchievementCoins(lcoins);
            l.setStartXp(start);
            l.setEndXp(end);
            l.setStartSpins(0);
            l.setEndSpins(0);
            l.setRewards(createRewards(lnum, eConfig.getAuto()));
            nLevels.add(l);
        }

        if (eConfig.getAuto().isEnabled() && eConfig.getAuto().isConsole()) {
            printLevelInfos(nLevels);
        }

        return nLevels;
    }

    private static void printLevelInfos(List<LevelInfo> nLevels) {
        for (LevelInfo li : nLevels) {
            int diamonds = 0;
            int xpbooster = 0;
            int cnbooster = 0;

            for (Reward r : li.getRewards()) {
                if (r.getType().equals("D")) {
                    diamonds = r.getCount();
                }
                if (r.getType().equals("MX")) {
                    xpbooster = r.getCount();
                }
                if (r.getType().equals("MS")) {
                    cnbooster = r.getCount();
                }
            }

            System.out.println(li.getLevel() + "," + li.getStartXp() + "," + li.getEndXp() + "," + li.getAchievementCoins() +
                    "," + diamonds + "," + xpbooster + "," + cnbooster);
        }
    }

    private static List<Reward> createRewards(int level, LevelConfiguration.AutoLevelConfiguration aConf) {
        List<Reward> rewardsStr = new ArrayList<>();
        getRewardForRemainders(level, rewardsStr, aConf.getCoinboosterRemainder(), "MS#120#15", 0, aConf.isAdditiveRewards());
        getRewardForRemainders(level, rewardsStr, aConf.getXpboosterRemainder(), "MX#120#30", 0, aConf.isAdditiveRewards());
        getRewardForRemainders(level, rewardsStr, aConf.getDiamondsRemainder(), "D", 10, aConf.isAdditiveRewards());
        return rewardsStr;
    }

    private static void getRewardForRemainders(int level, List<Reward> rewards, int[] remainders, String baseDef, int scale,
                                               boolean additive) {
        int cnt = 0;
        for (int remainder : remainders) {
            if (level % remainder == 0) {
                if (scale > 1) {
                    cnt += remainder / 10 * scale;
                } else {
                    cnt++;
                }
                if (!additive) {
                    break;
                }
            }
        }
        if (cnt > 0) {
            rewards.add(new Reward(baseDef + "#" + cnt));
        }
    }

}
