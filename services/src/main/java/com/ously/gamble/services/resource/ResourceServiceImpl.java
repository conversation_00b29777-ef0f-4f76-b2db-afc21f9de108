package com.ously.gamble.services.resource;

import com.ously.gamble.api.resource.ManagedResourceComponent;
import com.ously.gamble.api.resource.ResourceActivatedEvent;
import com.ously.gamble.api.resource.ResourceService;
import com.ously.gamble.persistence.model.resource.*;
import com.ously.gamble.persistence.repository.resource.ResourceDataRepository;
import com.ously.gamble.persistence.repository.resource.ResourceRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

@Service
public class ResourceServiceImpl extends BaseOuslyService implements ResourceService {

    private static final Logger log = LoggerFactory.getLogger(ResourceServiceImpl.class);

    private final ResourceRepository resourceRepo;
    private final ResourceDataRepository resourceDataRepo;
    private final List<ManagedResourceComponent> managedComponents;

    private final RTopic activationTopic;


    public ResourceServiceImpl(ResourceRepository resRepo, ResourceDataRepository resDataRepo,
                               @Lazy @Autowired(required = false) List<ManagedResourceComponent> components,
                               RedissonClient client) {
        this.resourceRepo = resRepo;
        this.resourceDataRepo = resDataRepo;
        this.managedComponents = components;
        this.activationTopic = client.getTopic("resourceActivationTopic", new StringCodec());
        this.activationTopic.addListener(String.class, (channel, message) -> notifyComponents(message));
    }

    @PreDestroy
    public void reapListener() {
        activationTopic.removeAllListeners();
    }

    private void notifyComponents(String message) {
        log.info("Resource activation received for {}", message);
        var evnt = ResourceActivatedEvent.fromString(message);
        managedComponents.stream().filter(a -> a.getComponentName().equals(evnt.component()) && a.getResourceNames().contains(evnt.name())).forEach(a -> a.handleResourceActivation(evnt.component(), evnt.name()));
    }


    @Override
    @Transactional
    public Optional<ResourceDto> createNewResource(String component, String name, String description, String mimetype) {
        var res = new Resource();
        res.setComponent(component);
        res.setName(name);
        res.setDescription(description);
        res.setMimetype(mimetype);
        try {
            res = resourceRepo.saveAndFlush(res);
            return Optional.of(new ResourceDto(res.getComponent(), res.getName(), -1, res.getDescription(), res.getMimetype()));
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public Optional<ResourceDataDto> getActiveResourceData(String component, String name) {
        var activeResourceData =
                resourceDataRepo.getActiveResourceData(component, name);
        return activeResourceData.map(a -> new ResourceDataDto(a.component(), a.name(), a.version(), a.active(), a.blocked(),
                a.createdAt(), a.activatedAt(), a.info(), decompressGzip(a.data())));
    }

    private byte[] decompressGzip(byte[] data) {
        try {
            var gzIn = new GZIPInputStream(new ByteArrayInputStream(data));
            return gzIn.readAllBytes();
        } catch (Exception e) {
            log.error("Error uncompressing data", e);
        }
        return null;
    }

    @Override
    public List<ResourceDto> getAllResources() {
        return resourceRepo.getAllResources();
    }

    @Override
    public List<ResourceDataDto> getAllResourceVersions(String component, String name) {
        return resourceDataRepo.getAllResourceData(component, name);
    }

    @Override
    public Optional<ResourceDataDto> getResourceVersion(String component, String name, int version) {
        var activeResourceData =
                resourceDataRepo.getResourceVersion(component, name, version);
        return activeResourceData.map(a -> new ResourceDataDto(a.component(), a.name(), a.version(), a.active(), a.blocked(),
                a.createdAt(), a.activatedAt(), a.info(), decompressGzip(a.data())));
    }

    @Override
    @Transactional
    public Optional<ResourceDataDto> storeNewResourceData(String component, String name, byte[] data, String versionInfo) {
        var resource = resourceRepo.findById(new ResourceId(component, name));
        if (resource.isEmpty()) {
            return Optional.empty();
        }

        var newVersion = resourceDataRepo.getNextVersionOfResource(component, name);

        var newRD = new ResourceData();
        newRD.setComponent(component);
        newRD.setName(name);
        newRD.setVersion(newVersion);
        newRD.setBlocked(false);
        var cData = compressGzip(data);
        if (cData == null) {
            return Optional.empty();
        }
        newRD.setData(compressGzip(data));
        newRD.setInfo(versionInfo);
        newRD = resourceDataRepo.saveAndFlush(newRD);
        return Optional.of(new ResourceDataDto(newRD.getComponent(), newRD.getName(), newRD.getVersion(), false, newRD.isBlocked(),
                newRD.getCreatedAt(), newRD.getLastActivationAt(), newRD.getInfo(), data));
    }

    private byte[] compressGzip(byte[] data) {
        try {
            var oStr = new ByteArrayOutputStream(data.length / 2);
            var gzOut = new GZIPOutputStream(oStr, 8192);
            gzOut.write(data);
            gzOut.finish();
            return oStr.toByteArray();
        } catch (Exception e) {
            log.warn("Error compressing data", e);
            return null;
        }
    }

    @Override
    @Transactional
    public Optional<ResourceDataDto> activateResourceVersion(String component, String name, int version, long userId) {

        var res = resourceRepo.findById(new ResourceId(component, name));
        if (res.isPresent() && Objects.requireNonNullElse(res.get().getActiveVersion(), -1) != version) {
            var resource = res.get();
            var resData = resourceDataRepo.findById(new ResourceDataId(component, name, version));
            if (resData.isPresent()) {
                var resourceData = resData.get();
                resource.setActiveVersion(version);
                resourceData.setActivatedBy(userId);
                resourceData.setLastActivationAt(Instant.now());
                resourceRepo.saveAndFlush(resource);
                resourceDataRepo.saveAndFlush(resourceData);
                publishEvent(new ResourceActivatedEvent(resource.getComponent(), resource.getName(), resourceData.getVersion()));
                return Optional.of(new ResourceDataDto(component, name, version, true, resourceData.isBlocked(),
                        resourceData.getCreatedAt(), resourceData.getLastActivationAt(), resourceData.getInfo(), null));

            }
        }
        return Optional.empty();
    }

    @Override
    @Transactional
    public ResourceDataDto blockResourceVersion(String component, String name, int version, boolean blocked, long userId,
                                                String reason) {
        return null;
    }

    @Override
    public Optional<ResourceDto> getResource(String component, String name) {
        Optional<Resource> byId = resourceRepo.findById(new ResourceId(component, name));
        return byId.map(a -> new ResourceDto(component, name, Objects.requireNonNullElse(byId.get().getActiveVersion(), -1),
                byId.get().getDescription(), byId.get().getMimetype()));

    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void notifyManagedComponents(ResourceActivatedEvent evnt) {
        activationTopic.publish(evnt.asString());
    }

}
