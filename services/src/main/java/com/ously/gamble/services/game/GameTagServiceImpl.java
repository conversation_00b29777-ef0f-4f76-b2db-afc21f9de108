package com.ously.gamble.services.game;

import com.ously.gamble.api.games.CasinoGameTag;
import com.ously.gamble.api.games.GameTagService;
import com.ously.gamble.persistence.model.game.GameTag;
import com.ously.gamble.persistence.model.game.GameTagAssociation;
import com.ously.gamble.persistence.model.game.GameTagAssociationId;
import com.ously.gamble.persistence.repository.game.GameTagAssociationRepository;
import com.ously.gamble.persistence.repository.game.GameTagRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GameTagServiceImpl implements GameTagService {

    private final GameTagRepository gameTagRepo;
    private final GameTagAssociationRepository gameTagAssocRepo;

    public GameTagServiceImpl(GameTagRepository gtRepo, GameTagAssociationRepository gtaRepo) {
        this.gameTagRepo = gtRepo;
        this.gameTagAssocRepo = gtaRepo;
    }

    @Override
    @Transactional
    public List<GameTag> getAllGameTags() {
        return gameTagRepo.findAll();
    }

    @Override
    @Transactional
    public List<CasinoGameTag> getAllCasinoGametags() {
        return gameTagRepo.findAll().stream().map(CasinoGameTag::new).collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void updateOrSaveGameTag(CasinoGameTag casinoGameTag) {
        var gameTag = gameTagRepo.findById(casinoGameTag.id()).orElse(null);

        if (gameTag == null) {
            gameTag = new GameTag();
            gameTag.setId(casinoGameTag.id());
        }

        gameTag.setDescription(casinoGameTag.description());
        gameTag.setName(casinoGameTag.name());
        gameTag.setType(casinoGameTag.type());
        gameTag.setScope(casinoGameTag.scope());

        gameTagRepo.saveAndFlush(gameTag);
    }


    @Override
    @Transactional
    public Set<Integer> getAllCasinoGametagsForGame(long gId) {
        return gameTagAssocRepo.findAllAssociationsByGameId(gId).stream().map(a -> a.getId().getTag_id()).collect(Collectors.toSet());
    }

    @Override
    @Transactional
    public Set<Integer> updateTagsForGame(final long gameId, Set<Integer> newTags2) {
        Set<Integer> newTags = Objects.requireNonNullElseGet(newTags2, Collections::emptySet);
        var currentTags = gameTagAssocRepo.findAllTagIdsForGameId(gameId);
        if (currentTags.equals(newTags)) {
            return newTags;
        }
        var tagsToRemove = new HashSet<>(currentTags);
        tagsToRemove.removeAll(newTags);
        gameTagAssocRepo.deleteAllById(tagsToRemove.stream().map(a -> new GameTagAssociationId((int) gameId, a)).collect(Collectors.toList()));
        var tagsToAdd = new HashSet<>(newTags);
        tagsToAdd.removeAll(currentTags);
        gameTagAssocRepo.saveAll(
                tagsToAdd.stream().map(a -> {
                    var gta = new GameTagAssociation();
                    gta.setId(new GameTagAssociationId((int) gameId, a));
                    return gta;
                }).toList());
        return newTags;
    }

    @Override
    public boolean hasGameTagId(long gameId, String tagName) {
        return gameTagRepo.hasGameATagWithName(gameId, tagName) > 0;
    }

}
