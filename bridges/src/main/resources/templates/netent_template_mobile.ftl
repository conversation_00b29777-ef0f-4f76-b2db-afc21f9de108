<html>
<head>
    <title>Netent</title>
    <script type="text/javascript"
            src="https://netent-static.casinomodule.com/gameinclusion/library/gameinclusion.js"></script>
    <script type="text/javascript">
        var startGame = function () {
            var config = {
                gameId: "${gameId}",
                launchType: "iframeredirect",
                staticServerURL: "https://netent-static.casinomodule.com/",
                gameServerURL: "${gameServerUrl}",
                sessionId: "${jwt}",
                targetElement: "gameArea",
                disableDeviceDetection: true
            };

            // Game launch successful.
            var success = function (netEntExtend) {

            };
            // Error handling here.
            var error = function (e) {
            };

            netent.launch(config, success, error);
        };

        window.onload = startGame;

    </script>
</head>
<body>
<div id="gameArea"></div>
</body>

</html>

