package com.ously.gamble.bridge;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.TestContext;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.ActiveGameManagementService;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.bridge.spinomenal.SpinomenalConfiguration;
import com.ously.gamble.bridge.spinomenal.SpinomenalService;
import com.ously.gamble.bridge.spinomenal.SpinomenalUtils;
import com.ously.gamble.bridge.spinomenal.payload.AuthenticationRequest;
import com.ously.gamble.bridge.spinomenal.payload.PlayerBalanceRequest;
import com.ously.gamble.bridge.spinomenal.payload.ProcessBetRequest;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Session;
import com.ously.gamble.persistence.repository.session.SessionRepository;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import static com.ously.gamble.test.TestFixtures.randomBigDecimal;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.DOWN;
import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(SpringExtension.class)
@Transactional
public class SpinoTokenGenerationTests extends TestContext {

    public static final String EXT_USER_ID = "UID:1";
    public static final long USER_ID = 1L;
    @Autowired
    SpinomenalService spinCtrl;

    @Autowired
    SpinomenalConfiguration spinoSec;

    @Autowired
    ObjectMapper om;

    @Autowired
    GameManagementService gmMgmt;

    @Autowired
    UserManagementService uMgr;

    @Autowired
    ActiveGameManagementService giRepo;

    @Test
    public void testTokenWorkflow() throws Exception {
        var game = gmMgmt.getByIdUncached(USER_ID);
        var user = uMgr.getByIdUncached(USER_ID);
        var oW = getWallet(USER_ID);
        var initialBalance = oW.getBalance();
        var gi = spinCtrl.createNewGameInstanceRaw(new CasinoUser(user), new CasinoGame(game, null), GamePlatform.TEST, new GameSettings());
        gi.setUserId(user.getId());
        gi.setGameId(game.getId());
        giRepo.save(gi);
        assertNotNull(gi);

        createSessionFromGameInstance(gi);

        var token = gi.getToken();

        // ok, now we send authentication
        var arq = new AuthenticationRequest();
        arq.setGameToken(token);
        arq.setGameCode(game.getGameId());
        arq.setReqTime(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        arq.setSig(spinoSec.createSignature(arq.getSigPart()));
        var arp = spinCtrl.authenticate(arq);
        assertEquals(0, arp.getErrorCode().longValue());

        // ok, now we send getBalanceReq
        var pbr = new PlayerBalanceRequest();
        pbr.setExtId(EXT_USER_ID);
        pbr.setGameToken(gi.getToken());
        pbr.setReqTime(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        pbr.setSig(spinoSec.createSignature(pbr.getSigPart()));

        var pba = spinCtrl.getPlayerBalance(pbr);
        assertNotNull(pba);
        var cVal = pba.getBalance().longValue();
        assertTrue(cVal >= 10000);

        // now we need incoming process bet
        var betAmount = randomBigDecimal().setScale(2, DOWN);
        var response = spinCtrl.processBet(betRequest(token, betAmount));
        assertEquals(response.getBalanceAfter(), initialBalance.subtract(betAmount));

        // now we process a win
        var winAmount = randomBigDecimal().setScale(2, DOWN);
        response = spinCtrl.processBet(winRequest(token, winAmount));
        var netProfit = winAmount.subtract(betAmount);
        assertEquals(response.getBalanceAfter(), initialBalance.add(netProfit));

        // now we check the wallet to see if everything is reflected
        var uW = getWallet(USER_ID);
        assertEquals(1L, uW.getGamesPlayed());
        assertEquals(1L, uW.getGamesWon());
        assertEquals(winAmount, uW.getSumWin());
        assertEquals(betAmount, uW.getSumBet());
        assertEquals(uW.getBalance(), initialBalance.add(netProfit));
    }

    private @NotNull ProcessBetRequest winRequest(String token, BigDecimal winAmount) {
        return processBetRequest(token, EXT_USER_ID, "WIN", ZERO, winAmount);
    }

    private @NotNull ProcessBetRequest betRequest(String token, BigDecimal betAmount) {
        return processBetRequest(token, EXT_USER_ID, "BET", betAmount, ZERO);
    }

    final AtomicLong requestIdCounter = new AtomicLong(1L);

    private ProcessBetRequest processBetRequest(String token, String extUserId, String trType,
                                                BigDecimal betAmount, BigDecimal winAmount) {
        var betReq = new ProcessBetRequest();
        betReq.setBetAmount(betAmount);
        betReq.setExternalId(extUserId);
        betReq.setGameToken(token);
        betReq.setGameCode("SPINO_BOOKOFAAH");
        betReq.setRefTicketId("TESTBET_START_01");
        betReq.setRoundFinish("WIN".equals(trType));
        betReq.setRoundId((long) 1);
        betReq.setTicketId(requestIdCounter.getAndIncrement());
        betReq.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        betReq.setTransactionDescription("withdraw of 1st bet");
        betReq.setTransactionType(trType);
        betReq.setWinAmount(winAmount);
        betReq.setSig(spinoSec.createSignature(betReq.getSigPart()));
        betReq.setProviderCode(spinoSec.getVendorName());
        return betReq;
    }

    @Autowired
    SessionRepository ssRepo;

    private void createSessionFromGameInstance(GameInstance gI) {
        // Create session
        var s = new Session();
        s.setUserId(gI.getUserId());
        s.setGameId(gI.getGameId());
        s.setCountry(gI.getCountry());
        s.setSessionId(gI.getId());
        s.setCreationTime(gI.getCreationTime().toInstant(ZoneOffset.UTC));
        s.setHandlerVersion(gI.getHandlerVersion());
        s.setJurisdiction(gI.getJurisdiction());
        s.setPlatform(Objects.requireNonNullElse(gI.getPlatform(), GamePlatform.UNKNOWN));
        s.setStatus(SessionState.ACTIVE);
        s.setToken(gI.getToken());
        s.setCountry("DE");
        s = ssRepo.saveAndFlush(s);
        assertNotNull(s);
    }
}
