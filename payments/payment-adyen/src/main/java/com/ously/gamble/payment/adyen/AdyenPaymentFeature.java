package com.ously.gamble.payment.adyen;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.stereotype.Service;

@Service
public class AdyenPaymentFeature extends AbstractPlatformFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Adyen Web payment feature", "Adyen web payment integration - Social only");
    }
}
