package com.ously.gamble.payment.coinspaid.service;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.payment.coinspaid.api.CoinspaidRestService;
import com.ously.gamble.payment.coinspaid.api.CoinspaidService;
import com.ously.gamble.payment.coinspaid.api.restapi.CPPCurrencyType;
import com.ously.gamble.payment.coinspaid.api.restapi.CPPGetCurrencyPairsResponse;
import com.ously.gamble.payment.coinspaid.api.restapi.CPPGetExchangeRateRequest;
import com.ously.gamble.payment.coinspaid.config.CoinspaidConfig;
import com.ously.gamble.payment.coinspaid.persistence.model.UserAddress;
import com.ously.gamble.payment.coinspaid.persistence.repository.UserAddressRepository;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.deposit.DepositAddress;
import com.ously.gamble.payment.payload.deposit.DepositCondition;
import com.ously.gamble.payment.payload.deposit.DepositMethod;
import com.ously.gamble.payment.payload.payout.PayoutCondition;
import com.ously.gamble.payment.payload.payout.PayoutDetails;
import com.ously.gamble.payment.payload.payout.PayoutMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ously.gamble.payment.payload.PMCurrency.USDTE;


@Service
@ConditionalOnBean(CoinspaidConfig.class)
public class CoinspaidServiceImpl implements CoinspaidService {

    private final Logger log = LoggerFactory.getLogger(CoinspaidServiceImpl.class);

    private final BigDecimal MAX_DEPOSIT_IN_USD;
    private final CoinspaidRestService cpRestService;
    private final UserAddressRepository uaRepo;
    private final LoadingCache<PMCurrency, CPPGetCurrencyPairsResponse> depositPairCache;
    private final LoadingCache<PMCurrency, CPPGetCurrencyPairsResponse> payoutPairCache;
    private final LoadingCache<PMCurrency, Collection<DepositMethod>> depositMethodCache;


    public CoinspaidServiceImpl(
            CoinspaidConfig config,
            LCacheFactory<PMCurrency, CPPGetCurrencyPairsResponse> lcFactory,
            LCacheFactory<PMCurrency, Collection<DepositMethod>> lcFactory2,
            CoinspaidRestService cpRSrv,
            UserAddressRepository uaRepo
    ) {
        this.MAX_DEPOSIT_IN_USD = config.getMaxdepositinusd();
        this.cpRestService = cpRSrv;
        this.uaRepo = uaRepo;

        this.depositPairCache = lcFactory.registerCacheLoader("cpDepositPairs", 50, 5, 30,
                currency -> cpRestService.getAvailableCurrencies(null,
                        currency.name()).orElseGet(() -> new CPPGetCurrencyPairsResponse(Collections.emptyList())));

        this.payoutPairCache = lcFactory.registerCacheLoader("cpPayoutPairs", 50, 5, 15,
                currency -> cpRestService.getAvailableCurrencies(currency.name(), null).orElseGet(() -> new CPPGetCurrencyPairsResponse(Collections.emptyList())));

        this.depositMethodCache = lcFactory2.registerCacheLoader("cpPaymentMethods", 50, 5, 60,
                this::getMethodsForTargetCurrencyInternal);
    }

    @Override
    public Collection<DepositMethod> getMethodsForTargetCurrency(PMCurrency usdte) {
        return depositMethodCache.get(usdte);
    }

    @Override
    public Optional<BigDecimal> getDepositExchangeRate(String currency) {
        var first = getMethodsForTargetCurrency(USDTE).stream().filter(a -> a.getCurrency().equals(currency)).findFirst();
        if (first.isEmpty()) {
            return Optional.empty();
        }
        var poMethod = first.get();

        var exRtReq =
                new CPPGetExchangeRateRequest(null
                        , currency, "30",
                        "USDTE");
        var exchangeRate = cpRestService.getExchangeRate(exRtReq);
        if (exchangeRate.isEmpty()) {
            return Optional.empty();
        }

        var senderAmount = new BigDecimal(30);
        var receiverAmount = new BigDecimal(exchangeRate.get().data().senderAmount());
        var cRate = receiverAmount.divide(senderAmount, 8, RoundingMode.HALF_UP);

        return Optional.of(cRate);
    }

    @Override
    @Transactional
    public Optional<DepositAddress> getDepositAddressFor(long userId, String currency) {
        var optAddress = uaRepo.findUserAddressByUserIdAndFromCurrency(userId, currency);
        if (optAddress.isEmpty()) {
            optAddress = createNewDepositAddress(userId, currency);
        }
        var ua = optAddress.get();
        return Optional.of(new DepositAddress(ua.getAddress(), ua.getTag(), ua.getFromCurrency()));
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserAddress> getUserDepositAddresses(long userId) {
        return uaRepo.findUserAddressesByUserId(userId);
    }

    @Override
    @Transactional
    public Optional<PayoutDetails> getPayoutDetailsFor(Long userId, String currency) {

        if ("USDTE".equals(currency)) {
            var poDt = new PayoutDetails(
                    CoinspaidConfig.HANDLERNAME + '-' + "USDTE",
                    BigDecimal.valueOf(5),
                    BigDecimal.ONE, Instant.now(),
                    Instant.now().plus(24, ChronoUnit.HOURS),
                    CoinspaidConfig.HANDLERNAME);
            return Optional.of(poDt);
        }


        var first = getMethodsForPayout(USDTE).stream().filter(a -> a.getCurrency().equals(currency)).findFirst();
        if (first.isEmpty()) {
            return Optional.empty();
        }
        var poMethod = first.get();

        var exRtReq =
                new CPPGetExchangeRateRequest(poMethod.getConditions().minPayoutAmount().toString()
                        , "USDTE", null,
                        currency);
        log.info("Getting rate for Request:{}", exRtReq);
        var exchangeRate = cpRestService.getExchangeRate(exRtReq);
        if (exchangeRate.isEmpty()) {
            return Optional.empty();
        }

        var tsFixed = exchangeRate.get().data().tsFixed();
        var tsRelease = exchangeRate.get().data().tsRelease();
        var senderAmount = new BigDecimal(exchangeRate.get().data().senderAmount());
        var receiverAmount = new BigDecimal(exchangeRate.get().data().receiverAmount());
        var cRate = receiverAmount.divide(senderAmount, 8, RoundingMode.HALF_UP);

        var poDt = new PayoutDetails(
                poMethod.getName(),
                poMethod.getConditions().minPayoutAmount(),
                cRate, Instant.ofEpochSecond(tsFixed), Instant.ofEpochSecond(tsRelease),
                CoinspaidConfig.HANDLERNAME);
        return Optional.of(poDt);

    }

    @Override
    public Collection<PayoutMethod> getMethodsForPayout(PMCurrency srcCurrency) {
        // we need to find all methods which allow payouts for srcCurrency (exchange) and amount.
        // if amount == ZERO, then no limits apply

        var cppGetCurrencyPairsResponse = payoutPairCache.get(srcCurrency);
        var collect = cppGetCurrencyPairsResponse.data().stream().filter(a -> a.to().type() == CPPCurrencyType.crypto).map(
                cpp -> {
                    var pc =
                            new PayoutCondition(new BigDecimal(cpp.from().minAmount()));
                    return new PayoutMethod(CoinspaidConfig.HANDLERNAME + '-' + cpp.to().currency(), PMCurrency.valueOf(cpp.to().currency()), pc);
                }
        ).collect(Collectors.toList());

        // add usdte / direct transfer without exch.
        collect.add(new PayoutMethod(CoinspaidConfig.HANDLERNAME + '-' + srcCurrency.name(),
                srcCurrency, new PayoutCondition(BigDecimal.ONE)));

        return collect;
    }

    private Optional<UserAddress> createNewDepositAddress(long userId, String currency) {

        var pmCurrency = PMCurrency.valueOf(currency);
        var toCurrency = pmCurrency.isStable() ? null : USDTE;


        // Check if combination is available
        if (toCurrency != null) {
            if (depositPairCache.get(toCurrency).data().stream().noneMatch(a -> a.from().currency().equals(pmCurrency.name()))) {
                return Optional.empty();
            }
        } else {
            if (pmCurrency != USDTE) {
                return Optional.empty();
            }
        }
        var depositAddress = cpRestService.getDepositAddress(
                "cpuid:" + userId, currency, (toCurrency == null) ? null : toCurrency.name());
        if (depositAddress.isEmpty()) {
            return Optional.empty();
        }
        var cpAddress = depositAddress.get();
        var ua = new UserAddress();
        ua.setUserId(userId);
        ua.setCpId(cpAddress.data().id());
        ua.setAddress(cpAddress.data().address());
        ua.setCreatedAt(Instant.now());
        ua.setTag(Objects.requireNonNullElse(cpAddress.data().tag(), ""));
        ua.setFromCurrency(cpAddress.data().currency());
        ua.setToCurrency((toCurrency == null) ? cpAddress.data().currency() : toCurrency.name());
        ua = uaRepo.save(ua);
        return Optional.of(ua);
    }


    private Collection<DepositMethod> getMethodsForTargetCurrencyInternal(PMCurrency usdte) {
        try {
            var collect =
                    depositPairCache.get(usdte).data().stream().filter(cPair -> cPair.from().type() == CPPCurrencyType.crypto).map(cPair -> {
                        try {
                            return new DepositMethod(CoinspaidConfig.HANDLERNAME + '-' + cPair.from().currency(),
                                    PMCurrency.valueOf(cPair.from().currency()),
                                    new DepositCondition(new BigDecimal(cPair.from().minAmountDepWithExch()),
                                            MAX_DEPOSIT_IN_USD.divide(new BigDecimal(cPair.rateTo()), 8,
                                                    RoundingMode.DOWN).multiply(new BigDecimal(cPair.rateFrom()))));
                        } catch (Exception e) {
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList());

            collect.add(new DepositMethod(CoinspaidConfig.HANDLERNAME + '-' + usdte.name(), usdte,
                    new DepositCondition(BigDecimal.ONE, MAX_DEPOSIT_IN_USD)));

            return collect;
        } catch (Exception e) {
            log.error("Error trying to compile PaymentMethods:", e);
            return Collections.emptyList();
        }
    }
}
