package com.ously.gamble.payment.coinspaid.payload.notification;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.payment.coinspaid.api.notification.CPNotification;
import com.ously.gamble.payment.coinspaid.api.notification.CPNotificationStatus;
import com.ously.gamble.payment.coinspaid.api.notification.CPNotificationTransactionType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class NotificationSerDeTests {

    private final ObjectMapper om = new ObjectMapper();


    @Test
    public void testNotificationSerDeDepositUSDTE() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                        "/deposit_USDTE_directly.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(2, cpNotification.fees().size());
        assertEquals(1, cpNotification.transactions().size());
        assertEquals("USDTE", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.blockchain,
                cpNotification.transactions().getFirst().transactionType());
        assertEquals("USDTE", cpNotification.transactions().getFirst().currency());

    }


    @Test
    public void testNotificationSerDeDeposit1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                        "/deposit_exchange_xrpl_confirmed.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(1, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("XRP", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.exchange,
                cpNotification.transactions().get(1).transactionType());
        assertEquals("USDTE", cpNotification.transactions().get(1).currencyTo());

    }


    @Test
    public void testNotificationETHDeposit1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                                                                      "/newDeposit.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(2, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("ETH", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.exchange,
                cpNotification.transactions().get(1).transactionType());
        assertEquals("USDTE", cpNotification.transactions().get(1).currencyTo());

    }

    @Test
    public void testNotificationSerDeWithdraw1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                                                                      "/withdraw_exchange_eth_confirmed.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(2, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("USDTE", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.exchange,
                cpNotification.transactions().get(1).transactionType());
        assertEquals("ETH", cpNotification.transactions().get(1).currencyTo());

    }

    @Test
    public void testNotificationSerDeExchange1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications/exchange_USDTE_BTC_notification.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(1, cpNotification.fees().size());
        assertEquals(1, cpNotification.transactions().size());
        assertEquals("USDTE", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.exchange,
                cpNotification.transactions().getFirst().transactionType());
        assertEquals("BTC", cpNotification.transactions().getFirst().currencyTo());

    }


    @Test
    public void testNotificationSerDeWithdrawalProblem1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                                                                      "/withdrawal_exchange_problem_callback_1.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(1, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("USDTE", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.internal,
                cpNotification.transactions().getFirst().transactionType());
        assertNull(cpNotification.transactions().getFirst().currencyTo());

    }

    @Test
    public void testNotificationSerDeDepositProblem1() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                                                                      "/deposit_exchange_problem_callback_1.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.confirmed, cpNotification.status());
        assertEquals(1, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("ETH", cpNotification.currencySent().currency());
        assertEquals(CPNotificationTransactionType.internal,
                cpNotification.transactions().getFirst().transactionType());
        assertNull(cpNotification.transactions().getFirst().currencyTo());

    }


    @Test
    void testNotificationWithdrawNotEnoughFundsRpl() throws Exception {
        var cpNotification = om.readValue(this.getClass().getResource("/notifications" +
                                                                      "/withdraw_exchange_notenoughfunds_ripple.json"),
                CPNotification.class);

        assertEquals(CPNotificationStatus.cancelled, cpNotification.status());
        assertEquals(0, cpNotification.fees().size());
        assertEquals(2, cpNotification.transactions().size());
        assertEquals("Not enough money on balance", cpNotification.error());
        assertEquals("549:9273e82d12ce090db7b9b97a26f462c8", cpNotification.foreignId());
        assertEquals(CPNotificationTransactionType.blockchain,
                cpNotification.transactions().getFirst().transactionType());
        assertNull(cpNotification.transactions().getFirst().currencyTo());

    }


}
