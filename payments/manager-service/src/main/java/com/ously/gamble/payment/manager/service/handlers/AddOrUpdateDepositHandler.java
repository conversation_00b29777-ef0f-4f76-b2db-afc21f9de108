package com.ously.gamble.payment.manager.service.handlers;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.crm.CRMUserUpdateRequest;
import com.ously.gamble.api.events.UserTagEvent;
import com.ously.gamble.api.freespins.DepositEvent;
import com.ously.gamble.api.user.EUserTag;
import com.ously.gamble.api.user.UserMessageEvent;
import com.ously.gamble.api.user.UserTransactionService;
import com.ously.gamble.api.user.UserTxRequest;
import com.ously.gamble.payment.api.PaymentManager;
import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.api.actions.manager.AddOrUpdateDepositAction;
import com.ously.gamble.payment.api.handler.info.ActionStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import com.ously.gamble.payment.manager.config.PaymentManagerConfig;
import com.ously.gamble.payment.manager.configprops.PaymentLimitsConfig;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentDepositId;
import com.ously.gamble.payment.manager.persistence.model.PaymentDeposit;
import com.ously.gamble.payment.manager.persistence.model.PaymentDepositStep;
import com.ously.gamble.payment.manager.persistence.repository.PaymentDepositRepository;
import com.ously.gamble.payment.manager.persistence.repository.PaymentDepositStepRepository;
import com.ously.gamble.payment.manager.service.ManagerActionHandler;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.actions.DepositUpdate;
import com.ously.gamble.payment.payload.actions.PaymentActionStatus;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@ConditionalOnBean(PaymentManagerConfig.class)
public class AddOrUpdateDepositHandler implements ManagerActionHandler<AddOrUpdateDepositAction> {

    private final Logger log = LoggerFactory.getLogger(AddOrUpdateDepositHandler.class);

    private final PaymentDepositRepository depositRepo;
    private final PaymentDepositStepRepository depositStepRepo;
    private final UserTransactionService userTxService;
    private final ApplicationEventPublisher eventPublisher;
    private final PaymentLimitsConfig pLimitsConfig;

    public AddOrUpdateDepositHandler(PaymentDepositRepository pdRepo,
                                     PaymentDepositStepRepository pdsRepo,
                                     UserTransactionService uTxService,
                                     ApplicationEventPublisher eventPublisher,
                                     PaymentLimitsConfig pLimitsConfig) {
        this.depositRepo = pdRepo;
        this.depositStepRepo = pdsRepo;
        this.userTxService = uTxService;
        this.eventPublisher = eventPublisher;
        this.pLimitsConfig = pLimitsConfig;
    }


    @Override
    public void performManagerAction(AddOrUpdateDepositAction action, PaymentContext ctx,
                                     PaymentManager pMgr) {
        log.info("PaymentManager, performing action AddOrUpdateDeposit: {}", action.getUpdate());

        // do we have the deposit already ?

        var optDeposit =
                depositRepo.findById(new PaymentDepositId(action.getContext().getUserId(),
                        action.getContext().getPaymentReference()));

        if (optDeposit.isEmpty()) {
            var deposit = new PaymentDeposit();
            deposit.setUserId(action.getContext().getUserId());
            deposit.setDepositId(action.getContext().getPaymentReference());
            deposit.setHandler(action.getContext().getHandler());
            deposit.setVersion(1);
            deposit.setStatus(PaymentStatus.CREATED);
            deposit.setAmount(getAmount(action.getUpdate()));
            deposit.setFee(getFee(action.getUpdate()));
            deposit.setConversionRate(getRate(action.getUpdate()));
            deposit.setCurrency(getCurrency(action.getUpdate()));
            deposit.setCurrencyTo(getCurrencyTo(action.getUpdate()));
            deposit.setCreatedAt(Instant.now());
            deposit.setUpdatedAt(Instant.now());
            optDeposit = Optional.of(depositRepo.saveAndFlush(deposit));
            ctx.addLogEntry(action, ActionStatus.INFO, "Added new deposit '{}' for user {}",
                    optDeposit.get().getDepositId(), optDeposit.get().getUserId());


        }
        // Add step
        var dpStep = new PaymentDepositStep();
        dpStep.setCreatedAt(Instant.now());
        dpStep.setDepositId(action.getContext().getPaymentReference());
        dpStep.setUserId(action.getContext().getUserId());
        dpStep.setSeq(depositStepRepo.getNextSeqForStep(action.getContext().getUserId(),
                action.getContext().getPaymentReference()));
        dpStep.setStatusBefore(optDeposit.get().getStatus());
        dpStep.setStatusAfter(getDepositStatus(action.getUpdate().status()));
        dpStep.setNotificationId(action.getUpdate().notificationMd5());


        // Resolve internal action
        if (ctx.getCurrentStatus() != ActionStatus.ERROR) {
            if (optDeposit.get().getInternalTransactionId() == null && dpStep.getStatusBefore() != PaymentStatus.SUCCESS && dpStep.getStatusAfter() == PaymentStatus.SUCCESS) {
                // book transaction
                var uTxReq = new UserTxRequest();
                uTxReq.setCredit(optDeposit.get().getAmount());
                // Add deposit wager amount
                uTxReq.setAddWager(uTxReq.getCredit().multiply(BigDecimal.valueOf(pLimitsConfig.getDepositWagerFactor())));
                uTxReq.setUserId(optDeposit.get().getUserId());
                uTxReq.setTxRef(optDeposit.get().getDepositId());
                uTxReq.setType(UserTransactionType.DEPOSIT);
                uTxReq.setDescription("DEPOSIT:" + action.getContext().getHandler() + ':' + action.getUpdate().notificationMd5());
                try {
                    var userTxResponse = userTxService.addTransaction(uTxReq);
                    optDeposit.get().setInternalTransactionId(userTxResponse.getTxId());
                    optDeposit.get().setStatus(PaymentStatus.SUCCESS);
                    depositRepo.saveAndFlush(optDeposit.get());
                    log.info("Booked deposit userTX:{}", action);
                    ctx.addLogEntry(action, ActionStatus.OK, "Added transaction for deposit ‘{}‘ with " +
                                    "amount '{}'",
                            optDeposit.get().getDepositId(), optDeposit.get().getAmount());
                    dpStep.setInfo(ctx);
                    depositStepRepo.saveAndFlush(dpStep);
                    eventPublisher.publishEvent(new DepositEvent(action.getContext().getPaymentReference(),
                            optDeposit.get().getUserId(),
                            optDeposit.get().getAmount(),
                            optDeposit.get().getDepositId(),
                            userTxResponse.getTxId()
                    ));
                    eventPublisher.publishEvent(new UserTagEvent(optDeposit.get().getUserId(), EUserTag.DEPOSITOR.name(), true));
                    eventPublisher.publishEvent(new CRMUserEvent(optDeposit.get().getUserId(), "MONEY_DEPOSIT", "amount", optDeposit.get().getAmount(), "currency", optDeposit.get().getCurrency(), "payment_agent", "CP_AP", "deposit_id", optDeposit.get().getId().getDepositId()));
                    eventPublisher.publishEvent(new CRMUserUpdateRequest(optDeposit.get().getUserId()));
                } catch (OuslyTransactionException e) {
                    // TODO: Support Intervention !!
                    log.error("Error booking deposit userTX", e);
                    ctx.addLogEntry(action, ActionStatus.ERROR, "Error adding transaction for deposit ‘{}‘" +
                                    " with " +
                                    "amount '{}'",
                            optDeposit.get().getDepositId(), optDeposit.get().getAmount());
                    dpStep.setInfo(ctx);
                    optDeposit.get().setStatus(PaymentStatus.INTERNAL_ERROR);
                    depositStepRepo.saveAndFlush(dpStep);
                }
            }
        } else {
            dpStep.setInfo(ctx);
            depositStepRepo.saveAndFlush(dpStep);
            optDeposit.get().setStatus(PaymentStatus.SUPPORT);
        }
        sendDepositStatusMessage(optDeposit.get().getUserId(), ctx.getCurrentStatus(), optDeposit.get());

    }

    @Override
    public void performManagerAction(AddOrUpdateDepositAction action, PaymentContext ctx) {
        this.performManagerAction(action, ctx, null);
    }

    private static BigDecimal getRate(DepositUpdate update) {
        if (update.amount() != null && update.amountTo() != null) {
            return update.amount().divide(update.amountTo(), 8, RoundingMode.FLOOR);
        }
        return BigDecimal.ONE;

    }

    private static String getCurrency(DepositUpdate update) {
        return update.currency().name();
    }

    private static String getCurrencyTo(DepositUpdate update) {
        return update.currencyTo().name();
    }


    private static BigDecimal getFee(DepositUpdate update) {

        var theFee = BigDecimal.ZERO;
        var currencyTo = getCurrencyTo(update);
        var rate = getRate(update);
        for (var fee : update.fees()) {
            if (!fee.feeCurrency().equals(currencyTo)) {
                theFee = theFee.add(fee.feeAmount().divide(rate, 8, RoundingMode.FLOOR));
            } else {
                theFee = theFee.add(fee.feeAmount());
            }
        }
        return theFee;
    }

    private static BigDecimal getAmount(DepositUpdate update) {
        // converted to USD(T/TE)
        if (update.currencyTo().isStable() && update.currencyTo().getStableCurrency() == PMCurrency.USD) {
            return update.amountTo();
        }
        // direct deposit of USD(T/TE)
        if (update.currency().isStable() && update.currency().getStableCurrency() == PMCurrency.USD) {
            return update.amount();
        }
        // no stable currency for this deposit. We need to convert acc. to current rate
        throw new IllegalArgumentException("no stable currency for this deposit. We need to " +
                "convert '" + update.currency() + "' to current rate");
    }

    private static PaymentStatus getDepositStatus(
            PaymentActionStatus status) {
        return switch (status) {
            case SUCCESS -> PaymentStatus.SUCCESS;
            case CANCEL -> PaymentStatus.CANCEL_REQUIRED;
            case ERROR -> PaymentStatus.FAILED;
            case PENDING -> PaymentStatus.PENDING;
        };
    }

    @Override
    public Class getActionClass() {
        return AddOrUpdateDepositAction.class;
    }


    private void sendDepositStatusMessage(Long userId, ActionStatus currentStatus,
                                          PaymentDeposit pmDeposit) {
        Map<String, String> vars = new HashMap<>(6);
        vars.put("amountDeposited", pmDeposit.getAmount().round(new MathContext(8, RoundingMode.HALF_UP)).toPlainString());
        vars.put("depositStatus", currentStatus.name());
        vars.put("receivedCoinAmount",
                pmDeposit.getAmount().multiply(pmDeposit.getConversionRate()).round(new MathContext(8, RoundingMode.HALF_UP)).toPlainString());
        vars.put("receivedCoinCurrency", pmDeposit.getCurrency());

        var cnt = new UserMessageContent(getMsgTypeForDeposit(pmDeposit), vars,
                Collections.emptyMap());
        eventPublisher.publishEvent(new UserMessageEvent(userId, true, pmDeposit.getDepositId() + ':' + currentStatus
                , cnt));
    }

    private static UserMessageType getMsgTypeForDeposit(PaymentDeposit pmDeposit) {
        return switch (pmDeposit.getStatus()) {
            case SUCCESS -> UserMessageType.DEPOSIT_RECEIVED;
            case PENDING -> UserMessageType.IGNORE;
            default -> UserMessageType.DEPOSIT_PROBLEM;
        };
    }

}
