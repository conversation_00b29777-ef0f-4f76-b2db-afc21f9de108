package com.ously.gamble.payment.manager.persistence.idclasses;


import java.io.Serializable;
import java.util.Objects;

public class PaymentDepositStepId implements Serializable {
    Long userId;
    String depositId;
    Integer seq;

    public PaymentDepositStepId() {
    }

    public PaymentDepositStepId(Long userId, String paymentId, Integer seq) {
        this.userId = userId;
        this.depositId = paymentId;
        this.seq = seq;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDepositId() {
        return depositId;
    }

    public void setDepositId(String depositId) {
        this.depositId = depositId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (PaymentDepositStepId) o;

        if (!Objects.equals(userId, that.userId)) {
            return false;
        }
        return Objects.equals(depositId, that.depositId) && Objects.equals(seq, that.seq);
    }

    @Override
    public int hashCode() {
        var result = userId != null ? userId.hashCode() : 0;
        result = 31 * result + (depositId != null ? depositId.hashCode() : 0);
        result = 31 * result + (seq != null ? seq.hashCode() : 0);
        return result;
    }
}
