package com.ously.gamble.payment.api.actions;

import com.ously.gamble.payment.payload.PMContext;

public abstract class AbstractAction implements HandlerAction {
    private ActionType type;
    private PMContext context;

    protected AbstractAction(ActionType type, PMContext context) {
        this.type = type;
        this.context = context;
    }

    protected AbstractAction(ActionType type) {
        this(type, null);
    }

    @Override
    public ActionType getType() {
        return type;
    }

    @Override
    public PMContext getContext() {
        return context;
    }

    public void setContext(PMContext context) {
        this.context = context;
    }

    public void setType(ActionType type) {
        this.type = type;
    }


    @Override
    public String toString() {
        return "AbstractAction{" +
                "type=" + type +
                ", context=" + context +
                '}';
    }
}
