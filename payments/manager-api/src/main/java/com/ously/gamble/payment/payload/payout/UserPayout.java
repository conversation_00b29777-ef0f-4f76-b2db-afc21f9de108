package com.ously.gamble.payment.payload.payout;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.ously.gamble.payment.payload.PMCurrency;

import java.math.BigDecimal;
import java.time.Instant;

public record UserPayout(
        String payoutId,
        Long userId,
        String status,
        PMCurrency srcCurrency,
    BigDecimal srcAmount,
        PMCurrency targetCurrency,
    BigDecimal targetAmount,
    BigDecimal transactionFee,
    BigDecimal operatorGrant,
    @JsonSerialize(using = InstantSerializer.class)
    Instant    createdAt,
    @JsonSerialize(using = InstantSerializer.class)
    Instant    confirmedAt,
    String     srcAddress,
    String     targetAddress,
    String     tag
)
    {}
