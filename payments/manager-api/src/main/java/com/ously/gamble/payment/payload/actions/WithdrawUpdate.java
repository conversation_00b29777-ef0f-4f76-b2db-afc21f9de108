package com.ously.gamble.payment.payload.actions;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.ously.gamble.payment.payload.PMCurrency;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

public record WithdrawUpdate(
        PMCurrency currency,
        BigDecimal amount,
        PMCurrency currencyTo,
        BigDecimal amountTo,
        BigDecimal totalFees,
        PaymentActionStatus status,
        @JsonSerialize(using = InstantSerializer.class)
        Instant updatedAt,
        String notificationMd5,
        List<PaymentFee> fees,
        List<PaymentTransaction> transactions
){

        @Override
        public String toString() {
                return "WithdrawUpdate{" +
                       "currency=" + currency +
                       ", amount=" + amount +
                       ", currencyTo=" + currencyTo +
                       ", amountTo=" + amountTo +
                       ", totalFees=" + totalFees +
                       ", status=" + status +
                       ", updatedAt=" + updatedAt +
                       ", notificationMd5='" + notificationMd5 + '\'' +
                       ", fees=" + fees +
                       ", transactions=" + transactions +
                       '}';
        }
}
