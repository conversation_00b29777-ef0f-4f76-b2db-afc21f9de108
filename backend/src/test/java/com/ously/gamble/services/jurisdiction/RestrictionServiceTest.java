package com.ously.gamble.services.jurisdiction;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.CodecType;
import com.ously.gamble.api.jurisdiction.CasinoJurisdictionCountry;
import com.ously.gamble.api.jurisdiction.RestrictionService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
class RestrictionServiceTest extends TestContext {


    @Autowired
    RestrictionService rSrv;

    @Autowired
    CachedMapFactory<String, String> cmf;

    @Test
    void testBasicRestrictionStuff() {

        var cntr_block = cmf.createCachedSet("cntr_block", CodecType.DEFAULT, String.class, 0);
        assertFalse(cntr_block.contains("XX"));
        assertFalse(rSrv.isBlocked("XX"));

        var cjc = rSrv.addCountry(new CasinoJurisdictionCountry("XX", "takatuka", true, false));
        assertTrue(cjc.isPresent());
        assertTrue(cntr_block.contains("XX"));


        assertTrue(rSrv.isBlocked("XX"));
        rSrv.updateCountry(new CasinoJurisdictionCountry(cjc.get().code(), cjc.get().name(), false,
                false));
        assertFalse(cntr_block.contains("XX"));
        assertFalse(rSrv.isBlocked("XX"));


    }


}
