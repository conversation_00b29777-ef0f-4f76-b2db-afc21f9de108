package com.ously.gamble.services.social;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.features.LevelInfo;
import com.ously.gamble.config.LevelConfiguration;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.services.features.LevelManagerImpl;
import com.ously.gamble.services.features.LevelManagerServiceImpl;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(SpringExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class LevelManagerImplTest extends TestContext {

    @Autowired
    LevelManagerImpl lm;

    @Autowired
    LevelManagerServiceImpl lsm;


    @Autowired
    LevelConfiguration eConf;


    @Test
    @Order(1)
    @Transactional
    void testLevelling() {

        var w = new Wallet();
        w.setXp(0L);
        w.setLevel(0);
        var result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertFalse(result.getValue0());
        assertEquals(0L, w.getLevel());
        result = lm.applyLevels(w, BigDecimal.valueOf(1000), 1.0);
        assertTrue(result.getValue0());
        assertEquals(4L, w.getLevel());
        assertEquals(0.346, w.getPercnl().doubleValue(), 0.1);

        result = lm.applyLevels(w, BigDecimal.valueOf(2000), 1.0);
        assertEquals(6, result.getValue1().getLevel());
        assertEquals(2, result.getValue1().getAchievements().size());
        assertEquals("S#" + eConf.getScaledLevelUpCoins(80L),
                result.getValue1().getAchievements().get(1).getPriceDef());


    }


    @Test
    @Transactional
    @Order(2)
    void testLevellingWithSpinsInThebeginning() {

        // Change level 0 to spin based
        LevelInfo levelInfo = lsm.getLevelInfo(0);
        levelInfo.setStartXp(0);
        levelInfo.setEndXp(0);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(2);
        lsm.updateLevel(0,levelInfo);

        levelInfo = lsm.getLevelInfo(1);
        levelInfo.setStartXp(0);
        levelInfo.setEndXp(0);
        levelInfo.setStartSpins(2);
        levelInfo.setEndSpins(4);
        lsm.updateLevel(1,levelInfo);




        levelInfo = lsm.getLevelInfo(2);
        levelInfo.setStartXp(0);
        levelInfo.setEndXp(100);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(2,levelInfo);

        levelInfo = lsm.getLevelInfo(3);
        levelInfo.setStartXp(100);
        levelInfo.setEndXp(200);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(3,levelInfo);

        levelInfo = lsm.getLevelInfo(4);
        levelInfo.setStartXp(200);
        levelInfo.setEndXp(500);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(4,levelInfo);

        levelInfo = lsm.getLevelInfo(5);
        levelInfo.setStartXp(500);
        levelInfo.setEndXp(1000);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(5,levelInfo);

        levelInfo = lsm.getLevelInfo(6);
        levelInfo.setStartXp(1000);
        levelInfo.setEndXp(2000);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(6,levelInfo);

        levelInfo = lsm.getLevelInfo(7);
        levelInfo.setStartXp(2000);
        levelInfo.setEndXp(10000);
        levelInfo.setStartSpins(0);
        levelInfo.setEndSpins(0);
        lsm.updateLevel(7,levelInfo);


        // reload rules
        lm.entityChanged(0);
        // assert that its effective

        var lifi= lsm.getLevelInfosFinalInternal();
        assertEquals(0, lifi.get(0).getEndXp());
        assertEquals(2, lifi.get(0).getEndSpins());
        assertEquals(0, lifi.get(1).getEndXp());
        assertEquals(4, lifi.get(1).getEndSpins());


        var w = new Wallet();
        w.setXp(0L);
        w.setLevel(0);
        w.setGamesPlayed(0);
        var result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertFalse(result.getValue0());
        assertEquals(0L, w.getLevel());
        assertEquals(0.5, w.getPercnl().doubleValue(),0.01);
        w.setGamesPlayed(1);

        result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertTrue(result.getValue0());
        assertEquals(1L, w.getLevel());
        assertEquals(0.0, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(2);

        result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertEquals(1L, w.getLevel());
        assertEquals(0.5, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(3);

        result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertTrue(result.getValue0());
        assertEquals(2, result.getValue1().getLevel());
        assertEquals(0.2, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(4);

        result = lm.applyLevels(w, BigDecimal.valueOf(10), 1.0);
        assertEquals(2L, w.getLevel());
        assertEquals(0.3, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(5);

        result = lm.applyLevels(w, BigDecimal.valueOf(90), 1.0);
        assertEquals(3L, result.getValue1().getLevel());
        assertEquals(3L, w.getLevel());
        assertEquals(0.2, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(6);
        result = lm.applyLevels(w, BigDecimal.valueOf(50), 1.0);
        assertEquals(3L, w.getLevel());
        assertEquals(0.7, w.getPercnl().doubleValue(), 0.01);
        w.setGamesPlayed(7);

        result = lm.applyLevels(w, BigDecimal.valueOf(400), 1.0);
        assertEquals(5L, w.getLevel());
        assertEquals(0.14, w.getPercnl().doubleValue(), 0.01);
        assertEquals(2, result.getValue1().getAchievements().size());
        w.setGamesPlayed(7);


        // now jump 2 levels


    }


}