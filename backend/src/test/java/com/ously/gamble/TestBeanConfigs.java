package com.ously.gamble;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.features.LevelManager;
import com.ously.gamble.api.rankings.RankingSchedulerService;
import com.ously.gamble.api.rankings.TournamentCreationService;
import com.ously.gamble.api.session.JurisdictionHandler;
import com.ously.gamble.api.session.NewTransactionService;
import com.ously.gamble.api.session.TransactionService;
import com.ously.gamble.persistence.repository.ColdTransactionRepository;
import com.ously.gamble.persistence.repository.TransactionRepository;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.session.SessionRoundRepository;
import com.ously.gamble.persistence.repository.session.SessionTransactionRepository;
import com.ously.gamble.ranking.persistence.repository.RankingRepository;
import com.ously.gamble.ranking.persistence.repository.RankingScheduleRepository;
import com.ously.gamble.ranking.service.RankingSchedulerServiceImpl;
import com.ously.gamble.ranking.service.TournamentCreationServiceImpl;
import com.ously.gamble.services.common.TransactionServiceImpl;
import com.ously.gamble.services.session.NewTransactionServiceImpl;
import jakarta.persistence.EntityManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class TestBeanConfigs {
    @Bean
    public TransactionService transactionService(WalletRepository walletRepository,
                                                 TransactionRepository txRepository,
                                                 LevelManager lm,
                                                 ColdTransactionRepository coldTxRepo,
                                                 FeatureConfig fCfg,
                                                 List<JurisdictionHandler> handlers) {
        return new TransactionServiceImpl(walletRepository, txRepository, lm,
                coldTxRepo, fCfg, handlers);
    }

    @Bean
    public TournamentCreationService tournamentCreationService(final RankingScheduleRepository rsRep) {
        return new TournamentCreationServiceImpl(rsRep);
    }

    @Bean
    public RankingSchedulerService rankSchedulerService(final RankingScheduleRepository rsRep,
                                                        final RankingRepository rkRep, TournamentCreationService tCreationService) {
        return new RankingSchedulerServiceImpl(rsRep, rkRep, tCreationService, null, null);
    }

    @Bean
    public NewTransactionService newTransactionService(WalletRepository walletRepository,
                                                       SessionTransactionRepository txRepository,
                                                       LevelManager lm,
                                                       SessionRoundRepository srRepo,
                                                       List<JurisdictionHandler> handlers,
                                                       EntityManager em,
                                                       CachedMap<Long, String> openRoundsCachedMap,
                                                       FeatureConfig fCfg) {
        return new NewTransactionServiceImpl(walletRepository, txRepository, lm,
                srRepo, handlers, em, openRoundsCachedMap, fCfg);
    }

}
