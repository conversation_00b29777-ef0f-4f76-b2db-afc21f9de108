package com.ously.gamble.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.TestContext;
import com.ously.gamble.api.events.UserEventBean;
import com.ously.gamble.persistence.model.user.UserEventType;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@TestMethodOrder(OrderAnnotation.class)
class AMQPRabbitTest extends TestContext {

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    AmqpAdmin admin;


    @Test
    @Order(1)
    void testSending() {
        var exchange = ExchangeBuilder.directExchange("spinarena").build();
        var queue = QueueBuilder.durable("testing").build();
        var binding = BindingBuilder.bind(queue).to(exchange).with("testing").noargs();
        admin.declareExchange(exchange);
        admin.declareQueue(queue);
        admin.declareBinding(binding);

        var ueb = new UserEventBean();
        ueb.setType(UserEventType.REFRESH);
        ueb.setUserId(1L);
        ueb.setRemAdr("nix");
        assertNotNull(ueb);
        // now send msg
        rabbitTemplate.convertAndSend("testing", ueb);
    }

    @Autowired
    ObjectMapper om;

    @Test
    @Order(3)
    void testQueue() throws Exception {
        var testing = rabbitTemplate.receive("testing", 1000L);
        assertNotNull(testing);
        var userEventBean = om.readValue(testing.getBody(), UserEventBean.class);
        assertEquals("nix", userEventBean.getRemAdr());
        assertEquals(1L, userEventBean.getUserId());
    }

}