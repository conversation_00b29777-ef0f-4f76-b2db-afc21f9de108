//package com.ously.gamble.apitesting;
//
//import com.ously.gamble.payload.*;
//import io.restassured.RestAssured;
//import org.junit.Ignore;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.springframework.boot.web.server.LocalServerPort;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//
//import static io.restassured.RestAssured.with;
//import static org.hamcrest.Matchers.equalTo;
//import static org.hamcrest.Matchers.greaterThan;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.fail;
//
//@ExtendWith(SpringExtension.class)
//@Transactional
//@Ignore
//public class UserSignupLoginTest extends RAAbstractTest {
//
//    @LocalServerPort
//    int randomServerPort;
//
//    @BeforeEach
//    public void setup() {
//        RestAssured.port = randomServerPort;
//    }
//
//    @AfterEach
//    public void reap() {
//        tokens.clear();
//    }
//
//    /**
//     * A new user signs up after checking that email and username are free. Then he loads all games and his stats (poll)
//     */
////    @Test
//    public void signUpAndNormalUserBehaviour1() {
//        with().queryParam("email", "<EMAIL>").
//                when().
//                request("GET", "/api/user/checkEmailAvailability").
//                then().statusCode(200).
//                assertThat().body("available", equalTo(true));
//
//        with().queryParam("email", "<EMAIL>").
//                when().
//                request("GET", "/api/user/checkEmailAvailability").
//                then().statusCode(200).
//                assertThat().body("available", equalTo(false));
//
//        with().queryParam("username", "jkleemann2").
//                when().
//                request("GET", "/api/user/checkUsernameAvailability").
//                then().statusCode(200).
//                assertThat().body("available", equalTo(true));
//
//        with().queryParam("username", "jkleemann").
//                when().
//                request("GET", "/api/user/checkUsernameAvailability").
//                then().statusCode(200).
//                assertThat().body("available", equalTo(false));
//
//        // Now prepare signup
//        // leave out name to check minimum parameters
//        SignUpRequest sur = createSignupRequest("jkleemann2", "<EMAIL>");
//        withBody(sur).
//                when().
//                request("POST", "/api/auth/signup").
//                then().statusCode(400);
//        sur = createSignupRequest("jkleemann2", "<EMAIL>");
//        withBody(sur).
//                when().
//                request("POST", "/api/auth/signup").
//                then().statusCode(201).
//                assertThat().body("displayName", equalTo("jkleemann2"));
//
//
//        // now login the new user
//        login("jkleemann2", "secretsecret");
//        // and request the profile
//        withUser("jkleemann2").when().request("GET", "/api/user/me").
//                then().statusCode(200).assertThat().body("id", greaterThan(0));
//
//        // all games, i expect game #1
//        withUser("jkleemann2").when().request("GET", "/api/games/all").
//                then().statusCode(200).assertThat().body("id[0]", greaterThan(0));
//
//        // Poll my data!!! and check i am fresh
//        withUser("jkleemann2").when().request("GET", "/api/stats").then()
//                .statusCode(200).assertThat()
//                .body("xp", equalTo(0))
//                .body("level", equalTo(0));
//
//        // user loads and changes its profile
//        UserSummary up = withUser("jkleemann2").when().request("GET", "/api/user/me").then()
//                .statusCode(200).extract().body().as(UserSummary.class);
//        up.setDisplayName("dpname63457");
//        withBodyAndUser(up, "jkleemann2").when().request("PUT", "/api/user/me").then().statusCode(200)
//                .body("displayName", equalTo("dpname63457"));
//
//
//        // user tries to call an admin-only endpoint
//        withUser("jkleemann2").when().request("GET", "/api/admin/users/{id}", 1).then()
//                .statusCode(403);
//
//
//        // an admin user should not have a problem with admin msgs
//        login("jkleemann", "secret");
//        withUser("jkleemann").when().request("GET", "/api/admin/users/{id}", 1).then()
//                .statusCode(200);
//
//
//        // get stat
//        withUser("jkleemann2").when().request("GET", "/api/stats").then()
//                .statusCode(200).body("level", equalTo(0));
//        // add money
//        DepositRequest depReq = new DepositRequest();
//        depReq.setAmount(new BigDecimal(10000.0));
//        depReq.setPaid(BigDecimal.ZERO);
//        // Deposit some spinz on the user, as an excuse for the block
//        withBodyAndUser(depReq, "jkleemann").when().request("POST", "/api/deposits/{id}", up.getId()).then().statusCode(200);
//        UserStats ustats = withUser("jkleemann2").when().request("GET", "/api/stats").then()
//                .statusCode(200).extract().body().as(UserStats.class);
//        assertEquals(20000, ustats.getBalance().longValue());
//    }
//
//
//    public UserSummary createUser(String username, String email, String pw, String name) {
//        SignUpRequest sur = createSignupRequest(username, email);
//        return withBody(sur).
//                when().
//                request("POST", "/api/auth/signup").
//                then().statusCode(201).extract().body().as(UserSummary.class);
//    }
//
//    public SignUpRequest createSignupRequest(String displayName, String email) {
//        SignUpRequest sur = new SignUpRequest();
//        sur.setDisplayName(displayName);
//        sur.setEmail(email);
//        return sur;
//    }
//
//    /**
//     * An admin logs in, gets list (page) of games and loads a game, changes it and saves it back again.
//     */
//    //  @Test
//    public void signinAndDoAdminStuffWithGamesAndBlockBan() {
//
//        // now login to new user
//        login("jkleemann", "secret");
//
//        // now get all Games
//        withUser("jkleemann").when().request("GET", "/api/admin/games").then()
//                .statusCode(200).body("totalElements", greaterThan(1));
//
//        // Get dedicated game, change name, save and double check name change
//        CasinoGame as = withUser("jkleemann").when().request("GET", "/api/admin/games/{id}", 1l).then().statusCode(200).extract().body().as(CasinoGame.class);
//        as.setName("RenamedName");
//        CasinoGame as1 = withBodyAndUser(as, "jkleemann").when().request("PUT", "/api/admin/games/{id}", 1).then().statusCode(200).extract().body().as(CasinoGame.class);
//        assertEquals(as.getName(), as1.getName());
//        withUser("jkleemann").when().request("GET", "/api/admin/games/{id}", 1l).then().statusCode(200).
//                body("name", equalTo("RenamedName"));
//
//        //
//        // now the admin blocks a user, bans a user and sets it back to normal again
//        //
//        UserSummary usr = createUser("jkleemann3", "<EMAIL>", "secretsecret", "anotherNormalUser3");
//
//        // ban the user:
//        withUser("jkleemann").when().request("GET", "/api/admin/users/{id}/block", usr.getId()).then().statusCode(200);
//
//        try {
//            login("jkleemann3", "secretsecret");
//            fail("user should be blocked and not be able to login");
//        } catch (Exception e) {
//            //
//        }
//
//        // check ban event: (1)
//        withUser("jkleemann").when().request("GET", "/api/admin/users/{id}/events", usr.getId()).then().statusCode(200).body("totalElements", equalTo(1));
//
//        // rehab the user:
//        withUser("jkleemann").when().request("GET", "/api/admin/users/{id}/rehab", usr.getId()).then().statusCode(200);
//        // should work now
//        login("jkleemann3", "secretsecret");
//        UserSummary up = withUser("jkleemann3").when().request("GET", "/api/user/me").then()
//                .statusCode(200).extract().body().as(UserSummary.class);
//
//        // now we have an "rehab" event as well and a login!!
//        withUser("jkleemann").when().request("GET", "/api/admin/users/{id}/events", usr.getId()).then().statusCode(200).body("totalElements", equalTo(3));
//
//        DepositRequest depReq = new DepositRequest();
//        depReq.setAmount(new BigDecimal(10000.0));
//        depReq.setPaid(BigDecimal.ZERO);
//        // Deposit some spinz on the user, as an excuse for the block
//        withBodyAndUser(depReq, "jkleemann").when().request("POST", "/api/deposits/{id}", usr.getId()).then().statusCode(200);
//
//        UserStats uStats = withUser("jkleemann3").when().request("GET", "/api/stats").then()
//                .statusCode(200).extract().body().as(UserStats.class);
//        assertEquals(20000, uStats.getBalance().longValue());
//    }
//
//
//}
