//package com.ously.gamble.apitesting;
//
//import com.ously.gamble.TestContext;
//import com.ously.gamble.api.auth.JwtAuthenticationResponse;
//import com.ously.gamble.api.user.LoginRequest;
//import io.restassured.response.Response;
//import io.restassured.specification.RequestSpecification;
//import org.javatuples.Pair;
//
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//import static io.restassured.RestAssured.with;
//
//public class RAAbstractTest extends TestContext {
//    Map<String, Pair<String, String>> tokens = new ConcurrentHashMap<>();
//
//    void login(String username, String password) {
//        LoginRequest logn = new LoginRequest();
//        logn.setUsernameOrEmail(username);
//        logn.setPassword(password);
//        Response liResp = withBodyAndUser(logn, null).when().request("POST", "/api/auth/signin").thenReturn();
//        try {
//            JwtAuthenticationResponse as = liResp.getBody().as(JwtAuthenticationResponse.class);
//            tokens.put(username, new Pair<String, String>(as.getAccessToken(), as.getRefreshToken()));
//        } catch (Exception e) {
//            throw new IllegalStateException("User not active", e);
//        }
//    }
//
//    RequestSpecification withUser(String user) {
//        return withBodyAndUser(null, user);
//    }
//
//    RequestSpecification withBody(Object body) {
//        return withBodyAndUser(body, null);
//    }
//
//    RequestSpecification withCT() {
//        return withBodyAndUser(null, null);
//    }
//
//    RequestSpecification withBodyAndUser(Object body, String user) {
//        RequestSpecification rs = with().
//                contentType("application/json");
//        if (body != null) {
//            rs = rs.body(body);
//        }
//        if (user != null) {
//            String token = tokens.get(user).getValue0();
//            rs = rs.header("Authorization", "Bearer " + token);
//        }
//        return rs;
//    }
//}
